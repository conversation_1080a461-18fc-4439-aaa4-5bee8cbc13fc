<?php

namespace App\Contracts;

interface AuditableInterface
{
    /**
     * Get the attributes that should be audited
     *
     * @return array
     */
    public function getAuditableAttributes(): array;

    /**
     * Get the attributes that should be excluded from auditing
     *
     * @return array
     */
    public function getAuditExclude(): array;

    /**
     * Get custom audit events for this model
     *
     * @return array
     */
    public function getAuditEvents(): array;

    /**
     * Transform audit data before saving
     *
     * @param array $data
     * @return array
     */
    public function transformAuditData(array $data): array;

    /**
     * Get the identifier for audit logs
     *
     * @return string
     */
    public function getAuditIdentifier(): string;
}
