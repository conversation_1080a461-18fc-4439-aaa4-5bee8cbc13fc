@extends('layouts.master')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('quran.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('audits.index') }}">Audit Trails</a></li>
                    <li class="breadcrumb-item active">Audit Details</li>
                </ol>
            </div>
            <h4 class="page-title">Audit Details</h4>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5 class="card-title">Basic Information</h5>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Date/Time:</strong></td>
                                <td>{{ $audit->created_at->format('Y-m-d H:i:s') }}</td>
                            </tr>
                            <tr>
                                <td><strong>User:</strong></td>
                                <td>
                                    @if($audit->user)
                                        <span class="badge badge-soft-primary">{{ $audit->user->name }}</span>
                                    @else
                                        <span class="badge badge-soft-secondary">System</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Event:</strong></td>
                                <td>
                                    <span class="badge badge-soft-{{ $audit->event == 'created' ? 'success' : ($audit->event == 'updated' ? 'warning' : 'danger') }}">
                                        {{ $audit->formatted_event }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Model:</strong></td>
                                <td>{{ $audit->model_name }} #{{ $audit->auditable_id }}</td>
                            </tr>
                            <tr>
                                <td><strong>IP Address:</strong></td>
                                <td>{{ $audit->ip_address }}</td>
                            </tr>
                            <tr>
                                <td><strong>URL:</strong></td>
                                <td><small>{{ $audit->url }}</small></td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="col-md-6">
                        <h5 class="card-title">User Agent</h5>
                        <p class="text-muted small">{{ $audit->user_agent }}</p>
                        
                        @if($audit->tags)
                            <h5 class="card-title mt-3">Tags</h5>
                            @foreach($audit->tags as $tag)
                                <span class="badge badge-soft-info mr-1">{{ $tag }}</span>
                            @endforeach
                        @endif
                    </div>
                </div>

                @if($audit->changes)
                    <hr>
                    <h5 class="card-title">Changes Made</h5>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Field</th>
                                    <th>Old Value</th>
                                    <th>New Value</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($audit->changes as $field => $change)
                                    <tr>
                                        <td><strong>{{ ucfirst(str_replace('_', ' ', $field)) }}</strong></td>
                                        <td>
                                            @if(is_null($change['old']))
                                                <span class="text-muted">null</span>
                                            @else
                                                <code>{{ is_array($change['old']) ? json_encode($change['old']) : $change['old'] }}</code>
                                            @endif
                                        </td>
                                        <td>
                                            @if(is_null($change['new']))
                                                <span class="text-muted">null</span>
                                            @else
                                                <code>{{ is_array($change['new']) ? json_encode($change['new']) : $change['new'] }}</code>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @endif

                @if($audit->old_values && $audit->event == 'deleted')
                    <hr>
                    <h5 class="card-title">Deleted Data</h5>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Field</th>
                                    <th>Value</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($audit->old_values as $field => $value)
                                    <tr>
                                        <td><strong>{{ ucfirst(str_replace('_', ' ', $field)) }}</strong></td>
                                        <td>
                                            @if(is_null($value))
                                                <span class="text-muted">null</span>
                                            @else
                                                <code>{{ is_array($value) ? json_encode($value) : $value }}</code>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @endif

                @if($audit->new_values && $audit->event == 'created')
                    <hr>
                    <h5 class="card-title">Created Data</h5>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Field</th>
                                    <th>Value</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($audit->new_values as $field => $value)
                                    <tr>
                                        <td><strong>{{ ucfirst(str_replace('_', ' ', $field)) }}</strong></td>
                                        <td>
                                            @if(is_null($value))
                                                <span class="text-muted">null</span>
                                            @else
                                                <code>{{ is_array($value) ? json_encode($value) : $value }}</code>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @endif

                <div class="mt-3">
                    <a href="{{ route('audits.index') }}" class="btn btn-secondary">
                        <i class="mdi mdi-arrow-left"></i> Back to Audit Logs
                    </a>
                    
                    @if($audit->auditable)
                        <a href="#" class="btn btn-info" onclick="alert('Model view not implemented yet')">
                            <i class="mdi mdi-eye"></i> View {{ $audit->model_name }}
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
