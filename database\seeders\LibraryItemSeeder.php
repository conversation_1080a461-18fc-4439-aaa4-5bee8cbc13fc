<?php

namespace Database\Seeders;

use App\Models\LibraryItem;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

class LibraryItemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Make sure the storage directories exist
        Storage::makeDirectory('public/library/thumbnails');
        Storage::makeDirectory('public/library/pdf');
        Storage::makeDirectory('public/library/audio');
        Storage::makeDirectory('public/library/video');
        Storage::makeDirectory('public/library/other');

        // Create sample thumbnails array
        $thumbnails = [
            'pdf' => $this->getSampleImages('pdf', 10),
            'video' => $this->getSampleImages('video', 10),
            'audio' => $this->getSampleImages('audio', 10),
            'other' => $this->getSampleImages('other', 5),
        ];

        // Create 50 library items
        LibraryItem::factory(50)->make()->each(function ($item) use ($thumbnails) {
            // Assign a thumbnail based on media type
            $mediaType = $item->media_type;
            $thumbnailsForType = $thumbnails[$mediaType] ?? $thumbnails['other'];
            $randomThumbnail = $thumbnailsForType[array_rand($thumbnailsForType)];

            // Copy the sample thumbnail to storage
            $thumbnailName = 'thumbnail_' . uniqid() . '.jpg';
            $thumbnailPath = 'library/thumbnails/' . $thumbnailName;

            // Create a copy of the sample image in the storage
            if (file_exists(public_path('samples/thumbnails/' . $randomThumbnail))) {
                Storage::put(
                    'public/' . $thumbnailPath,
                    file_get_contents(public_path('samples/thumbnails/' . $randomThumbnail))
                );
                $item->thumbnail_path = $thumbnailPath;
            }

            // Always create a file path since it's required
            $fileName = 'document_' . uniqid() . '.txt';
            $filePath = 'library/' . $mediaType . '/' . $fileName;

            // Create a simple placeholder file
            $content = "This is a sample {$mediaType} file for testing purposes.\n\n";
            $content .= "Title: {$item->title}\n";
            $content .= "Author: {$item->author}\n\n";
            $content .= "This file was generated by the LibraryItemSeeder.";

            Storage::put('public/' . $filePath, $content);
            $item->file_path = $filePath;

            // For PDFs, sometimes replace with a real PDF file if available
            if ($mediaType === 'pdf' && rand(0, 1) === 1) {
                $samplePdfs = $this->getSampleFiles('pdf');
                if (!empty($samplePdfs)) {
                    $randomPdf = $samplePdfs[array_rand($samplePdfs)];
                    $pdfName = 'document_' . uniqid() . '.pdf';
                    $pdfPath = 'library/pdf/' . $pdfName;

                    if (file_exists(public_path('samples/files/' . $randomPdf))) {
                        Storage::put(
                            'public/' . $pdfPath,
                            file_get_contents(public_path('samples/files/' . $randomPdf))
                        );
                        $item->file_path = $pdfPath;
                    }
                }
            }

            // Save the item
            $item->save();
        });

        $this->command->info('Library items seeded successfully!');
    }

    /**
     * Get sample images for a specific media type
     */
    private function getSampleImages(string $type, int $count): array
    {
        // Create the samples directory if it doesn't exist
        $sampleDir = public_path('samples/thumbnails');
        if (!File::exists($sampleDir)) {
            File::makeDirectory($sampleDir, 0755, true);

            // Create a default image for each type if none exist
            $this->createDefaultImage($sampleDir, 'pdf_default.jpg', '#3498db', 'PDF');
            $this->createDefaultImage($sampleDir, 'video_default.jpg', '#e74c3c', 'VIDEO');
            $this->createDefaultImage($sampleDir, 'audio_default.jpg', '#2ecc71', 'AUDIO');
            $this->createDefaultImage($sampleDir, 'other_default.jpg', '#9b59b6', 'FILE');
        }

        // Get all images that match the type prefix
        $files = File::files($sampleDir);
        $typeImages = [];

        foreach ($files as $file) {
            $filename = $file->getFilename();
            if (strpos($filename, $type) === 0 || $filename === $type . '_default.jpg') {
                $typeImages[] = $filename;
            }
        }

        // If no type-specific images found, use the default
        if (empty($typeImages) && File::exists($sampleDir . '/' . $type . '_default.jpg')) {
            $typeImages[] = $type . '_default.jpg';
        }

        // If still empty, create a default
        if (empty($typeImages)) {
            $defaultImage = $type . '_default.jpg';
            $this->createDefaultImage($sampleDir, $defaultImage, '#3498db', strtoupper($type));
            $typeImages[] = $defaultImage;
        }

        return $typeImages;
    }

    /**
     * Get sample PDF files
     */
    private function getSampleFiles(string $type): array
    {
        // Create the samples directory if it doesn't exist
        $sampleDir = public_path('samples/files');
        if (!File::exists($sampleDir)) {
            File::makeDirectory($sampleDir, 0755, true);

            // Create a sample PDF if none exist
            if ($type === 'pdf') {
                $this->createSamplePdf($sampleDir, 'sample_document.pdf');
            }
        }

        // Get all files that match the type
        $files = File::files($sampleDir);
        $typeFiles = [];

        foreach ($files as $file) {
            $filename = $file->getFilename();
            $extension = pathinfo($filename, PATHINFO_EXTENSION);

            if ($extension === $type) {
                $typeFiles[] = $filename;
            }
        }

        return $typeFiles;
    }

    /**
     * Create a default image with text
     */
    private function createDefaultImage(string $dir, string $filename, string $bgColor, string $text): void
    {
        // Check if GD extension is available
        if (!extension_loaded('gd')) {
            $this->command->warn('GD extension not available. Cannot create default images.');
            return;
        }

        // Create a 400x300 image
        $img = imagecreatetruecolor(400, 300);

        // Convert hex color to RGB
        $hex = ltrim($bgColor, '#');
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));

        // Fill background
        $bgColorResource = imagecolorallocate($img, $r, $g, $b);
        imagefill($img, 0, 0, $bgColorResource);

        // Add text
        $textColor = imagecolorallocate($img, 255, 255, 255);
        $font = 5; // Built-in font

        // Center the text
        $textWidth = strlen($text) * imagefontwidth($font);
        $textHeight = imagefontheight($font);
        $x = (400 - $textWidth) / 2;
        $y = (300 - $textHeight) / 2;

        imagestring($img, $font, $x, $y, $text, $textColor);

        // Save the image
        imagejpeg($img, $dir . '/' . $filename, 90);
        imagedestroy($img);
    }

    /**
     * Create a sample PDF file
     */
    private function createSamplePdf(string $dir, string $filename): void
    {
        // Check if we can use a library like FPDF or TCPDF
        // For simplicity, we'll just create a text file with .pdf extension
        $content = "This is a sample PDF document for testing purposes.\n\n";
        $content .= "Islamic Library System\n";
        $content .= "Sample Document\n\n";
        $content .= "This file was generated by the LibraryItemSeeder.";

        file_put_contents($dir . '/' . $filename, $content);
    }
}
