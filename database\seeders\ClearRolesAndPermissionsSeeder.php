<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Role;
use App\Models\Permission;
use App\Models\UserRole;
use Illuminate\Support\Facades\DB;

class ClearRolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Clearing existing roles and permissions...');

        // Disable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        try {
            // Clear pivot tables first
            DB::table('user_roles')->truncate();
            DB::table('role_permissions')->truncate();

            // Clear main tables
            Role::truncate();
            Permission::truncate();

            $this->command->info('✅ All roles and permissions cleared successfully!');
            $this->command->info('You can now run: php artisan db:seed --class=RolesAndPermissionsSeeder');

        } catch (\Exception $e) {
            $this->command->error('❌ Error clearing data: ' . $e->getMessage());
        } finally {
            // Re-enable foreign key checks
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        }
    }
}
