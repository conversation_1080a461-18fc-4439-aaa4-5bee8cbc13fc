@extends('layouts.master')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('quran.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('user-activities.index') }}">User Activities</a></li>
                    <li class="breadcrumb-item active">Activity Dashboard</li>
                </ol>
            </div>
            <h4 class="page-title">Activity Dashboard</h4>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row">
    <div class="col-md-3">
        <div class="card widget-flat">
            <div class="card-body">
                <div class="float-right">
                    <i class="mdi mdi-account-clock widget-icon bg-success-lighten text-success"></i>
                </div>
                <h5 class="text-muted font-weight-normal mt-0" title="Total Activities">Total Activities</h5>
                <h3 class="mt-3 mb-3">{{ number_format($stats['total']) }}</h3>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card widget-flat">
            <div class="card-body">
                <div class="float-right">
                    <i class="mdi mdi-calendar-today widget-icon bg-info-lighten text-info"></i>
                </div>
                <h5 class="text-muted font-weight-normal mt-0" title="Today">Today</h5>
                <h3 class="mt-3 mb-3">{{ number_format($stats['today']) }}</h3>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card widget-flat">
            <div class="card-body">
                <div class="float-right">
                    <i class="mdi mdi-calendar-week widget-icon bg-warning-lighten text-warning"></i>
                </div>
                <h5 class="text-muted font-weight-normal mt-0" title="This Week">This Week</h5>
                <h3 class="mt-3 mb-3">{{ number_format($stats['this_week']) }}</h3>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card widget-flat">
            <div class="card-body">
                <div class="float-right">
                    <i class="mdi mdi-calendar-month widget-icon bg-primary-lighten text-primary"></i>
                </div>
                <h5 class="text-muted font-weight-normal mt-0" title="This Month">This Month</h5>
                <h3 class="mt-3 mb-3">{{ number_format($stats['this_month']) }}</h3>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Top Activity Types -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title">Top Activity Types</h4>
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>Activity Type</th>
                                <th>Count</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($stats['by_type'] as $stat)
                                <tr>
                                    <td>
                                        <span class="badge badge-soft-primary">
                                            {{ ucfirst(str_replace('_', ' ', $stat->activity_type)) }}
                                        </span>
                                    </td>
                                    <td>{{ number_format($stat->count) }}</td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="2" class="text-center">No data available</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Subject Types -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title">Top Subject Types</h4>
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>Subject Type</th>
                                <th>Count</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($stats['by_subject'] as $stat)
                                <tr>
                                    <td>
                                        <span class="badge badge-soft-info">
                                            {{ class_basename($stat->subject_type) }}
                                        </span>
                                    </td>
                                    <td>{{ number_format($stat->count) }}</td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="2" class="text-center">No data available</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Most Active Users -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title">Most Active Users</h4>
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Activities</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($stats['top_users'] as $stat)
                                <tr>
                                    <td>
                                        @if($stat->user)
                                            <span class="badge badge-soft-primary">{{ $stat->user->name }}</span>
                                        @else
                                            <span class="badge badge-soft-secondary">Unknown User</span>
                                        @endif
                                    </td>
                                    <td>{{ number_format($stat->count) }}</td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="2" class="text-center">No data available</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Logins -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title">Recent Logins</h4>
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Time</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($stats['recent_logins'] as $login)
                                <tr>
                                    <td>
                                        @if($login->user)
                                            <span class="badge badge-soft-success">{{ $login->user->name }}</span>
                                        @else
                                            <span class="badge badge-soft-secondary">Unknown User</span>
                                        @endif
                                    </td>
                                    <td>
                                        <small>{{ $login->created_at->format('M d, H:i') }}</small>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="2" class="text-center">No recent logins</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title">Recent Activities</h4>
                <p class="text-muted font-13 mb-4">
                    Latest 20 user activities across the system.
                </p>

                <div class="table-responsive">
                    <table class="table table-striped table-centered mb-0">
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>User</th>
                                <th>Activity</th>
                                <th>Description</th>
                                <th>IP</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($recentActivities as $activity)
                                <tr>
                                    <td>
                                        <small>{{ $activity->created_at->format('H:i:s') }}</small>
                                        <br>
                                        <small class="text-muted">{{ $activity->created_at->format('M d') }}</small>
                                    </td>
                                    <td>
                                        @if($activity->user)
                                            <span class="badge badge-soft-primary">{{ $activity->user->name }}</span>
                                        @else
                                            <span class="badge badge-soft-secondary">Guest</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge badge-soft-{{ 
                                            $activity->activity_type == 'login' ? 'success' : 
                                            ($activity->activity_type == 'logout' ? 'warning' : 
                                            ($activity->activity_type == 'delete' ? 'danger' : 'info')) 
                                        }}">
                                            {{ $activity->formatted_activity_type }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ Str::limit($activity->description, 40) }}</span>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ $activity->ip_address }}</small>
                                    </td>
                                    <td>
                                        <a href="{{ route('user-activities.show', $activity) }}" class="btn btn-xs btn-outline-primary">
                                            <i class="mdi mdi-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center">No recent activities found.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <div class="mt-3 text-center">
                    <a href="{{ route('user-activities.index') }}" class="btn btn-primary">
                        View All Activities
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
