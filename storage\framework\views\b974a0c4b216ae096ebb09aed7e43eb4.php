<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('quran.dashboard')); ?>">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo e(route('roles.index')); ?>">Roles</a></li>
                    <li class="breadcrumb-item active"><?php echo e($role->display_name); ?></li>
                </ol>
            </div>
            <h4 class="page-title">Role Details</h4>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 class="header-title">
                        <i class="mdi mdi-account-group text-primary"></i> <?php echo e($role->display_name); ?>

                    </h4>
                    <div>
                        <?php if(auth()->user()->hasPermission('roles.update')): ?>
                            <a href="<?php echo e(route('roles.edit', $role)); ?>" class="btn btn-warning">
                                <i class="mdi mdi-pencil"></i> Edit
                            </a>
                        <?php endif; ?>
                        <?php if(auth()->user()->hasPermission('roles.create')): ?>
                            <a href="<?php echo e(route('roles.clone', $role)); ?>" class="btn btn-secondary">
                                <i class="mdi mdi-content-copy"></i> Clone
                            </a>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">Name:</th>
                                <td><code><?php echo e($role->name); ?></code></td>
                            </tr>
                            <tr>
                                <th>Display Name:</th>
                                <td><?php echo $role->color_badge; ?></td>
                            </tr>
                            <tr>
                                <th>Status:</th>
                                <td><?php echo $role->badge; ?></td>
                            </tr>
                            <tr>
                                <th>Type:</th>
                                <td>
                                    <?php if($role->is_system): ?>
                                        <span class="badge badge-danger">System</span>
                                    <?php else: ?>
                                        <span class="badge badge-info">Custom</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">Sort Order:</th>
                                <td><?php echo e($role->sort_order); ?></td>
                            </tr>
                            <tr>
                                <th>Permissions:</th>
                                <td><span class="badge badge-info"><?php echo e($role->permissions->count()); ?></span></td>
                            </tr>
                            <tr>
                                <th>Active Users:</th>
                                <td><span class="badge badge-success"><?php echo e($role->activeUsers->count()); ?></span></td>
                            </tr>
                            <tr>
                                <th>Created:</th>
                                <td><?php echo e($role->created_at->format('M j, Y g:i A')); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>

                <?php if($role->description): ?>
                    <div class="mt-3">
                        <h6>Description:</h6>
                        <p class="text-muted"><?php echo e($role->description); ?></p>
                    </div>
                <?php endif; ?>

                <!-- Permissions by Module -->
                <div class="mt-4">
                    <h5>
                        <i class="mdi mdi-key"></i> Permissions
                        <span class="badge badge-primary ms-2"><?php echo e($role->permissions->count()); ?></span>
                    </h5>

                    <?php if($permissionsByModule->count() > 0): ?>
                        <div class="row">
                            <?php $__currentLoopData = $permissionsByModule; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $module => $permissions): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-md-6 mb-3">
                                    <div class="card border">
                                        <div class="card-header bg-light py-2">
                                            <h6 class="mb-0">
                                                <span class="badge badge-secondary"><?php echo e(ucfirst($module)); ?></span>
                                                <small class="text-muted">(<?php echo e($permissions->count()); ?> permissions)</small>
                                            </h6>
                                        </div>
                                        <div class="card-body py-2">
                                            <div class="row">
                                                <?php $__currentLoopData = $permissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="col-6">
                                                        <small>
                                                            <?php echo $permission->action_badge; ?>

                                                            <?php if(!$permission->is_active): ?>
                                                                <i class="mdi mdi-pause-circle text-muted" title="Inactive"></i>
                                                            <?php endif; ?>
                                                        </small>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="mdi mdi-key-outline h2 text-muted"></i>
                            <p class="text-muted">No permissions assigned to this role</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Users with this role -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="mdi mdi-account-multiple"></i> Users with Role
                    <span class="badge badge-primary ms-2"><?php echo e($role->activeUsers->count()); ?></span>
                </h5>

                <?php if($role->activeUsers->count() > 0): ?>
                    <div class="list-group list-group-flush">
                        <?php $__currentLoopData = $role->activeUsers->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="list-group-item d-flex align-items-center px-0">
                                <img src="<?php echo e($user->avatar_url); ?>" alt="<?php echo e($user->name); ?>" 
                                     class="rounded-circle me-2" width="32" height="32">
                                <div class="flex-grow-1">
                                    <div class="fw-bold"><?php echo e($user->full_name); ?></div>
                                    <small class="text-muted"><?php echo e($user->email); ?></small>
                                    <?php if($user->pivot->expires_at): ?>
                                        <br><small class="text-warning">
                                            <i class="mdi mdi-clock-outline"></i>
                                            Expires: <?php echo e($user->pivot->expires_at->format('M j, Y')); ?>

                                        </small>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <?php echo $user->status_badge; ?>

                                    <?php if(auth()->user()->hasPermission('users.read')): ?>
                                        <a href="<?php echo e(route('users.show', $user)); ?>" class="btn btn-sm btn-outline-primary ms-1">
                                            <i class="mdi mdi-eye"></i>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>

                    <?php if($role->activeUsers->count() > 10): ?>
                        <div class="text-center mt-2">
                            <small class="text-muted">
                                +<?php echo e($role->activeUsers->count() - 10); ?> more users
                            </small>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="text-center py-3">
                        <i class="mdi mdi-account-outline h2 text-muted"></i>
                        <p class="text-muted">No users assigned to this role</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Role Statistics -->
        <div class="card mt-3">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="mdi mdi-chart-pie"></i> Statistics
                </h5>

                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary"><?php echo e($role->permissions->count()); ?></h4>
                            <p class="text-muted mb-0">Permissions</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success"><?php echo e($role->activeUsers->count()); ?></h4>
                        <p class="text-muted mb-0">Active Users</p>
                    </div>
                </div>

                <hr>

                <div class="row text-center">
                    <div class="col-12">
                        <h6 class="text-muted">Permission Coverage</h6>
                        <?php
                            $totalPermissions = \App\Models\Permission::active()->count();
                            $coverage = $totalPermissions > 0 ? round(($role->permissions->count() / $totalPermissions) * 100) : 0;
                        ?>
                        <div class="progress mb-2">
                            <div class="progress-bar" role="progressbar" style="width: <?php echo e($coverage); ?>%">
                                <?php echo e($coverage); ?>%
                            </div>
                        </div>
                        <small class="text-muted"><?php echo e($role->permissions->count()); ?> of <?php echo e($totalPermissions); ?> permissions</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\alquran\resources\views/roles/show.blade.php ENDPATH**/ ?>