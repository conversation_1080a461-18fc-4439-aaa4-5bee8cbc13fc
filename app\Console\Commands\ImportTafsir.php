<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Quran;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\DB;

class ImportTafsir extends Command
{
    protected $signature = 'import:tafsir {file}';
    protected $description = 'Import Tafsir into the database';

    public function handle()
    {
        $filePath = $this->argument('file');

        if (!File::exists($filePath)) {
            $this->error("File not found: $filePath");
            return;
        }

        $this->info('Reading Tafsir JSON file...');
        $tafsirData = json_decode(File::get($filePath), true);

        if (!$tafsirData) {
            $this->error('Invalid JSON format.');
            return;
        }

        $this->info('Saving Tafsir into database...');

        DB::beginTransaction();
        try {
            foreach ($tafsirData as $ayahKey => $ayahInfo) {
                [$surah, $ayah] = explode(':', $ayahKey);
                $tafsirText = $ayahInfo['text'] ?? '';

                // Avoid extremely long queries
                if (strlen($tafsirText) > 65000) {
                    $chunks = str_split($tafsirText, 65000);
                    $tafsirText = implode('', $chunks);
                }

                Quran::where('surah_number', $surah)
                    ->where('ayah_number', $ayah)
                    ->update(['tafsir' => $tafsirText]);
            }

            DB::commit();
            $this->info('Tafsir data imported successfully!');
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Error importing Tafsir: ' . $e->getMessage());
        }
    }
}
