<?php

namespace App\Http\Controllers;

use App\Models\Quran;
use Illuminate\Http\Request;

class QuranController extends Controller
{
    public function index()
    {
        return response()->json(Quran::all());
    }

    public function getSurah($number)
    {
        $surah = Quran::where('surah_number', $number)->get();
        return response()->json($surah);
    }

    public function getAyah($surah, $ayah)
    {
        return response()->json(Quran::where('surah_number', $surah)
            ->where('ayah_number', $ayah)
            ->first());
    }

    // Search Function
    public function search(Request $request)
    {
        $query = $request->input('q');

        if (!$query) {
            return response()->json(['error' => 'Search query is required'], 400);
        }

        // Search in Arabic text and all translation columns
        $results = Quran::where(function ($q) use ($query) {
            $q->where('ayah_text', 'LIKE', "%{$query}%")
                ->orWhere('translation_en', 'LIKE', "%{$query}%")
                ->orWhere('translation_ur', 'LIKE', "%{$query}%")
                ->orWhere('translation_tr', 'LIKE', "%{$query}%")
                ->orWhere('translation_bn', 'LIKE', "%{$query}%")
                ->orWhere('translation_zh', 'LIKE', "%{$query}%")
                ->orWhere('translation_es', 'LIKE', "%{$query}%")
                ->orWhere('translation_fr', 'LIKE', "%{$query}%")
                ->orWhere('translation_id', 'LIKE', "%{$query}%")
                ->orWhere('translation_ru', 'LIKE', "%{$query}%")
                ->orWhere('translation_sv', 'LIKE', "%{$query}%")
                ->orWhere('tafsir', 'LIKE', "%{$query}%");
        })
            ->limit(50) // Limit results for performance
            ->get();

        return response()->json($results);
    }

    public function getTafsir($surah, $ayah)
    {
        $ayahData = Quran::where('surah_number', $surah)
            ->where('ayah_number', $ayah)
            ->first();

        if (!$ayahData || !$ayahData->tafsir) {
            return response()->json(['error' => 'Tafsir not available'], 404);
        }

        return response()->json([
            'surah_number' => $ayahData->surah_number,
            'ayah_number' => $ayahData->ayah_number,
            'ayah_text' => $ayahData->ayah_text,
            'ayah_translation' => $ayahData->ayah_translation,
            'tafsir' => $ayahData->tafsir,
        ]);
    }

    public function getAyahLng($surah, $ayah, $language = 'en')
    {
        $translationColumn = match ($language) {
            'en' => 'translation_en',
            'ur' => 'translation_ur',
            'tr' => 'translation_tr',
            'hi' => 'translation_hi',
            default => 'translation_en',
        };

        $ayahData = Quran::where('surah_number', $surah)
            ->where('ayah_number', $ayah)
            ->first();

        if (!$ayahData) {
            return response()->json(['error' => 'Ayah not found'], 404);
        }

        return response()->json([
            'surah_number' => $ayahData->surah_number,
            'ayah_number' => $ayahData->ayah_number,
            'ayah_text' => $ayahData->ayah_text,
            'translation' => $ayahData->$translationColumn,
        ]);
    }

    public function getTafsirLng($surah, $ayah, $language = 'en')
    {
        $ayahData = Quran::where('surah_number', $surah)
            ->where('ayah_number', $ayah)
            ->first();

        if (!$ayahData) {
            return response()->json(['error' => 'Ayah not found'], 404);
        }

        if (!$ayahData->tafsir) {
            return response()->json(['error' => 'Tafsir not available'], 404);
        }

        $translationColumn = match ($language) {
            'en' => 'translation_en',
            'ur' => 'translation_ur',
            'tr' => 'translation_tr',
            'hi' => 'translation_hi',
            'bn' => 'translation_bn',
            'zh' => 'translation_zh',
            'es' => 'translation_es',
            'fr' => 'translation_fr',
            'id' => 'translation_id',
            'ru' => 'translation_ru',
            'sv' => 'translation_sv',
            default => 'translation_en',
        };

        return response()->json([
            'surah_number' => $ayahData->surah_number,
            'ayah_number' => $ayahData->ayah_number,
            'ayah_text' => $ayahData->ayah_text,
            'translation' => $ayahData->$translationColumn,
            'tafsir' => $ayahData->tafsir,
            'language' => $language,
        ]);
    }

    /**
     * Get all translations for a specific ayah
     */
    public function getTranslations($surah, $ayah)
    {
        $ayahData = Quran::where('surah_number', $surah)
            ->where('ayah_number', $ayah)
            ->first();

        if (!$ayahData) {
            return response()->json(['error' => 'Ayah not found'], 404);
        }

        // Get all available translations
        $translations = [
            'arabic' => $ayahData->ayah_text,
            'en' => $ayahData->translation_en,
            'bn' => $ayahData->translation_bn,
            'zh' => $ayahData->translation_zh,
            'es' => $ayahData->translation_es,
            'fr' => $ayahData->translation_fr,
            'id' => $ayahData->translation_id,
            'ru' => $ayahData->translation_ru,
            'sv' => $ayahData->translation_sv,
            'tr' => $ayahData->translation_tr,
            'ur' => $ayahData->translation_ur,
        ];

        // Filter out null translations
        $translations = array_filter($translations);

        return response()->json([
            'surah_number' => $ayahData->surah_number,
            'ayah_number' => $ayahData->ayah_number,
            'translations' => $translations,
            'tafsir' => $ayahData->tafsir,
        ]);
    }

    /**
     * Get translation in a specific language
     */
    public function getTranslation($surah, $ayah, $language)
    {
        $column = match ($language) {
            'bn' => 'translation_bn',
            'zh' => 'translation_zh',
            'en' => 'translation_en',
            'es' => 'translation_es',
            'fr' => 'translation_fr',
            'id' => 'translation_id',
            'ru' => 'translation_ru',
            'sv' => 'translation_sv',
            'tr' => 'translation_tr',
            'ur' => 'translation_ur',
            default => 'translation_en',
        };

        $ayahData = Quran::where('surah_number', $surah)
            ->where('ayah_number', $ayah)
            ->first();

        if (!$ayahData) {
            return response()->json(['error' => 'Ayah not found'], 404);
        }

        if (!$ayahData->$column) {
            return response()->json(['error' => "Translation in $language not available"], 404);
        }

        return response()->json([
            'surah_number' => $ayahData->surah_number,
            'ayah_number' => $ayahData->ayah_number,
            'arabic' => $ayahData->ayah_text,
            'translation' => $ayahData->$column,
            'language' => $language,
        ]);
    }

    /**
     * List all available languages for translations
     */
    public function availableLanguages()
    {
        $languages = [
            'bn' => 'Bengali',
            'zh' => 'Chinese',
            'en' => 'English',
            'es' => 'Spanish',
            'fr' => 'French',
            'id' => 'Indonesian',
            'ru' => 'Russian',
            'sv' => 'Swedish',
            'tr' => 'Turkish',
            'ur' => 'Urdu',
        ];

        // Check which languages actually have translations
        $sample = Quran::where('surah_number', 1)
            ->where('ayah_number', 1)
            ->first();

        $available = [];
        if ($sample) {
            foreach ($languages as $code => $name) {
                $column = "translation_$code";
                if ($sample->$column) {
                    $available[$code] = $name;
                }
            }
        }

        return response()->json([
            'available_languages' => $available,
        ]);
    }
}
