@extends('layouts.master')

@section('content')
    <!-- Row -->

    <div class="row">
        <div class="col-12">
            <div class="card-box table-responsive">
                <a href="{{ route('surahs.create') }}" class="btn btn-primary float-right">Add Surahs</a>
                <h4 class="header-title"><b>Surahs List</b></h4>
                <p class="sub-header">
                    List of Surahs of Quran for Translation.
                </p>

                <table id="datatable-buttons" class="table table-striped table-bordered dt-responsive nowrap"
                    style="border-collapse: collapse; border-spacing: 0; width: 100%;">
                    <thead>
                        <tr>
                            <th class="border-bottom-0">ID</th>
                            <th class="border-bottom-0">Surahs Name</th>
                            <th class="border-bottom-0">Chapter No</th>
                            <th class="border-bottom-0">Status</th>
                            <th class="border-bottom-0">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($surahs as $surah)
                            <tr>
                                <td>{{ $surah->id }}</td>
                                <td>{{ $surah->surahname }}</td>
                                <td>{{ $surah->chapter_id }}</td>
                                <td><span
                                        class="badge badge-{{ $surah->isactive == 1 ? 'primary' : 'danger' }}">{{ $surah->isactive == 1 ? 'Active' : 'Disable' }}</span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('surahs.edit', $surah->id) }}" class="btn btn-sm btn-purple"><i
                                                class="far fa-edit"></i></a>
                                        <form action="{{ route('surahs.destroy', $surah) }}" method="POST"
                                            style="display: inline-block">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit"
                                                onclick="return confirm('Are you sure to delete this record?')"
                                                class="btn btn-danger btn-sm"><i class="fas fa-trash-alt"></i></button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach

                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <!-- End Row -->
@endsection
