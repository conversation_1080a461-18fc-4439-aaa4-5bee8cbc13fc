@extends('layouts.master')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title">Items List</h4>
                
                <table id="basic-datatable" class="table dt-responsive nowrap">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Description</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($items as $item)
                        <tr>
                            <td>{{ $item->id }}</td>
                            <td>{{ $item->name }}</td>
                            <td>{{ $item->description }}</td>
                            <td>
                                <a href="{{ route('items.show', $item->id) }}" class="btn btn-info btn-sm" data-toggle="tooltip" title="View">
                                    <i class="fe-eye"></i>
                                </a>
                                <a href="{{ route('items.edit', $item->id) }}" class="btn btn-primary btn-sm" data-toggle="tooltip" title="Edit">
                                    <i class="fe-edit"></i>
                                </a>
                                <form action="{{ route('items.destroy', $item->id) }}" method="POST" style="display:inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-danger btn-sm delid" data-toggle="tooltip" title="Delete">
                                        <i class="fe-trash-2"></i>
                                    </button>
                                </form>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection