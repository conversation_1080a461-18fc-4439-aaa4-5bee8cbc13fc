<?php

namespace App\Http\Controllers;

use App\Models\Surah;
use App\Models\Chapter;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SurahController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
        $surahs=Surah::all();
        return view('surahs.surahslst',compact('surahs'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
        $chapts=Chapter::all();
        return view('surahs.surahsadd',compact('chapts'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
        $isactive=0;
        if($request->isactive==1)
            $isactive=1;

        $surah=new Surah();
        $surah->surahname=$request->surahname;
        $surah->surahcode=$request->surahcode;
        $surah->chapter_id=$request->chapter_id;
        $surah->isactive=$isactive;
        $surah->created_by=Auth::user()->name ?? "Shahid";
        $surah->created_at=Now();
        $surah->save();

        return redirect()->route('surahs.index')->with('success', 'Surah added successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Surah $surah)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Surah $surah)
    {
        //
        $chapts=Chapter::all();
        return view('surahs.surahsedit',compact('chapts','surah'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Surah $surah)
    {
        //
        $isactive=0;
        if($request->isactive==1)
            $isactive=1;

        $surah=Surah::find($surah->id);
        $surah->surahname=$request->surahname;
        $surah->surahcode=$request->surahcode;
        $surah->chapter_id=$request->chapter_id;
        $surah->isactive=$isactive;
        $surah->created_by=Auth::user()->name ?? "Shahid";
        $surah->created_at=Now();
        $surah->save();

        return redirect()->route('surahs.index')->with('success', 'Surah added successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Surah $surah)
    {
        //
        $surah->delete();
        return redirect()->route('surahs.index')->with('success', 'Surah added successfully!');
    }
}
