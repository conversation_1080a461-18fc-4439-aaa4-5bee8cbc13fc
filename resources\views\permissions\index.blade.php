@extends('layouts.master')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('quran.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Permissions</li>
                    </ol>
                </div>
                <h4 class="page-title">Permission Management</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h4 class="header-title">
                                <i class="mdi mdi-key text-primary"></i> Permissions
                            </h4>
                        </div>
                        <div class="col-md-6 text-end">
                            @if (auth()->user()->hasPermission('permissions.create'))
                                <a href="{{ route('permissions.create') }}" class="btn btn-primary">
                                    <i class="mdi mdi-plus"></i> Create Permission
                                </a>
                                <a href="{{ route('permissions.bulk-create') }}" class="btn btn-info">
                                    <i class="mdi mdi-plus-box-multiple"></i> Bulk Create
                                </a>
                            @endif
                        </div>
                    </div>

                    <!-- DataTable Filters -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <select id="moduleFilter" class="form-control">
                                <option value="">All Modules</option>
                                @foreach ($modules as $module)
                                    <option value="{{ $module }}">{{ ucfirst($module) }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select id="actionFilter" class="form-control">
                                <option value="">All Actions</option>
                                @foreach ($actions as $action)
                                    <option value="{{ $action }}">{{ ucfirst($action) }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select id="statusFilter" class="form-control">
                                <option value="">All Status</option>
                                <option value="Active">Active</option>
                                <option value="Inactive">Inactive</option>
                                <option value="System">System</option>
                                <option value="Custom">Custom</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-secondary" onclick="clearFilters()">
                                <i class="mdi mdi-refresh"></i> Clear Filters
                            </button>
                        </div>
                    </div>

                    <!-- Permissions DataTable -->
                    <div class="table-responsive">
                        <table id="permissionsTable" class="table table-striped table-bordered dt-responsive nowrap"
                            style="width:100%">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Display Name</th>
                                    <th>Module</th>
                                    <th>Action</th>
                                    <th>Roles</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($permissions as $permission)
                                    <tr>
                                        <td>
                                            <code>{{ $permission->name }}</code>
                                        </td>
                                        <td>{{ $permission->display_name }}</td>
                                        <td data-order="{{ $permission->module }}">
                                            {!! $permission->module_badge !!}
                                        </td>
                                        <td data-order="{{ $permission->action }}">
                                            {!! $permission->action_badge !!}
                                        </td>
                                        <td data-order="{{ $permission->roles->count() }}">
                                            @foreach ($permission->roles->take(3) as $role)
                                                <span class="badge badge-secondary me-1">{{ $role->display_name }}</span>
                                            @endforeach
                                            @if ($permission->roles->count() > 3)
                                                <span class="text-muted">+{{ $permission->roles->count() - 3 }} more</span>
                                            @endif
                                        </td>
                                        <td data-order="{{ $permission->is_system ? 'System' : 'Custom' }}">
                                            @if ($permission->is_system)
                                                <span class="badge badge-danger">System</span>
                                            @else
                                                <span class="badge badge-info">Custom</span>
                                            @endif
                                        </td>
                                        <td data-order="{{ $permission->is_active ? 'Active' : 'Inactive' }}">
                                            {!! $permission->badge !!}
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                @if (auth()->user()->hasPermission('permissions.read'))
                                                    <a href="{{ route('permissions.show', $permission) }}"
                                                        class="btn btn-sm btn-info" title="View">
                                                        <i class="mdi mdi-eye"></i>
                                                    </a>
                                                @endif

                                                @if (auth()->user()->hasPermission('permissions.update'))
                                                    <a href="{{ route('permissions.edit', $permission) }}"
                                                        class="btn btn-sm btn-warning" title="Edit">
                                                        <i class="mdi mdi-pencil"></i>
                                                    </a>
                                                @endif

                                                @if (auth()->user()->hasPermission('permissions.update'))
                                                    <button type="button"
                                                        class="btn btn-sm btn-{{ $permission->is_active ? 'secondary' : 'success' }}"
                                                        onclick="toggleStatus({{ $permission->id }})"
                                                        title="{{ $permission->is_active ? 'Deactivate' : 'Activate' }}">
                                                        <i
                                                            class="mdi mdi-{{ $permission->is_active ? 'pause' : 'play' }}"></i>
                                                    </button>
                                                @endif

                                                @if (auth()->user()->hasPermission('permissions.delete') && !$permission->is_system)
                                                    <button type="button" class="btn btn-sm btn-danger"
                                                        onclick="deletePermission({{ $permission->id }})" title="Delete">
                                                        <i class="mdi mdi-delete"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('styles')
    <!-- DataTables CSS -->
    <link href="{{ asset('assets/libs/datatables.net-bs5/css/dataTables.bootstrap5.min.css') }}" rel="stylesheet"
        type="text/css" />
    <link href="{{ asset('assets/libs/datatables.net-responsive-bs5/css/responsive.bootstrap5.min.css') }}"
        rel="stylesheet" type="text/css" />
    <link href="{{ asset('assets/libs/datatables.net-buttons-bs5/css/buttons.bootstrap5.min.css') }}" rel="stylesheet"
        type="text/css" />
@endsection

@section('scripts')
    <!-- DataTables JS -->
    <script src="{{ asset('assets/libs/datatables.net/js/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('assets/libs/datatables.net-bs5/js/dataTables.bootstrap5.min.js') }}"></script>
    <script src="{{ asset('assets/libs/datatables.net-responsive/js/dataTables.responsive.min.js') }}"></script>
    <script src="{{ asset('assets/libs/datatables.net-responsive-bs5/js/responsive.bootstrap5.min.js') }}"></script>
    <script src="{{ asset('assets/libs/datatables.net-buttons/js/dataTables.buttons.min.js') }}"></script>
    <script src="{{ asset('assets/libs/datatables.net-buttons-bs5/js/buttons.bootstrap5.min.js') }}"></script>
    <script src="{{ asset('assets/libs/datatables.net-buttons/js/buttons.html5.min.js') }}"></script>
    <script src="{{ asset('assets/libs/datatables.net-buttons/js/buttons.print.min.js') }}"></script>
    <script src="{{ asset('assets/libs/jszip/jszip.min.js') }}"></script>
    <script src="{{ asset('assets/libs/pdfmake/build/pdfmake.min.js') }}"></script>
    <script src="{{ asset('assets/libs/pdfmake/build/vfs_fonts.js') }}"></script>

    <script>
        $(document).ready(function() {
            // Initialize DataTable
            var table = $('#permissionsTable').DataTable({
                responsive: true,
                pageLength: 25,
                lengthMenu: [
                    [10, 25, 50, 100, -1],
                    [10, 25, 50, 100, "All"]
                ],
                order: [
                    [1, 'asc']
                ], // Sort by Display Name
                dom: 'Bfrtip',
                buttons: [{
                        extend: 'copy',
                        className: 'btn btn-secondary btn-sm'
                    },
                    {
                        extend: 'csv',
                        className: 'btn btn-secondary btn-sm'
                    },
                    {
                        extend: 'excel',
                        className: 'btn btn-secondary btn-sm'
                    },
                    {
                        extend: 'pdf',
                        className: 'btn btn-secondary btn-sm'
                    },
                    {
                        extend: 'print',
                        className: 'btn btn-secondary btn-sm'
                    }
                ],
                columnDefs: [{
                    targets: [7], // Actions column
                    orderable: false,
                    searchable: false
                }],
                language: {
                    search: "Search permissions:",
                    lengthMenu: "Show _MENU_ permissions per page",
                    info: "Showing _START_ to _END_ of _TOTAL_ permissions",
                    infoEmpty: "No permissions found",
                    infoFiltered: "(filtered from _MAX_ total permissions)",
                    emptyTable: "No permissions available",
                    zeroRecords: "No matching permissions found"
                }
            });

            // Custom filters
            $('#moduleFilter').on('change', function() {
                var value = this.value;
                table.column(2).search(value).draw();
            });

            $('#actionFilter').on('change', function() {
                var value = this.value;
                table.column(3).search(value).draw();
            });

            $('#statusFilter').on('change', function() {
                var value = this.value;
                table.column(6).search(value).draw();
            });

            // Clear filters function
            window.clearFilters = function() {
                $('#moduleFilter').val('');
                $('#actionFilter').val('');
                $('#statusFilter').val('');
                table.search('').columns().search('').draw();
            };
        });

        function toggleStatus(permissionId) {
            if (confirm('Are you sure you want to change the status of this permission?')) {
                fetch(`/permissions/${permissionId}/toggle-status`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            location.reload();
                        } else {
                            alert(data.error || 'An error occurred');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred while updating the permission status');
                    });
            }
        }

        function deletePermission(permissionId) {
            if (confirm('Are you sure you want to delete this permission? This action cannot be undone.')) {
                // Create a form and submit it
                var form = document.createElement('form');
                form.method = 'POST';
                form.action = `/permissions/${permissionId}`;

                var csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                var methodField = document.createElement('input');
                methodField.type = 'hidden';
                methodField.name = '_method';
                methodField.value = 'DELETE';

                form.appendChild(csrfToken);
                form.appendChild(methodField);
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
@endsection
