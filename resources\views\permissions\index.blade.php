@extends('layouts.master')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('quran.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item active">Permissions</li>
                </ol>
            </div>
            <h4 class="page-title">Permission Management</h4>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <h4 class="header-title">
                            <i class="mdi mdi-key text-primary"></i> Permissions
                        </h4>
                    </div>
                    <div class="col-md-6 text-end">
                        @if(auth()->user()->hasPermission('permissions.create'))
                            <a href="{{ route('permissions.create') }}" class="btn btn-primary">
                                <i class="mdi mdi-plus"></i> Create Permission
                            </a>
                            <a href="{{ route('permissions.bulk-create') }}" class="btn btn-info">
                                <i class="mdi mdi-plus-box-multiple"></i> Bulk Create
                            </a>
                        @endif
                    </div>
                </div>

                <!-- Filters -->
                <div class="row mb-3">
                    <div class="col-12">
                        <form method="GET" action="{{ route('permissions.index') }}" class="row g-3">
                            <div class="col-md-3">
                                <input type="text" name="search" class="form-control" placeholder="Search permissions..." value="{{ request('search') }}">
                            </div>
                            <div class="col-md-2">
                                <select name="module" class="form-control">
                                    <option value="">All Modules</option>
                                    @foreach($modules as $module)
                                        <option value="{{ $module }}" {{ request('module') == $module ? 'selected' : '' }}>
                                            {{ ucfirst($module) }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select name="action" class="form-control">
                                    <option value="">All Actions</option>
                                    @foreach($actions as $action)
                                        <option value="{{ $action }}" {{ request('action') == $action ? 'selected' : '' }}>
                                            {{ ucfirst($action) }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select name="status" class="form-control">
                                    <option value="">All Status</option>
                                    <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                                    <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                    <option value="system" {{ request('status') == 'system' ? 'selected' : '' }}>System</option>
                                    <option value="custom" {{ request('status') == 'custom' ? 'selected' : '' }}>Custom</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-secondary">
                                    <i class="mdi mdi-filter"></i> Filter
                                </button>
                                <a href="{{ route('permissions.index') }}" class="btn btn-light">
                                    <i class="mdi mdi-refresh"></i> Reset
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Permissions Table -->
                <div class="table-responsive">
                    <table class="table table-striped table-bordered">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Display Name</th>
                                <th>Module</th>
                                <th>Action</th>
                                <th>Roles</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($permissions as $permission)
                                <tr>
                                    <td>
                                        <code>{{ $permission->name }}</code>
                                        @if($permission->is_system)
                                            <span class="badge badge-danger ms-1">System</span>
                                        @endif
                                    </td>
                                    <td>{{ $permission->display_name }}</td>
                                    <td>{!! $permission->module_badge !!}</td>
                                    <td>{!! $permission->action_badge !!}</td>
                                    <td>
                                        @foreach($permission->roles->take(3) as $role)
                                            <span class="badge badge-secondary me-1">{{ $role->display_name }}</span>
                                        @endforeach
                                        @if($permission->roles->count() > 3)
                                            <span class="text-muted">+{{ $permission->roles->count() - 3 }} more</span>
                                        @endif
                                    </td>
                                    <td>{!! $permission->badge !!}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            @if(auth()->user()->hasPermission('permissions.read'))
                                                <a href="{{ route('permissions.show', $permission) }}" class="btn btn-sm btn-info">
                                                    <i class="mdi mdi-eye"></i>
                                                </a>
                                            @endif
                                            
                                            @if(auth()->user()->hasPermission('permissions.update'))
                                                <a href="{{ route('permissions.edit', $permission) }}" class="btn btn-sm btn-warning">
                                                    <i class="mdi mdi-pencil"></i>
                                                </a>
                                            @endif

                                            @if(auth()->user()->hasPermission('permissions.update'))
                                                <button type="button" class="btn btn-sm btn-{{ $permission->is_active ? 'secondary' : 'success' }}" 
                                                        onclick="toggleStatus({{ $permission->id }})">
                                                    <i class="mdi mdi-{{ $permission->is_active ? 'pause' : 'play' }}"></i>
                                                </button>
                                            @endif

                                            @if(auth()->user()->hasPermission('permissions.delete') && !$permission->is_system)
                                                <form method="POST" action="{{ route('permissions.destroy', $permission) }}" style="display: inline;">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger" 
                                                            onclick="return confirm('Are you sure you want to delete this permission?')">
                                                        <i class="mdi mdi-delete"></i>
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <i class="mdi mdi-key-outline h1 text-muted"></i>
                                        <h4 class="text-muted">No permissions found</h4>
                                        <p class="text-muted">No permissions match your current filters.</p>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <p class="text-muted">
                            Showing {{ $permissions->firstItem() ?? 0 }} to {{ $permissions->lastItem() ?? 0 }} 
                            of {{ $permissions->total() }} permissions
                        </p>
                    </div>
                    <div>
                        {{ $permissions->appends(request()->query())->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function toggleStatus(permissionId) {
    if (confirm('Are you sure you want to change the status of this permission?')) {
        fetch(`/permissions/${permissionId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.error || 'An error occurred');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the permission status');
        });
    }
}
</script>
@endsection
