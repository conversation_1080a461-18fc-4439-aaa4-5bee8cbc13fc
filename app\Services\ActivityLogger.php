<?php

namespace App\Services;

use App\Models\UserActivity;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;

class ActivityLogger
{
    /**
     * Log a user activity
     */
    public static function log(
        string $activityType,
        string $description,
        $subject = null,
        array $properties = [],
        $user = null
    ): UserActivity {
        $user = $user ?? Auth::user();

        // Handle subject ID safely
        $subjectId = null;
        if ($subject) {
            if (method_exists($subject, 'getKey')) {
                $id = $subject->getKey();
                $subjectId = is_numeric($id) ? (int)$id : null;
            } elseif (is_object($subject) && isset($subject->id)) {
                $id = $subject->id;
                $subjectId = is_numeric($id) ? (int)$id : null;
            } elseif (is_array($subject) && isset($subject['id'])) {
                $id = $subject['id'];
                $subjectId = is_numeric($id) ? (int)$id : null;
            }
        }

        return UserActivity::create([
            'user_id' => $user ? $user->id : null,
            'activity_type' => $activityType,
            'description' => $description,
            'subject_type' => $subject ? get_class($subject) : null,
            'subject_id' => $subjectId,
            'properties' => $properties,
            'method' => Request::method(),
            'url' => Request::fullUrl(),
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'session_id' => session()->getId(),
        ]);
    }

    /**
     * Log user login
     */
    public static function logLogin($user): UserActivity
    {
        return self::log(
            'login',
            "User {$user->name} logged in",
            $user,
            ['login_time' => now()],
            $user
        );
    }

    /**
     * Log user logout
     */
    public static function logLogout($user): UserActivity
    {
        return self::log(
            'logout',
            "User {$user->name} logged out",
            $user,
            ['logout_time' => now()],
            $user
        );
    }

    /**
     * Log model view
     */
    public static function logView($model, string $description = null): UserActivity
    {
        $modelName = class_basename($model);
        $identifier = method_exists($model, 'getAuditIdentifier')
            ? $model->getAuditIdentifier()
            : $model->getKey();

        $description = $description ?? "Viewed {$modelName}: {$identifier}";

        return self::log('view', $description, $model);
    }

    /**
     * Log model creation
     */
    public static function logCreate($model, string $description = null): UserActivity
    {
        $modelName = class_basename($model);
        $identifier = method_exists($model, 'getAuditIdentifier')
            ? $model->getAuditIdentifier()
            : $model->getKey();

        $description = $description ?? "Created {$modelName}: {$identifier}";

        return self::log('create', $description, $model);
    }

    /**
     * Log model update
     */
    public static function logUpdate($model, string $description = null): UserActivity
    {
        $modelName = class_basename($model);
        $identifier = method_exists($model, 'getAuditIdentifier')
            ? $model->getAuditIdentifier()
            : $model->getKey();

        $description = $description ?? "Updated {$modelName}: {$identifier}";

        return self::log('update', $description, $model);
    }

    /**
     * Log model deletion
     */
    public static function logDelete($model, string $description = null): UserActivity
    {
        $modelName = class_basename($model);
        $identifier = method_exists($model, 'getAuditIdentifier')
            ? $model->getAuditIdentifier()
            : $model->getKey();

        $description = $description ?? "Deleted {$modelName}: {$identifier}";

        return self::log('delete', $description, $model);
    }

    /**
     * Log file download
     */
    public static function logDownload($model, string $filename = null): UserActivity
    {
        $modelName = class_basename($model);

        // Handle identifier safely
        $identifier = 'unknown';
        if (method_exists($model, 'getAuditIdentifier')) {
            $identifier = $model->getAuditIdentifier();
        } elseif (method_exists($model, 'getKey')) {
            $identifier = $model->getKey();
        } elseif (is_object($model) && isset($model->id)) {
            $identifier = $model->id;
        }

        $filename = $filename ?? 'file';
        $description = "Downloaded {$filename} from {$modelName}: {$identifier}";

        return self::log('download', $description, $model, [
            'filename' => $filename,
            'download_time' => now(),
        ]);
    }

    /**
     * Log search activity
     */
    public static function logSearch(string $query, string $type = 'general', array $results = []): UserActivity
    {
        return self::log('search', "Searched for: {$query}", null, [
            'query' => $query,
            'search_type' => $type,
            'results_count' => count($results),
            'search_time' => now(),
        ]);
    }

    /**
     * Log export activity
     */
    public static function logExport(string $type, array $filters = []): UserActivity
    {
        return self::log('export', "Exported {$type} data", null, [
            'export_type' => $type,
            'filters' => $filters,
            'export_time' => now(),
        ]);
    }

    /**
     * Log custom activity
     */
    public static function logCustom(
        string $activityType,
        string $description,
        array $properties = []
    ): UserActivity {
        return self::log($activityType, $description, null, $properties);
    }

    /**
     * Log backup activity
     */
    public static function logBackup(string $backupType, string $filename, array $details = []): UserActivity
    {
        return self::log('backup', "Created {$backupType} backup: {$filename}", null, array_merge([
            'backup_type' => $backupType,
            'filename' => $filename,
            'backup_time' => now(),
        ], $details));
    }

    /**
     * Log restore activity
     */
    public static function logRestore(string $filename, array $details = []): UserActivity
    {
        return self::log('restore', "Restored backup: {$filename}", null, array_merge([
            'filename' => $filename,
            'restore_time' => now(),
        ], $details));
    }
}
