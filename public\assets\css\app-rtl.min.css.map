{"version": 3, "sources": ["custom/fonts/_fonts.scss", "custom/structure/_general.scss", "custom/structure/_left-menu.scss", "app-rtl.css", "../../node_modules/bootstrap/scss/mixins/_breakpoints.scss", "custom/structure/_topbar.scss", "custom/structure/_page-head.scss", "custom/structure/_footer.scss", "custom/structure/_right-sidebar.scss", "custom/components/_helper.scss", "custom/components/_social.scss", "custom/components/_custom-checkbox.scss", "custom/components/_custom-radio.scss", "custom/components/_print.scss", "custom/components/_widgets.scss", "custom/plugins/_waves.scss", "custom/plugins/_slimscroll.scss", "custom/plugins/_toaster.scss", "custom/plugins/_sweetalert.scss", "custom/plugins/_flot.scss", "custom/plugins/_morris.scss", "custom/plugins/_chartjs.scss", "custom/plugins/_sparkline.scss", "custom/plugins/_google-maps.scss", "custom/plugins/_vector-maps.scss", "custom/plugins/_mapeal-maps.scss", "custom/plugins/_calendar.scss", "custom/plugins/_summernote.scss", "custom/plugins/_select2.scss", "custom/plugins/_autocomplete.scss", "custom/plugins/_bootstrap-tagsinput.scss", "custom/plugins/_bootstrap-select.scss", "custom/plugins/_parsley.scss", "custom/plugins/_timepicker.scss", "custom/plugins/_daterange.scss", "custom/plugins/_clockpicker.scss", "custom/plugins/_form-wizard.scss", "custom/plugins/_x-editable.scss", "custom/plugins/_dropzone.scss", "custom/plugins/_datatable.scss", "custom/plugins/_responsive-table.scss", "custom/plugins/_tablesaw.scss", "custom/pages/_components-demo.scss", "custom/pages/_authentication.scss", "custom/pages/_email.scss", "custom/pages/_timeline.scss", "custom/pages/_account-pages.scss", "custom/pages/_pricing.scss", "custom/rtl/_general-rtl.scss", "custom/rtl/_bootstrap-rtl.scss", "../../node_modules/bootstrap/scss/mixins/_border-radius.scss", "custom/rtl/_spacing-rtl.scss", "custom/rtl/_float-rtl.scss", "custom/rtl/_text-rtl.scss", "../../node_modules/bootstrap/scss/mixins/_text-truncate.scss", "../../node_modules/bootstrap/scss/mixins/_text-emphasis.scss", "../../node_modules/bootstrap/scss/mixins/_hover.scss", "../../node_modules/bootstrap/scss/mixins/_text-hide.scss", "custom/rtl/_structure-rtl.scss", "custom/rtl/_plugins-rtl.scss", "custom/rtl/_components-rtl.scss", "custom/rtl/_pages-rtl.scss"], "names": [], "mappings": "AAIA,iHCAA,KACE,SAAA,SACA,WAAA,KAGF,KACE,eAAA,KACA,WAAA,OCLF,WACI,QAAA,EADJ,cAIQ,WAAA,KAJR,cAOQ,QAAA,EAPR,iBASY,MAAA,KATZ,sCAcQ,QAAA,KAdR,0BAiBQ,SAAA,SACA,OAAA,EACA,SAAA,OACA,mCAAA,KAAA,2BAAA,KACA,4BAAA,KAAA,oBAAA,KACA,4BAAA,MAAA,CAAA,WAAA,oBAAA,MAAA,CAAA,WAIR,uBCOA,sBDHY,QAAA,IAAA,KACA,MAAA,QACA,QAAA,MACA,SAAA,SACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IARZ,6BAAA,6BCeE,4BACA,4BDLc,MAAA,QAMhB,iCCGA,gCDCY,MAAA,QAMZ,SACI,OAAA,KACA,SAAA,OACA,MAAA,KAIJ,cACI,YAAA,MACA,SAAA,OACA,QAAA,EAAA,KAAA,IAAA,KACA,WAAA,KACA,WAAA,KAIJ,gBACI,MAAA,MACA,WAAA,KACA,OAAA,EACA,QAAA,KAAA,EACA,SAAA,MACA,mBAAA,IAAA,IAAA,SAAA,WAAA,IAAA,IAAA,SACA,IAAA,KACA,mBAAA,EAAA,EAAA,KAAA,EAAA,sBAAA,WAAA,EAAA,EAAA,KAAA,EAAA,sBACA,QAAA,GAIJ,sBAIgB,MAAA,QACA,QAAA,MACA,QAAA,KAAA,KACA,SAAA,SACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IACA,YAAA,MAAA,CAAA,WACA,UAAA,KAVhB,6BAAA,4BAAA,4BAeoB,MAAA,QACA,gBAAA,KACA,WAAA,QAjBpB,2BAoBoB,eAAA,OApBpB,wBAwBoB,QAAA,aACA,YAAA,UACA,OAAA,EAAA,KAAA,EAAA,IACA,WAAA,OACA,eAAA,OACA,MAAA,KACA,UAAA,KA9BpB,kCAiCoB,MAAA,MAjCpB,oCAmCwB,aAAA,EAnCxB,6BAwCgB,MAAA,QACA,WAAA,QAzChB,uBA6CgB,aAAA,KA7ChB,0BAgDoB,aAAA,KAhDpB,0BAsDQ,mBAAA,kBAAA,KAAA,WAAA,kBAAA,KAAA,WAAA,UAAA,KAAA,WAAA,UAAA,IAAA,CAAA,kBAAA,KACA,SAAA,SACA,MAAA,KACA,QAAA,aACA,YAAA,wBACA,eAAA,KACA,YAAA,OACA,UAAA,OACA,kBAAA,eAAA,UAAA,eA9DR,iCAgEY,QAAA,QAhEZ,qBAoEQ,WAAA,IApER,6CA0EgB,kBAAA,cAAA,UAAA,cA1EhB,0BAgFQ,QAAA,KAAA,KACA,eAAA,MACA,eAAA,KACA,OAAA,QACA,UAAA,SACA,eAAA,UACA,MAAA,QACA,YAAA,IAKR,oBAGQ,MAAA,eAHR,6BAQY,QAAA,KARZ,6BAWY,QAAA,MAXZ,0BAgBQ,SAAA,SACA,YAAA,EACA,MAAA,eACA,QAAA,EAnBR,yCCdE,2CDqCU,SAAA,kBACA,OAAA,eAxBZ,yCA2BY,WAAA,OCjCV,+CADA,+CADA,oDDQF,oDAoCgB,QAAA,eApChB,8CAyCoB,SAAA,SACA,YAAA,OA1CpB,gDA6CwB,QAAA,KAAA,KACA,WAAA,KACA,mBAAA,KAAA,WAAA,KA/CxB,uDAAA,sDAAA,sDAoD4B,MAAA,QApD5B,kDAuD4B,UAAA,SACA,aAAA,KAxD5B,qDA4D4B,QAAA,KACA,aAAA,KA7D5B,sDAmE4B,SAAA,SACA,MAAA,mBACA,MAAA,QACA,iBAAA,QACA,mBAAA,KAAA,WAAA,KAvE5B,2DA0EgC,QAAA,OA1EhC,uEAAA,kEAgFgC,QAAA,KAhFhC,uDAqF4B,QAAA,MACA,KAAA,KACA,SAAA,SACA,MAAA,MACA,OAAA,eACA,mBAAA,IAAA,IAAA,KAAA,EAAA,qBAAA,WAAA,IAAA,IAAA,KAAA,EAAA,qBA1F5B,0DA6FgC,mBAAA,IAAA,IAAA,KAAA,EAAA,qBAAA,WAAA,IAAA,IAAA,KAAA,EAAA,qBA7FhC,yDAgGgC,mBAAA,KAAA,WAAA,KACA,QAAA,IAAA,KACA,SAAA,SACA,MAAA,MACA,QAAA,EApGhC,+DAsGoC,MAAA,QAtGpC,8CA8GoB,QAAA,IAAA,EACA,QAAA,KACA,QAAA,KACA,iBAAA,KAjHpB,0DAsHgC,QAAA,MACA,KAAA,MACA,WAAA,MACA,OAAA,eACA,SAAA,SACA,MAAA,MA3HhC,mEAiIgC,SAAA,SACA,MAAA,KACA,IAAA,KACA,kBAAA,eAAA,UAAA,eApIhC,0DA0I4B,MAAA,QA1I5B,wBAoJQ,YAAA,eApJR,kBAyJQ,KAAA,eAzJR,oBA8JQ,QAAA,KAKR,cACI,WAAA,OE/QA,4BFmRA,KACI,WAAA,OACA,eAAA,KAEJ,gBACI,QAAA,KACA,QAAA,cAEJ,gCAEQ,QAAA,MAGR,cAAA,wBACI,YAAA,YACA,QAAA,EAAA,KAEJ,eACI,QAAA,KAEJ,UACI,QAAA,MG5WR,MACI,QAAA,MACA,YAAA,KAFJ,mBAIQ,QAAA,MAJR,mBAOQ,QAAA,KAPR,yBAUQ,MAAA,QACA,YAAA,IACA,UAAA,KACA,eAAA,UAbR,0BAgBQ,MAAA,KACA,YAAA,IACA,UAAA,KACA,eAAA,UAIR,UACI,OAAA,KACA,MAAA,MACA,MAAA,KAGJ,eACI,WAAA,QACA,mBAAA,EAAA,EAAA,KAAA,EAAA,sBAAA,WAAA,EAAA,EAAA,KAAA,EAAA,sBACA,QAAA,EAAA,KAAA,EAAA,EACA,SAAA,MACA,KAAA,EACA,MAAA,EACA,OAAA,KACA,QAAA,IARJ,+BAYY,MAAA,KAZZ,sCAeY,QAAA,EAAA,KACA,MAAA,qBACA,UAAA,KACA,QAAA,MACA,YAAA,KACA,WAAA,OACA,WAAA,KArBZ,wCA4BY,iBAAA,sBA5BZ,2BAmCQ,SAAA,OACA,OAAA,KACA,QAAA,MACA,UAAA,MACA,aAAA,KAvCR,2CA0CY,QAAA,WACA,eAAA,OA3CZ,4EA8CgB,UAAA,SACA,MAAA,qBA/ChB,yCAmDY,OAAA,KACA,OAAA,KACA,aAAA,KACA,cAAA,EACA,MAAA,KACA,iBAAA,sBACA,mBAAA,KAAA,WAAA,KACA,cAAA,KAAA,EAAA,EAAA,KA1DZ,+CA6DY,YAAA,EACA,QAAA,EA9DZ,gCAkEY,iBAAA,sBACA,MAAA,KACA,aAAA,YACA,cAAA,EAAA,KAAA,KAAA,EACA,mBAAA,eAAA,WAAA,eAtEZ,mCA2EQ,OAAA,KACA,MAAA,KACA,QAAA,aACA,OAAA,KACA,YAAA,KACA,MAAA,KACA,iBAAA,YACA,UAAA,KACA,OAAA,QAnFR,+CAuFQ,QAAA,KAMR,aACI,WAAA,MAGJ,mBACI,YAAA,EADJ,+BAIQ,iBAAA,KACA,QAAA,KAAA,KALR,8BASQ,UAAA,KACA,eAAA,OAVR,oCAcQ,QAAA,aACA,SAAA,SACA,IAAA,KACA,MAAA,KAjBR,gCAqBQ,QAAA,KAAA,KArBR,6CAwBY,MAAA,KACA,OAAA,KACA,MAAA,KACA,UAAA,KACA,YAAA,KACA,WAAA,OACA,WAAA,IACA,aAAA,KACA,cAAA,IACA,MAAA,KAjCZ,gDAqCY,cAAA,IACA,SAAA,OACA,YAAA,KACA,cAAA,SACA,YAAA,OACA,MAAA,QACA,YAAA,IA3CZ,kDA8CgB,YAAA,IA9ChB,sDAiDgB,QAAA,MAjDhB,qDAoDgB,QAAA,MACA,SAAA,OACA,cAAA,SACA,YAAA,OACA,UAAA,KAxDhB,0CA6DY,YAAA,KACA,YAAA,OACA,YAAA,KA/DZ,kDAoEY,QAAA,IAAA,KAKZ,kBACI,MAAA,MADJ,oBAGQ,eAAA,OACA,aAAA,IAIR,UACI,QAAA,EAAA,eADJ,cAGQ,OAAA,KACA,MAAA,KCpNR,gBACI,QAAA,EAAA,KACA,OAAA,EAAA,MAAA,KAAA,MACA,iBAAA,KACA,mBAAA,EAAA,EAAA,KAAA,EAAA,sBAAA,WAAA,EAAA,EAAA,KAAA,EAAA,sBAJJ,4BAMQ,UAAA,KACA,OAAA,EACA,YAAA,KACA,YAAA,IATR,kCAaQ,MAAA,MAbR,4BAiBQ,cAAA,EACA,QAAA,KAAA,EFmDJ,4BE5CA,4BAEQ,QAAA,MACA,YAAA,OACA,cAAA,SACA,SAAA,OALR,4BAQQ,QAAA,MAKZ,yBACI,kCAEQ,QAAA,MAKZ,yBACI,eAAA,4BACI,QAAA,MChDR,QACI,OAAA,EACA,QAAA,KAAA,KAAA,KACA,SAAA,SACA,MAAA,EACA,MAAA,QACA,KAAA,MACA,WAAA,IAAA,MAAA,QACA,WAAA,OH6DA,4BGzDA,QACI,KAAA,YACA,WAAA,QCdR,WACI,iBAAA,KACA,mBAAA,EAAA,EAAA,KAAA,EAAA,eAAA,CAAA,EAAA,IAAA,EAAA,EAAA,gBAAA,WAAA,EAAA,EAAA,KAAA,EAAA,eAAA,CAAA,EAAA,IAAA,EAAA,EAAA,gBACA,QAAA,MACA,SAAA,MACA,mBAAA,IAAA,IAAA,SAAA,WAAA,IAAA,IAAA,SACA,MAAA,MACA,QAAA,KACA,MAAA,gBACA,MAAA,OACA,IAAA,EACA,OAAA,EAXJ,2BAcQ,WAAA,QACA,QAAA,KAAA,KACA,MAAA,KAhBR,6BAmBQ,iBAAA,QACA,OAAA,KACA,MAAA,KACA,YAAA,KACA,MAAA,KACA,WAAA,OACA,cAAA,IACA,WAAA,KA1BR,mCA6BY,iBAAA,QA7BZ,qBAiCQ,QAAA,KACA,WAAA,OAlCR,+BAoCY,SAAA,SACA,OAAA,KACA,MAAA,KACA,OAAA,EAAA,KAAA,KAAA,KAvCZ,0CAyCgB,SAAA,SACA,MAAA,KACA,OAAA,EACA,OAAA,KACA,MAAA,KACA,iBAAA,KACA,YAAA,KACA,cAAA,IACA,mBAAA,EAAA,KAAA,KAAA,gBAAA,WAAA,EAAA,KAAA,KAAA,gBAjDhB,wBAqDY,cAAA,IArDZ,0BAuDgB,MAAA,QAOhB,kBACI,iBAAA,mBACA,SAAA,SACA,KAAA,EACA,MAAA,EACA,IAAA,EACA,OAAA,EACA,QAAA,KACA,QAAA,KACA,mBAAA,IAAA,IAAA,SAAA,WAAA,IAAA,IAAA,SAGJ,8BAEQ,MAAA,EAFR,qCAKQ,QAAA,MJVJ,4BIeA,WACI,SAAA,KADJ,4BAGQ,OAAA,gBAKZ,gCAEQ,SAAA,SACA,YAAA,IAAA,OAAA,QACA,aAAA,KACA,eAAA,IALR,uCAOY,QAAA,GACA,SAAA,SACA,KAAA,KACA,IAAA,IACA,MAAA,KACA,OAAA,KACA,iBAAA,KACA,OAAA,IAAA,MAAA,QACA,cAAA,IAMZ,0BAEQ,SAAA,OACA,QAAA,QAAA,EACA,SAAA,SAJR,0CAOY,QAAA,MACA,MAAA,KACA,aAAA,KACA,WAAA,IAVZ,8CAagB,MAAA,KAbhB,6CAkBY,QAAA,MACA,cAAA,EACA,YAAA,IApBZ,+CAsBgB,MAAA,QAtBhB,2CA2BY,MAAA,QACA,QAAA,MACA,OAAA,EACA,SAAA,OA9BZ,2CAkCY,MAAA,QACA,UAAA,SACA,SAAA,SACA,MAAA,IACA,IAAA,KCrJZ,UACE,UAAA,KAGF,UACE,UAAA,KAGF,UACE,UAAA,MAGF,UACE,UAAA,MAGF,UACE,UAAA,MAKF,uBACE,YAAA,MAAA,CAAA,WAIF,WACE,OAAA,OACA,MAAA,OAGF,WACE,OAAA,QACA,MAAA,QAGF,WACE,OAAA,OACA,MAAA,OAGF,WACE,OAAA,OACA,MAAA,OAGF,WACE,OAAA,KACA,MAAA,KAGF,YACE,OAAA,OACA,MAAA,OAGF,cACE,kBAAA,OAAA,eAAA,OAAA,YAAA,OACA,MAAA,KACA,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,OAAA,KACA,iBAAA,OAAA,cAAA,OAAA,gBAAA,OACA,MAAA,KAGF,cACE,aAAA,KADF,iCAGI,OAAA,EAAA,EAAA,KAAA,MACA,QAAA,aACA,OAAA,IAAA,MAAA,KACA,cAAA,IAOJ,oBACE,YAAA,IAGF,sBACE,YAAA,IAMF,WNikBA,WACA,WACA,WM/jBE,SAAA,OACA,cAAA,SACA,QAAA,YACA,mBAAA,SAGF,WACE,mBAAA,EAGF,WACE,mBAAA,EAIF,WACE,mBAAA,EAGF,WACE,mBAAA,EAKF,SACE,YAAA,QACA,aAAA,QC1HF,kBACI,OAAA,KACA,MAAA,KACA,YAAA,iBACA,QAAA,MACA,OAAA,IAAA,MAAA,QACA,cAAA,IACA,MAAA,QCPJ,gBAEQ,QAAA,aACA,aAAA,IACA,SAAA,SACA,YAAA,IALR,wBAOY,cAAA,IAAA,YACA,mBAAA,IAAA,YACA,iBAAA,KACA,cAAA,IACA,OAAA,IAAA,MAAA,QACA,QAAA,GACA,QAAA,aACA,OAAA,KACA,KAAA,EACA,YAAA,MACA,SAAA,SACA,WAAA,IAAA,YACA,MAAA,KACA,QAAA,YACA,IAAA,IArBZ,uBAwBY,MAAA,QACA,QAAA,aACA,UAAA,KACA,OAAA,KACA,KAAA,EACA,YAAA,MACA,aAAA,IACA,YAAA,IACA,SAAA,SACA,IAAA,EACA,MAAA,KAlCZ,+BAsCQ,OAAA,QACA,QAAA,EACA,QAAA,EACA,QAAA,YAzCR,8CA2CY,QAAA,IA3CZ,mDAgDY,eAAA,KACA,QAAA,EAjDZ,oDAsDY,QAAA,GACA,SAAA,SACA,IAAA,IACA,KAAA,IACA,QAAA,MACA,MAAA,IACA,OAAA,IACA,OAAA,IAAA,MAAA,QACA,iBAAA,EACA,kBAAA,EACA,kBAAA,cAGA,UAAA,cAnEZ,sDAwEY,iBAAA,QACA,OAAA,YAKZ,wCAGY,cAAA,IAKZ,0BACI,WAAA,EAGJ,gCAEQ,OAAA,KACA,MAAA,KACA,SAAA,SAJR,gCAOQ,OAAA,KACA,MAAA,KARR,uCAUY,YAAA,EAVZ,sCAaY,YAAA,EAOR,6DAGY,iBAAA,QACA,aAAA,QAJZ,4DAOY,aAAA,KAPZ,+DAGY,iBAAA,QACA,aAAA,QAJZ,8DAOY,aAAA,KAPZ,6DAGY,iBAAA,QACA,aAAA,QAJZ,4DAOY,aAAA,KAPZ,0DAGY,iBAAA,QACA,aAAA,QAJZ,yDAOY,aAAA,KAPZ,6DAGY,iBAAA,QACA,aAAA,QAJZ,4DAOY,aAAA,KAPZ,4DAGY,iBAAA,QACA,aAAA,QAJZ,2DAOY,aAAA,KAPZ,2DAGY,iBAAA,QACA,aAAA,QAJZ,0DAOY,aAAA,KAPZ,0DAGY,iBAAA,QACA,aAAA,QAJZ,yDAOY,aAAA,KAPZ,0DAGY,iBAAA,QACA,aAAA,QAJZ,yDAOY,aAAA,KAPZ,4DAGY,iBAAA,QACA,aAAA,QAJZ,2DAOY,aAAA,KCtHhB,aAEQ,QAAA,aACA,aAAA,IACA,SAAA,SACA,YAAA,IALR,qBAOY,cAAA,OAAA,IAAA,YACA,mBAAA,OAAA,IAAA,YACA,iBAAA,KACA,cAAA,IACA,OAAA,IAAA,MAAA,QACA,QAAA,GACA,QAAA,aACA,OAAA,KACA,KAAA,EACA,YAAA,MACA,SAAA,SACA,WAAA,OAAA,IAAA,YACA,MAAA,KACA,QAAA,YApBZ,oBAuBY,gBAAA,eAAA,IAAA,8BACA,cAAA,WACA,aAAA,WACA,cAAA,aAAA,IAAA,8BACA,kBAAA,WACA,mBAAA,kBAAA,IAAA,8BACA,iBAAA,QACA,cAAA,IACA,QAAA,IACA,QAAA,aACA,OAAA,KACA,KAAA,IACA,YAAA,MACA,SAAA,SACA,IAAA,IACA,UAAA,WACA,WAAA,kBAAA,IAAA,8BAAA,WAAA,UAAA,IAAA,8BAAA,WAAA,UAAA,IAAA,6BAAA,CAAA,kBAAA,IAAA,8BACA,MAAA,KAxCZ,yBA4CQ,OAAA,QACA,QAAA,EACA,QAAA,EACA,QAAA,YA/CR,wCAiDY,QAAA,IAjDZ,6CAsDY,eAAA,KACA,QAAA,IAAA,KAAA,yBACA,QAAA,KAAA,OAxDZ,8CA+DY,kBAAA,WACA,UAAA,WAhEZ,gDAqEY,OAAA,YAKZ,oBACI,WAAA,EAGJ,0BAEQ,OAAA,KAMJ,8CAGY,iBAAA,QAHZ,uDAQY,aAAA,QARZ,sDAWY,iBAAA,QAXZ,gDAGY,iBAAA,QAHZ,yDAQY,aAAA,QARZ,wDAWY,iBAAA,QAXZ,8CAGY,iBAAA,QAHZ,uDAQY,aAAA,QARZ,sDAWY,iBAAA,QAXZ,2CAGY,iBAAA,QAHZ,oDAQY,aAAA,QARZ,mDAWY,iBAAA,QAXZ,8CAGY,iBAAA,QAHZ,uDAQY,aAAA,QARZ,sDAWY,iBAAA,QAXZ,6CAGY,iBAAA,QAHZ,sDAQY,aAAA,QARZ,qDAWY,iBAAA,QAXZ,4CAGY,iBAAA,QAHZ,qDAQY,aAAA,QARZ,oDAWY,iBAAA,QAXZ,2CAGY,iBAAA,QAHZ,oDAQY,aAAA,QARZ,mDAWY,iBAAA,QAXZ,2CAGY,iBAAA,QAHZ,oDAQY,aAAA,QARZ,mDAWY,iBAAA,QAXZ,6CAGY,iBAAA,QAHZ,sDAQY,aAAA,QARZ,qDAWY,iBAAA,QChGhB,aVy/BE,QUx/BE,gBVu/BF,eADA,gBADA,WUh/BM,QAAA,KAEJ,WVs/BF,SAFA,cACA,WAEA,KUl/BM,QAAA,EACA,OAAA,GCfR,aACE,WAAA,wBACA,gBAAA,MACA,OAAA,IAAA,MAAA,KAHF,eAMI,WAAA,QACA,WAAA,QACA,UAAA,KACA,OAAA,KACA,YAAA,KACA,MAAA,KACA,WAAA,OACA,MAAA,eACA,cAAA,IACA,mBAAA,EAAA,KAAA,KAAA,gBAAA,WAAA,EAAA,KAAA,KAAA,gBAOJ,0BAEI,cAAA,IAAA,MAAA,QACA,SAAA,OACA,QAAA,KAAA,EACA,SAAA,SALJ,0CAOM,QAAA,MACA,MAAA,KACA,aAAA,KACA,MAAA,KAVN,8BAaM,MAAA,KAbN,6CAgBM,MAAA,QACA,QAAA,MACA,OAAA,EAlBN,2CAqBM,MAAA,QACA,QAAA,MACA,UAAA,KACA,OAAA,EAxBN,2CA2BM,MAAA,QACA,UAAA,KACA,SAAA,SACA,MAAA,IACA,IAAA,IAON,gCAEI,SAAA,SAFJ,mDAKM,MAAA,QACA,UAAA,KACA,SAAA,SACA,MAAA,IACA,IAAA,IATN,kDAYM,MAAA,QACA,QAAA,MACA,OAAA,KAAA,EACA,YAAA,IACA,UAAA,KACA,YAAA,KAjBN,mDAoBM,MAAA,QACA,QAAA,MACA,UAAA,KACA,OAAA,EAvBN,kBA2BI,WAAA,KACA,QAAA,MAOJ,qBAEI,QAAA,IAAA,EACA,cAAA,IAAA,MAAA,QACA,MAAA,KACA,SAAA,SALJ,oBAQI,MAAA,KACA,SAAA,SACA,IAAA,KACA,UAAA,KAXJ,6BAcI,aAAA,KACA,YAAA,OACA,QAAA,aACA,SAAA,OACA,cAAA,SACA,MAAA,MAnBJ,8BAsBI,YAAA,KCzHJ;;;;;;AAOC,cACC,SAAA,SACA,OAAA,QACA,QAAA,aACA,SAAA,OACA,oBAAA,KACA,iBAAA,KACA,gBAAA,KACA,YAAA,KACA,4BAAA,YAEF,4BACE,SAAA,SACA,cAAA,IACA,MAAA,MACA,OAAA,MACA,WAAA,MACA,YAAA,MACA,QAAA,EACA,WAAA,eAIA,WAAA,mHACA,mBAAA,IAAA,IAAA,SAGA,WAAA,IAAA,IAAA,SACA,4BAAA,iBAAA,CAAA,QAGA,4BAAA,OAAA,CAAA,kBAAA,oBAAA,OAAA,CAAA,kBAAA,oBAAA,SAAA,CAAA,QAAA,oBAAA,SAAA,CAAA,OAAA,CAAA,kBACA,kBAAA,SAAA,eAIA,UAAA,SAAA,eACA,eAAA,KAEF,wCACE,WAAA,qBAIA,WAAA,2IAEF,0CACE,WAAA,eAEF,sDACE,WAAA,qBAEF,oBACE,mBAAA,eAGA,WAAA,eAEF,cZgmCA,cY9lCE,kBAAA,cAIA,UAAA,cACA,mBAAA,oDAEF,cZ+lCA,oBAFA,oBACA,sBY1lCE,YAAA,OACA,eAAA,OACA,OAAA,QACA,OAAA,KACA,QAAA,EACA,MAAA,QACA,iBAAA,cACA,UAAA,IACA,YAAA,IACA,WAAA,OACA,gBAAA,KACA,QAAA,EAEF,cACE,QAAA,MAAA,MACA,cAAA,KAEF,oBACE,OAAA,EACA,QAAA,MAAA,MAEF,qBACE,cAAA,KACA,eAAA,OAEF,kCACE,QAAA,EAEF,yCACE,SAAA,SACA,IAAA,EACA,KAAA,EACA,QAAA,EAEF,cACE,WAAA,OACA,MAAA,MACA,OAAA,MACA,YAAA,MACA,cAAA,IAEF,aACE,mBAAA,KACA,mBAAA,EAAA,IAAA,MAAA,IAAA,gBACA,WAAA,EAAA,IAAA,MAAA,IAAA,gBACA,mBAAA,IAAA,IAGA,WAAA,IAAA,IAEF,oBACE,mBAAA,EAAA,IAAA,KAAA,IAAA,eACA,WAAA,EAAA,IAAA,KAAA,IAAA,eAEF,aACE,QAAA,MCjIF,eACI,OAAA,eCDJ,iBACI,QAAA,KACA,YAAA,aAAA,CAAA,WACA,iBAAA,QACA,UAAA,KACA,YAAA,KALJ,oBAOQ,YAAA,aAAA,CAAA,WAPR,mBAUQ,UAAA,QAVR,yBAYY,MAAA,KAKZ,aACI,QAAA,KAAA,KAAA,KAAA,KAGJ,uBACI,SAAA,SACA,IAAA,MACA,MAAA,MACA,UAAA,KACA,OAAA,QACA,OAAA,KACA,MAAA,KACA,iBAAA,QACA,cAAA,IACA,WAAA,OACA,YAAA,KAGJ,iBACI,OAAA,IACA,IAAA,EACA,cAAA,EAIA,iBACI,iBAAA,QACA,MAAA,KACA,aAAA,QAHJ,mBACI,iBAAA,QACA,MAAA,KACA,aAAA,QAHJ,iBACI,iBAAA,QACA,MAAA,KACA,aAAA,QAHJ,cACI,iBAAA,QACA,MAAA,KACA,aAAA,QAHJ,iBACI,iBAAA,QACA,MAAA,KACA,aAAA,QAHJ,gBACI,iBAAA,QACA,MAAA,KACA,aAAA,QAHJ,eACI,iBAAA,QACA,MAAA,KACA,aAAA,QAHJ,cACI,iBAAA,QACA,MAAA,KACA,aAAA,QAHJ,cACI,iBAAA,QACA,MAAA,KACA,aAAA,QAHJ,gBACI,iBAAA,QACA,MAAA,KACA,aAAA,QAKR,eACI,iBAAA,QACA,MAAA,KACA,aAAA,QCpDJ,aACE,YAAA,aAAA,CAAA,WACA,mBAAA,EAAA,KAAA,KAAA,eAAA,WAAA,EAAA,KAAA,KAAA,eACA,OAAA,IAAA,MAAA,QAHF,0BAMI,UAAA,KANJ,4BASI,UAAA,KATJ,2BAYI,OAAA,KAAA,EAZJ,yBAAA,0BAAA,6BAeI,OAAA,IAAA,MAAA,QACA,UAAA,KACA,mBAAA,KAAA,WAAA,KAjBJ,wCAoBI,iBAAA,kBACA,UAAA,QArBJ,sCAyBI,iBAAA,kBACA,UAAA,QA1BJ,iCA8BI,mBAAA,eAAA,WAAA,eAIJ,2BACE,MAAA,QACA,aAAA,QAGF,0BACE,aAAA,QADF,gCfkzCE,sDelzCF,mEAKI,iBAAA,QALJ,uCAAA,8CASI,aAAA,QAKJ,0BACE,MAAA,QACA,aAAA,QAGF,wBACE,aAAA,QADF,8BAGI,iBAAA,QAGJ,+BAAA,gCAAA,mCACE,QAAA,EACA,OAAA,IAAA,MAAA,QAGF,6BACE,iBAAA,qBCvEF,SACI,QAAA,IAAA,KACA,iBAAA,KACA,QAAA,GACA,MAAA,QACA,mBAAA,EAAA,KAAA,KAAA,gBAAA,WAAA,EAAA,KAAA,KAAA,gBACA,QAAA,EACA,cAAA,IAGJ,WAEQ,OAAA,KACA,YAAA,MAAA,CAAA,WAIR,aACI,aAAA,cACA,YAAA,KACA,cAAA,KACA,UAAA,KACA,YAAA,IACA,MAAA,QACA,eAAA,UAGJ,wBAGY,cAAA,IfuCR,4BejCA,aACI,QAAA,MCrCR,mBAEQ,YAAA,MAAA,CAAA,qBAGR,cACI,SAAA,SACA,QAAA,GAFJ,mCAKQ,UAAA,KACA,WAAA,OACA,cAAA,IACA,QAAA,KAAA,KACA,WAAA,KACA,MAAA,QACA,mBAAA,EAAA,KAAA,KAAA,gBAAA,WAAA,EAAA,KAAA,KAAA,gBACA,YAAA,aAAA,CAAA,WAZR,2DAeY,YAAA,IACA,OAAA,MAAA,EACA,YAAA,MAAA,CAAA,WAjBZ,uDAqBY,YAAA,OACA,OAAA,KAAA,EACA,MAAA,KC5BZ,eACI,OAAA,KACA,SAAA,SACA,MAAA,KCHJ,YACE,mBAAA,YAAA,WAAA,YACA,MAAA,eACA,OAAA,eACA,iBAAA,eACA,mBAAA,EAAA,KAAA,KAAA,gBAAA,WAAA,EAAA,KAAA,KAAA,gBACA,QAAA,IAAA,eACA,cAAA,IACA,aAAA,eAGF,UACE,MAAA,kBACA,UAAA,eACA,YAAA,eACA,YAAA,aAAA,CAAA,qBACA,YAAA,cCjBF,OpBm8CA,gBoBj8CE,OAAA,MACA,WAAA,KACA,cAAA,IAEF,eACE,QAAA,MACA,WAAA,OACA,MAAA,KACA,UAAA,KACA,YAAA,KACA,WAAA,QACA,cAAA,IACA,QAAA,KAAA,KAEF,qBACE,KAAA,IACA,YAAA,MACA,MAAA,EACA,OAAA,EACA,SAAA,SAEF,2BACE,OAAA,MACA,YAAA,KAAA,MAAA,YACA,aAAA,KAAA,MAAA,YACA,WAAA,KAAA,MAAA,QAEF,2BACE,IAAA,MACA,YAAA,KAAA,MAAA,YACA,aAAA,KAAA,MAAA,YACA,cAAA,KAAA,MAAA,QAEF,YACE,QAAA,GACA,OAAA,EAAA,MAAA,MAAA,MAFF,yBAKI,OAAA,KACA,MAAA,KCvCJ,kBACE,OAAA,KACA,WAAA,QACA,MAAA,KACA,YAAA,MAAA,CAAA,WACA,UAAA,QACA,QAAA,IAAA,ICJF,aAEM,SAAA,SAFN,qBAKU,IAAA,KALV,sBASU,IAAA,KATV,oBAaM,SAAA,SACA,iBAAA,QACA,QAAA,IACA,cAAA,IACA,QAAA,IAAA,KACA,QAAA,KACA,UAAA,MACA,QAAA,KACA,MAAA,KACA,YAAA,MAAA,CAAA,WAtBN,gBtBqgDA,iBACA,mBsB3+CM,QAAA,aACA,WAAA,OACA,eAAA,OACA,cAAA,IACA,YAAA,IACA,OAAA,QACA,iBAAA,QACA,gBAAA,KACA,MAAA,KACA,UAAA,KACA,SAAA,SACA,IAAA,EACA,KAAA,KACA,MAAA,KACA,OAAA,KACA,YAAA,KA1CN,yBA+CU,YAAA,aAAA,CAAA,qBClDV,UACE,MAAA,KACA,cAAA,EAGF,SACE,WAAA,KAGF,2BAEM,WAAA,KAIN,YACE,OAAA,KAAA,EAAA,IAAA,EADF,eAGM,UAAA,QACA,YAAA,SACA,eAAA,UAIN,4BAEM,YAAA,IAIN,QACE,WAAA,KAGF,6BvBohDA,6BAGA,4BAFA,yBACA,yBuBhhDM,QAAA,EAIN,wBAEM,WAAA,QACA,UAAA,KACA,YAAA,KACA,QAAA,KAAA,EACA,eAAA,UvBqhDN,yBAEA,yBADA,qBAFA,mBAFA,gBuB9gDA,gBvB+gDA,mBuBvgDM,aAAA,QAIN,kDAIc,MAAA,MACA,OAAA,IACA,YAAA,MAAA,CAAA,WACA,UAAA,KAMd,WACE,WAAA,QACA,OAAA,KACA,MAAA,QACA,eAAA,WACA,mBAAA,KAAA,WAAA,KACA,cAAA,IACA,OAAA,EAAA,IACA,QAAA,IAAA,KACA,OAAA,KAGF,eACE,YAAA,QACA,UAAA,KAGF,gBACE,WAAA,QAGF,oBACE,WAAA,QvBogDF,iBACA,mBuBlgDA,eAGE,WAAA,QACA,MAAA,KACA,YAAA,KAGF,iBACE,WAAA,QAGF,uBAEM,WAAA,KAIN,UACE,cAAA,IACA,OAAA,KACA,OAAA,KACA,UAAA,SACA,OAAA,IAAA,IACA,QAAA,IAAA,IACA,WAAA,OAGF,gBACE,OAAA,KACA,OAAA,KAAA,EACA,QAAA,IAAA,KACA,MAAA,KAGF,sCAGU,cAAA,IAHV,gCAOM,cAAA,IAPN,2BAUM,MAAA,KAIN,gCAEM,MAAA,KtBtFF,4BsB2FF,uBAAA,qBAAA,sBAEQ,MAAA,KACA,QAAA,MACA,MAAA,KACA,OAAA,KAAA,EAGR,oBAIgB,MAAA,KAKhB,iBACI,QAAA,MC/KN,WACE,YAAA,WACA,WAAA,OACA,YAAA,IACA,IAAA,6BACA,IAAA,oCAAA,2BAAA,CAAA,+BAAA,cAAA,CAAA,8BAAA,mBAIF,wBAEI,OAAA,IAAA,MAAA,QACA,mBAAA,KAAA,WAAA,KACA,OAAA,EAJJ,wCAOM,iBAAA,QACA,WAAA,IAAA,MAAA,QARN,uCAYQ,OAAA,KAKR,oBACE,QAAA,KAGF,eACE,OAAA,KACA,cAAA,MACA,QAAA,OAAA,MAHF,8BAOM,cAAA,ExBmpDN,qDwB9oDA,0DAEI,UAAA,MAGJ,cACG,QAAA,ECnDH,8CAEQ,OAAA,IAAA,MAAA,QACA,OAAA,KACA,QAAA,EAJR,2EAMY,YAAA,KACA,aAAA,KAPZ,wEAUY,OAAA,KACA,MAAA,KACA,MAAA,IAZZ,0EAcgB,aAAA,QAAA,YAAA,YAAA,YACA,aAAA,IAAA,IAAA,EAAA,IAMhB,gFAIgB,aAAA,YAAA,YAAA,QAAA,sBACA,aAAA,EAAA,IAAA,IAAA,cAMhB,yBACI,QAAA,IAAA,KAGJ,kBACI,OAAA,IAAA,MAAA,QACA,mBAAA,EAAA,EAAA,KAAA,EAAA,sBAAA,WAAA,EAAA,EAAA,KAAA,EAAA,sBAGJ,sDAEQ,QAAA,KACA,iBAAA,KAHR,6EAKY,OAAA,IAAA,MAAA,QACA,QAAA,EANZ,iFAUQ,WAAA,QAVR,yEAaQ,iBAAA,QACA,MAAA,QAdR,+EAgBY,WAAA,QACA,MAAA,KAKZ,gDAEQ,WAAA,KACA,OAAA,IAAA,MAAA,kBAHR,6EAKY,QAAA,IAAA,KALZ,uEAQY,OAAA,EARZ,2EAWY,WAAA,QACA,OAAA,KACA,MAAA,KACA,cAAA,IACA,QAAA,EAAA,IACA,WAAA,IAhBZ,mFAmBY,MAAA,KACA,aAAA,IApBZ,yFAsBgB,MAAA,KCrFhB,0BACI,OAAA,IAAA,MAAA,QACA,WAAA,KACA,OAAA,QACA,SAAA,KACA,WAAA,gBACA,mBAAA,EAAA,IAAA,IAAA,gBAAA,WAAA,EAAA,IAAA,IAAA,gBANJ,iCAQM,YAAA,IACA,MAAA,QAIJ,yBACE,QAAA,IAAA,KACA,YAAA,OACA,SAAA,OAGF,4BACE,QAAA,IAGF,uBACE,WAAA,QACA,OAAA,QAIF,oBACE,QAAA,IACA,YAAA,IACA,YAAA,MAAA,CAAA,WAHF,2BAKI,YAAA,IACA,UAAA,KACA,MAAA,QACA,QAAA,MCnCN,qBACI,mBAAA,KAAA,WAAA,KACA,QAAA,IAAA,IAAA,IACA,OAAA,IAAA,MAAA,QACA,MAAA,KAJJ,iCAOM,WAAA,QACA,QAAA,aACA,UAAA,KACA,OAAA,IAAA,IACA,QAAA,EAAA,IACA,cAAA,ICdN,sFACI,MAAA,eAGJ,qDAKoB,QAAA,MACA,MAAA,KACA,MAAA,KACA,YAAA,IACA,MAAA,QACA,WAAA,QACA,YAAA,OACA,WAAA,EAAA,EACA,OAAA,EAbpB,2DAesB,WAAA,QACA,MAAA,KAhBtB,yCAwBY,QAAA,QACA,QAAA,aACA,YAAA,wBA1BZ,yCA6BY,QAAA,YACA,eAAA,EA9BZ,oBAkCQ,QAAA,YAlCR,yBAqCQ,WAAA,kBArCR,2CAyCY,MAAA,eC7CZ,qBACE,OAAA,EACA,QAAA,EAFF,wBAKI,WAAA,KACA,MAAA,QACA,WAAA,IACA,aAAA,KACA,SAAA,SATJ,+BAYM,QAAA,QACA,YAAA,wBACA,SAAA,SACA,KAAA,IACA,IAAA,KAIN,eACE,aAAA,QAGF,iBACE,aAAA,QCxBF,4CAIgB,MAAA,KACA,OAAA,EALhB,8CAWoB,iBAAA,YACA,OAAA,IAAA,MAAA,YCZpB,2BAAA,iCACI,WAAA,QACA,WAAA,QAEF,oCACE,OAAA,IAAA,MAAA,kBAEF,4BACE,cAAA,IACA,MAAA,QACA,YAAA,IACA,UAAA,K/Bs3DkC,mC+Bp3DpC,mCAAA,qC/Bo3DF,qC+Bl3DI,OAAA,IAAA,MAAA,kBACA,QAAA,IACA,MAAA,KAEF,mCAAA,kCACE,WAAA,QACA,WAAA,QACA,OAAA,IAAA,MAAA,QACA,MAAA,KAEF,oCAAA,mCACE,aAAA,QCxBJ,yBACI,OAAA,QAEF,4BAAA,uBAAA,uBACE,KAAA,QAGF,kCAEM,iBAAA,QACA,MAAA,KCZR,eAKM,SAAA,SACA,QAAA,MACA,MAAA,KAPN,qBAUc,MAAA,IAVd,iBAeQ,UAAA,KACA,OAAA,EAAA,KAAA,KAhBR,uBAmBQ,cAAA,IACA,iBAAA,qBACA,QAAA,aACA,YAAA,KACA,aAAA,KACA,MAAA,KACA,WAAA,OACA,UAAA,QA1BR,iBAAA,wBAAA,uBA+BU,QAAA,MACA,MAAA,KACA,QAAA,IAAA,IACA,gBAAA,KACA,cAAA,IAnCV,2BAwCY,OAAA,IAAA,MAAA,QACA,MAAA,QACE,OAAA,QA1Cd,0BAAA,iCAAA,gCAmDc,WAAA,QACA,MAAA,KACA,OAAA,QArDd,kCAAA,yCAAA,wCAuDgB,MAAA,KAvDhB,uBAAA,8BAAA,6BA8DY,WAAA,QACA,MAAA,KA/DZ,wBAAA,+BAAA,8BAqEc,WAAA,KACA,MAAA,KACA,aAAA,QAvEd,uBAAA,qBA+Ec,MAAA,KACA,SAAA,SAhFd,iBAwFM,QAAA,MACA,WAAA,MACA,SAAA,OACA,SAAA,SACA,MAAA,KACA,QAAA,KA7FN,uBA+FU,QAAA,EACA,SAAA,SACA,MAAA,IAjGV,0BAoGc,WAAA,eApGd,6BAsGkB,QAAA,MACA,YAAA,KAvGlB,8BA4Gc,OAAA,EAAA,KACA,MAAA,KACA,OAAA,KA9Gd,6BAkHc,QAAA,MACA,aAAA,QAnHd,mCAsHkB,aAAA,QAtHlB,4CAyHkB,QAAA,aAzHlB,mCA6HkB,WAAA,KACA,OAAA,IAAA,MAAA,KACA,MAAA,QA/HlB,6BAoIc,QAAA,aACA,cAAA,KACA,WAAA,KAtId,mCAwIkB,MAAA,QACA,UAAA,KAzIlB,iBAkJM,SAAA,SACA,QAAA,MACA,WAAA,MACA,MAAA,KACA,WAAA,KAtJN,oBAwJU,QAAA,aACA,WAAA,MAzJV,uBA2Jc,OAAA,EAAA,KA3Jd,mBAAA,0BAAA,yBAgKU,WAAA,QACA,MAAA,KACA,QAAA,MACA,QAAA,KAAA,IACA,gBAAA,KACA,cAAA,IArKV,6BAAA,oCAAA,mCA0KY,WAAA,KACA,MAAA,QACA,OAAA,QACA,OAAA,IAAA,MAAA,QA7KZ,wBAsLU,QAAA,OACA,MAAA,KACA,MAAA,IAxLV,8BA2LkB,MAAA,KACA,MAAA,KA5LlB,0BAkMQ,MAAA,IACA,OAAA,EAAA,KAAA,KACA,QAAA,OACA,MAAA,KArMR,0BAyMU,QAAA,OACA,MAAA,MACA,MAAA,IACA,OAAA,EAAA,KACA,WAAA,eA7MV,gCAgNkB,OAAA,EAAA,EAAA,EAAA,IAalB,YAAA,QACE,QAAA,MACA,MAAA,KACA,SAAA,OAHF,cAAA,UAKM,QAAA,EALN,eAAA,WASM,WAAA,eACA,QAAA,EACA,OAAA,EAXN,kBAAA,cAcU,QAAA,MACA,QAAA,EAfV,iCAAA,6BAuBU,SAAA,SACA,KAAA,OAxBV,4BAAA,wBA8BU,SAAA,SACA,KAAA,OhCxLN,4BgC8LF,0BAAA,wBAAA,qBACI,MAAA,MCpQN,kBACI,WAAA,yBAAA,OAAA,OAAA,UAGJ,sBACI,WAAA,2BAAA,OAAA,OAAA,UAGJ,0BACI,QAAA,MCTJ,UACE,OAAA,IAAA,OAAA,kBACA,WAAA,KACA,cAAA,ICHF,oCACI,QAAA,EAGJ,gBACI,gBAAA,mBACA,cAAA,eAFJ,kCAAA,mCAQY,iBAAA,QARZ,qCAAA,sCAWgB,aAAA,QAXhB,+BAgBgB,QAAA,YAhBhB,+BAAA,+BAqBY,QAAA,IAAA,MAAA,kBACA,eAAA,KACA,MAAA,QACA,iBAAA,qBAKZ,iBACI,YAAA,IAKJ,8EAAA,8EAMwB,mBAAA,EAAA,KAAA,KAAA,gBAAA,WAAA,EAAA,KAAA,KAAA,gBACA,iBAAA,QACA,IAAA,OARxB,2EAAA,2EAiBwB,iBAAA,QACA,IAAA,OAWxB,mBACI,iBAAA,QACA,OAAA,KACA,MAAA,KACA,mBAAA,KAAA,WAAA,KACA,cAAA,IACA,WAAA,OACA,QAAA,GAPJ,sBAUQ,cAAA,KACA,iBAAA,qBACA,MAAA,KnCXJ,4BmCgBA,wBAAA,4BACI,QAAA,aAGJ,mBACI,QAAA,KAEJ,wBAEQ,WAAA,OACA,QAAA,MACA,OAAA,OAAA,EAAA,YAGR,eACI,QAAA,aACA,cAAA,QAKR,4BAEQ,iBAAA,QC5GR,iDAEM,QAAA,IAAA,KAFN,oCAMM,OAAA,KANN,2BAUQ,UAAA,KACA,YAAA,IAXR,gCAeM,aAAA,KAfN,sCAkBQ,QAAA,aACA,aAAA,IACA,SAAA,SACA,cAAA,EArBR,8CAuBU,cAAA,IAAA,YACA,mBAAA,IAAA,YACA,iBAAA,KACA,cAAA,IACA,OAAA,IAAA,MAAA,QACA,QAAA,GACA,QAAA,aACA,OAAA,KACA,KAAA,EACA,YAAA,MACA,SAAA,SACA,WAAA,IAAA,YACA,MAAA,KACA,QAAA,EApCV,6CAuCU,MAAA,QACA,QAAA,aACA,UAAA,KACA,OAAA,KACA,KAAA,EACA,YAAA,MACA,aAAA,IACA,YAAA,IACA,SAAA,SACA,IAAA,KACA,MAAA,KAjDV,qDAqDQ,OAAA,QACA,QAAA,EACA,QAAA,EACA,QAAA,EAxDR,oEA2DU,QAAA,IA3DV,yEAgEU,eAAA,KACA,QAAA,EAjEV,0EAsEa,QAAA,QACT,YAAA,sBACA,YAAA,IAxEJ,4EA6EU,iBAAA,QACA,OAAA,YA9EV,2EAmFU,iBAAA,KACA,aAAA,QApFV,0EAuFU,MAAA,QrCmnEV,uCqC1sEA,qDAAA,qDA6FM,WAAA,QACA,WAAA,QACA,MAAA,KACA,aAAA,QrCgnEJ,6CqChtEF,2DAAA,2DAkGU,MAAA,KAlGV,uDAuGY,IAAA,eAvGZ,+BA2GM,iBAAA,KACA,OAAA,IAAA,MAAA,kBACA,MAAA,QA7GN,+BAgHQ,WAAA,QACA,aAAA,QACA,MAAA,KAlHR,wCAqHM,MAAA,MArHN,uDAuHQ,KAAA,KACA,MAAA,EAKN,+BACE,QAAA,MC9HJ,gBAEQ,WAAA,QACA,iBAAA,KACA,OAAA,KAJR,mBAMY,YAAA,KANZ,kCASY,OAAA,KACA,YAAA,IACA,YAAA,MAAA,CAAA,WAXZ,aAeQ,WAAA,IAAA,MAAA,kBtCyuER,mBsCruEA,aAEI,UAAA,QACA,YAAA,QACA,QAAA,etCuuEJ,mBsCpuEA,yBAEI,cAAA,KtCsuEJ,0CsCnuEA,0CAEI,UAAA,IACA,cAAA,KAGJ,uCACI,mBAAA,KAAA,WAAA,KACA,aAAA,QAGJ,sCACI,YAAA,KACA,iBAAA,KACA,eAAA,KACA,OAAA,IAAA,MAAA,QACA,QAAA,IAAA,KACA,MAAA,QANJ,4CASQ,QAAA,KAIR,uDAEQ,WAAA,KtCiuER,6CADA,4CsC5tEA,4CAGI,MAAA,kBACA,iBAAA,QACA,QAAA,YACA,mBAAA,eAAA,WAAA,eACA,iBAAA,KAGJ,wCAEQ,QAAA,MAIR,4CACI,aAAA,IAAA,MAAA,QAGJ,uBACI,OAAA,QAGJ,4BACI,MAAA,eCrFJ,aACI,YAAA,KACA,cAAA,MAFJ,kBAKQ,cAAA,KACA,YAAA,IAMR,qBAEQ,OAAA,QACA,YAAA,KACA,YAAA,OACA,cAAA,SACA,QAAA,MACA,SAAA,OAPR,uBASY,cAAA,EACA,YAAA,QAVZ,mBAcQ,WAAA,OACA,eAAA,OACA,UAAA,KACA,MAAA,KACA,OAAA,KACA,YAAA,KACA,aAAA,KACA,MAAA,kBACA,cAAA,IACA,QAAA,aACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IAxBR,2BA2BQ,gBAAA,YACA,WAAA,KA5BR,mCA8BY,MAAA,KACA,WAAA,QAUZ,gCAEQ,iBAAA,QACA,WAAA,KACA,UAAA,MACA,YAAA,IACA,QAAA,KAAA,KC3DR,UACI,SAAA,SACA,IAAA,KACA,MAAA,KAIJ,WACI,MAAA,MACA,OAAA,EAAA,KACA,QAAA,KAAA,EAGF,MACE,iBAAA,KACA,kBAAA,EACA,UAAA,KAAA,GAAA,YACA,kBAAA,KAAA,GAAA,YAGF,MACE,UAAA,KAAA,GACA,kBAAA,KAAA,GACA,iBAAA,IAAA,IACA,yBAAA,IAAA,IAIJ,gBACA,GACI,kBAAA,KAEJ,KACI,kBAAA,GAIJ,gBACA,GACI,kBAAA,UAEJ,KACI,kBAAA,gBC3CJ,eACE,MAAA,MACA,MAAA,KACA,QAAA,EAAA,KAAA,KAAA,KAEF,gBACE,YAAA,MAGF,cACE,QAAA,MACA,aAAA,EAFF,iBAKI,SAAA,SACA,QAAA,MACA,OAAA,KACA,YAAA,KACA,OAAA,QACA,4BAAA,IAAA,oBAAA,IAVJ,mBAaM,MAAA,QAbN,uBAiBM,WAAA,sBACA,4BAAA,KAAA,oBAAA,KAlBN,2BAsBM,MAAA,KACA,SAAA,SAvBN,6BA2BM,MAAA,MzC43EA,oDACA,kCyCx5EN,0CAgCQ,QAAA,MACA,MAAA,KAjCR,kCAqCQ,OAAA,IAAA,MAAA,YACA,cAAA,MACA,OAAA,KAAA,KAAA,EACA,OAAA,EACA,MAAA,EACA,YAAA,EACA,UAAA,EA3CR,oDA+CQ,OAAA,KAAA,KAAA,EAAA,KA/CR,0CAmDQ,WAAA,KACA,YAAA,IApDR,oCAwDQ,SAAA,SACA,IAAA,KACA,KAAA,MACA,MAAA,EACA,cAAA,SACA,SAAA,OACA,YAAA,OA9DR,6BAmEM,SAAA,SACA,IAAA,EACA,KAAA,MACA,MAAA,EACA,OAAA,EzCg3EA,mCyCv7EN,sCA2EQ,SAAA,SACA,IAAA,EA5ER,sCAgFQ,KAAA,EACA,MAAA,MACA,cAAA,SACA,SAAA,OACA,YAAA,OApFR,mCAwFQ,MAAA,EACA,MAAA,MACA,aAAA,KA1FR,wBAAA,0BAgGI,WAAA,sBACA,4BAAA,KAAA,oBAAA,KAjGJ,wBzCy8EE,8ByCn2EE,mBAAA,MAAA,IAAA,EAAA,EAAA,QAAA,WAAA,MAAA,IAAA,EAAA,EAAA,QAtGJ,0BA0GI,YAAA,IACA,MAAA,QA3GJ,2CA+GI,aAAA,QA/GJ,6CAmHI,aAAA,QAnHJ,4CAuHI,aAAA,QAvHJ,qCA2HI,OAAA,QACA,OAAA,KACA,MAAA,KACA,SAAA,SACA,QAAA,aACA,mBAAA,MAAA,EAAA,EAAA,EAAA,IAAA,QAAA,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,QACA,cAAA,IAjIJ,2CAoIM,QAAA,EACA,OAAA,QArIN,yDAwIM,QAAA,EAxIN,2CA4IM,SAAA,SACA,IAAA,IACA,KAAA,IACA,MAAA,IACA,OAAA,IACA,OAAA,QACA,WAAA,QACA,QAAA,EACA,cAAA,YACA,4BAAA,KAAA,oBAAA,KArJN,kDAyJM,WAAA,QAMN,aAEI,YAAA,MAAA,CAAA,WACA,eAAA,OACA,MAAA,QACA,QAAA,KAAA,KACA,QAAA,MAKJ,yBACE,eACE,MAAA,KAEF,gBACE,YAAA,GAIJ,yBACE,6BAEI,MAAA,MAFJ,oCAKM,KAAA,KALN,6BASI,KAAA,MATJ,mCAWM,WAAA,MACA,cAAA,KACA,aAAA,MC3MR,UACE,gBAAA,SACA,eAAA,EACA,QAAA,MACA,cAAA,KACA,SAAA,SACA,aAAA,MACA,MAAA,KAPF,qBAUI,cAAA,KACA,aAAA,MACA,WAAA,KACA,SAAA,SAbJ,uBAeM,MAAA,KAfN,iBAmBI,iBAAA,kBACA,OAAA,EACA,QAAA,GACA,KAAA,IACA,SAAA,SACA,IAAA,KACA,MAAA,IACA,QAAA,EA1BJ,yBA6BI,sBAAA,IACA,WAAA,QACA,cAAA,IACA,MAAA,KACA,QAAA,MACA,OAAA,KACA,KAAA,MACA,WAAA,MACA,SAAA,SACA,WAAA,OACA,IAAA,IACA,MAAA,KAxCJ,2BA0CM,MAAA,KACA,UAAA,KACA,WAAA,IACA,SAAA,SACA,KAAA,IA9CN,4BAmDM,UAAA,KACA,WAAA,IAMN,kBACE,MAAA,QACA,UAAA,KACA,YAAA,IACA,OAAA,EAAA,EAAA,IACA,eAAA,UAGF,eACE,QAAA,UADF,sBAGI,QAAA,GACA,QAAA,MACA,MAAA,IALJ,qCASM,cAAA,KAAA,MAAA,YACA,aAAA,KAAA,MAAA,+BACA,WAAA,KAAA,MAAA,YACA,QAAA,MACA,OAAA,EACA,KAAA,MACA,WAAA,MACA,SAAA,SACA,IAAA,IACA,MAAA,EAlBN,4CAqBM,QAAA,KArBN,8BAyBI,cAAA,KAIJ,yBAEI,QAAA,GACA,QAAA,MACA,MAAA,IAJJ,6CAQM,cAAA,KAAA,MAAA,YACA,YAAA,KAAA,MAAA,+BACA,WAAA,KAAA,MAAA,YACA,QAAA,MACA,OAAA,EACA,KAAA,KACA,WAAA,MACA,SAAA,SACA,MAAA,MACA,IAAA,IACA,MAAA,EAlBN,yCAqBM,MAAA,MACA,WAAA,KAtBN,2CAwBQ,MAAA,MACA,YAAA,IAzBR,kCA8BI,KAAA,KACA,MAAA,MA/BJ,0BAkCI,QAAA,KAlCJ,0BAqCI,YAAA,EACA,aAAA,KAtCJ,sBAyCI,WAAA,MAzCJ,qBA4CI,WAAA,MA5CJ,kCA+CI,WAAA,MAKJ,eACE,QAAA,WACA,eAAA,IACA,MAAA,IAHF,kBAKI,UAAA,KACA,OAAA,EANJ,sBASI,WAAA,qBACA,QAAA,MACA,cAAA,IACA,YAAA,KACA,SAAA,SACA,WAAA,KACA,OAAA,EAfJ,uBAmBM,MAAA,QACA,QAAA,MACA,UAAA,KACA,cAAA,IAtBN,iBA0BI,MAAA,KACA,UAAA,KACA,cAAA,EA5BJ,sBA+BI,WAAA,KA/BJ,wBAiCM,MAAA,KACA,aAAA,IAlCN,0BAsCM,OAAA,KACA,MAAA,KACA,cAAA,IAxCN,6BA4CI,WAAA,KAAA,OAAA,OAAA,EAAA,EAAA,KACA,WAAA,KACA,QAAA,ICjMJ,cACE,OAAA,IAAA,EAGF,kBACE,iBAAA,QACA,WAAA,QACA,eAAA,EACA,WAAA,MAGF,cACE,QAAA,MACA,OAAA,MACA,MAAA,KAGF,aACE,SAAA,SACA,UAAA,MACA,OAAA,KAAA,KACA,iBAAA,KACA,cAAA,IALF,8BAQI,QAAA,KARJ,0BAYI,SAAA,SACA,KAAA,EACA,MAAA,EAIJ,kBACE,QAAA,KAAA,KAAA,EAAA,KAGF,YACE,MAAA,QACA,YAAA,oBAAA,IAAA,GAAA,CAAA,oBAAA,KAAA,IACA,UAAA,KACA,YAAA,IACA,YAAA,KAIF,WACE,MAAA,MACA,OAAA,EAAA,KACA,QAAA,KAAA,EAGF,MACE,iBAAA,KACA,kBAAA,EACA,UAAA,KAAA,GAAA,YACA,kBAAA,KAAA,GAAA,YAGF,MACE,UAAA,KAAA,GACA,kBAAA,KAAA,GACA,iBAAA,IAAA,IACA,yBAAA,IAAA,IAGF,wBACE,GACE,kBAAA,KAEF,KACE,kBAAA,GAIJ,gBACE,GACE,kBAAA,KAEF,KACE,kBAAA,GAIJ,wBACE,GACE,kBAAA,UAEF,KACE,kBAAA,gBAIJ,gBACE,GACE,kBAAA,UAEF,KACE,kBAAA,gBAIJ,wBACE,GACE,QAAA,EAEF,KACE,QAAA,GAIJ,gBACE,GACE,QAAA,EACF,KACE,QAAA,GCnHJ,gCAEI,MAAA,MACA,OAAA,MAAA,KAAA,MACA,eAAA,IACA,YAAA,KACA,WAAA,OACA,QAAA,EACA,SAAA,SARJ,4BAWI,YAAA,MAAA,CAAA,WACA,eAAA,IAZJ,4BAeI,UAAA,KACA,YAAA,MAAA,CAAA,WAhBJ,+BAmBI,UAAA,KACA,MAAA,qBApBJ,4BAwBI,QAAA,KAAA,KAAA,KAxBJ,+BA0BM,cAAA,KACA,YAAA,KC5BN,KACI,UAAA,IAGJ,KACI,WAAA,MCHJ,oBAEQ,WAAA,MACA,KAAA,eACA,MAAA,EACA,OAAA,KAIR,qBACI,MAAA,eACA,KAAA,YAFJ,0BAIQ,KAAA,YAMR,WAAA,oBACI,UAAA,IAMJ,GACI,cAAA,EAMJ,8CAIgB,aAAA,EACA,uBAAA,EACA,0BAAA,ECxBZ,wBAAA,OACA,2BAAA,ODiBJ,6CAYgB,wBAAA,EACA,2BAAA,ECjBZ,uBAAA,OACA,0BAAA,OD0BJ,oBACI,WAAA,eAKJ,GACI,aAAA,EAKJ,qBAEQ,OAAA,MAAA,KAAA,MAAA,MACA,KAAA,EAIR,iCAEQ,aAAA,OACA,YAAA,EAHR,gCAOQ,YAAA,OACA,aAAA,EAOR,mBACI,aAAA,OACA,cAAA,QAFJ,0BAKQ,KAAA,EACA,MAAA,KAOR,kCAEQ,cAAA,MACA,aAAA,EAHR,0CAKY,aAAA,MACA,QAAA,QACA,cAAA,EAOZ,mBACI,YAAA,OACA,aAAA,EAGJ,gBACI,cAAA,OACA,aAAA,EAGJ,8BAEQ,KAAA,KACA,MAAA,QAHR,6BAQQ,KAAA,KACA,MAAA,QAIR,eACI,cAAA,QACA,aAAA,EAFJ,6CAMY,MAAA,SACA,KAAA,KAPZ,4CAWY,MAAA,qBACA,KAAA,KAZZ,0EAkBY,kBAAA,oBAAA,UAAA,oBAKZ,0BAEQ,MAAA,KACA,KAAA,EACA,aAAA,QAQR,qBACI,YAAA,KACA,aAAA,EAGJ,oBACI,aAAA,KACA,YAAA,E9C+uFJ,6CACA,4CAHA,wFACA,+EAHA,uDACA,oE8CzuFA,uC9CuuFA,oD8C/tFI,wBAAA,MACA,2BAAA,MACA,uBAAA,EACA,0BAAA,E9C8uFJ,8CACA,6C8C5uFA,sC9CsuFA,mDAGA,qEACA,kFAHA,yDACA,sE8ChuFI,uBAAA,MACA,0BAAA,MACA,wBAAA,EACA,2BAAA,EE9MI,KAAgC,OAAA,YAChC,MhDw7FR,MgDt7FU,WAAA,YAEF,MhDw7FR,MgDt7FU,YAAA,YACA,aAAA,YAEF,MhDw7FR,MgDt7FU,cAAA,YAEF,MhDw7FR,MgDt7FU,aAAA,YACA,YAAA,YAjBF,KAAgC,OAAA,kBAChC,MhD68FR,MgD38FU,WAAA,kBAEF,MhD68FR,MgD38FU,YAAA,kBACA,aAAA,YAEF,MhD68FR,MgD38FU,cAAA,kBAEF,MhD68FR,MgD38FU,aAAA,kBACA,YAAA,YAjBF,KAAgC,OAAA,iBAChC,MhDk+FR,MgDh+FU,WAAA,iBAEF,MhDk+FR,MgDh+FU,YAAA,iBACA,aAAA,YAEF,MhDk+FR,MgDh+FU,cAAA,iBAEF,MhDk+FR,MgDh+FU,aAAA,iBACA,YAAA,YAjBF,KAAgC,OAAA,iBAChC,MhDu/FR,MgDr/FU,WAAA,iBAEF,MhDu/FR,MgDr/FU,YAAA,iBACA,aAAA,YAEF,MhDu/FR,MgDr/FU,cAAA,iBAEF,MhDu/FR,MgDr/FU,aAAA,iBACA,YAAA,YAjBF,KAAgC,OAAA,kBAChC,MhD4gGR,MgD1gGU,WAAA,kBAEF,MhD4gGR,MgD1gGU,YAAA,kBACA,aAAA,YAEF,MhD4gGR,MgD1gGU,cAAA,kBAEF,MhD4gGR,MgD1gGU,aAAA,kBACA,YAAA,YAjBF,KAAgC,OAAA,iBAChC,MhDiiGR,MgD/hGU,WAAA,iBAEF,MhDiiGR,MgD/hGU,YAAA,iBACA,aAAA,YAEF,MhDiiGR,MgD/hGU,cAAA,iBAEF,MhDiiGR,MgD/hGU,aAAA,iBACA,YAAA,YAjBF,KAAgC,QAAA,YAChC,MhDsjGR,MgDpjGU,YAAA,YAEF,MhDsjGR,MgDpjGU,aAAA,YACA,cAAA,YAEF,MhDsjGR,MgDpjGU,eAAA,YAEF,MhDsjGR,MgDpjGU,cAAA,YACA,aAAA,YAjBF,KAAgC,QAAA,kBAChC,MhD2kGR,MgDzkGU,YAAA,kBAEF,MhD2kGR,MgDzkGU,aAAA,kBACA,cAAA,YAEF,MhD2kGR,MgDzkGU,eAAA,kBAEF,MhD2kGR,MgDzkGU,cAAA,kBACA,aAAA,YAjBF,KAAgC,QAAA,iBAChC,MhDgmGR,MgD9lGU,YAAA,iBAEF,MhDgmGR,MgD9lGU,aAAA,iBACA,cAAA,YAEF,MhDgmGR,MgD9lGU,eAAA,iBAEF,MhDgmGR,MgD9lGU,cAAA,iBACA,aAAA,YAjBF,KAAgC,QAAA,iBAChC,MhDqnGR,MgDnnGU,YAAA,iBAEF,MhDqnGR,MgDnnGU,aAAA,iBACA,cAAA,YAEF,MhDqnGR,MgDnnGU,eAAA,iBAEF,MhDqnGR,MgDnnGU,cAAA,iBACA,aAAA,YAjBF,KAAgC,QAAA,kBAChC,MhD0oGR,MgDxoGU,YAAA,kBAEF,MhD0oGR,MgDxoGU,aAAA,kBACA,cAAA,YAEF,MhD0oGR,MgDxoGU,eAAA,kBAEF,MhD0oGR,MgDxoGU,cAAA,kBACA,aAAA,YAjBF,KAAgC,QAAA,iBAChC,MhD+pGR,MgD7pGU,YAAA,iBAEF,MhD+pGR,MgD7pGU,aAAA,iBACA,cAAA,YAEF,MhD+pGR,MgD7pGU,eAAA,iBAEF,MhD+pGR,MgD7pGU,cAAA,iBACA,aAAA,YAQF,MAAwB,OAAA,mBACxB,OhD2pGR,OgDzpGU,WAAA,mBAEF,OhD2pGR,OgDzpGU,aAAA,mBAEF,OhD2pGR,OgDzpGU,cAAA,mBAEF,OhD2pGR,OgDzpGU,YAAA,mBAfF,MAAwB,OAAA,kBACxB,OhD8qGR,OgD5qGU,WAAA,kBAEF,OhD8qGR,OgD5qGU,aAAA,kBAEF,OhD8qGR,OgD5qGU,cAAA,kBAEF,OhD8qGR,OgD5qGU,YAAA,kBAfF,MAAwB,OAAA,kBACxB,OhDisGR,OgD/rGU,WAAA,kBAEF,OhDisGR,OgD/rGU,aAAA,kBAEF,OhDisGR,OgD/rGU,cAAA,kBAEF,OhDisGR,OgD/rGU,YAAA,kBAfF,MAAwB,OAAA,mBACxB,OhDotGR,OgDltGU,WAAA,mBAEF,OhDotGR,OgDltGU,aAAA,mBAEF,OhDotGR,OgDltGU,cAAA,mBAEF,OhDotGR,OgDltGU,YAAA,mBAfF,MAAwB,OAAA,kBACxB,OhDuuGR,OgDruGU,WAAA,kBAEF,OhDuuGR,OgDruGU,aAAA,kBAEF,OhDuuGR,OgDruGU,cAAA,kBAEF,OhDuuGR,OgDruGU,YAAA,kBAMN,QAAmB,OAAA,eACnB,ShDquGJ,SgDnuGM,WAAA,eAEF,ShDquGJ,SgDnuGM,YAAA,eACA,aAAA,kBAEF,ShDquGJ,SgDnuGM,cAAA,eAEF,ShDquGJ,SgDnuGM,aAAA,eACA,YAAA,e/CbF,yB+ClDI,QAAgC,OAAA,YAChC,ShDwyGN,SgDtyGQ,WAAA,YAEF,ShDuyGN,SgDryGQ,YAAA,YACA,aAAA,YAEF,ShDsyGN,SgDpyGQ,cAAA,YAEF,ShDqyGN,SgDnyGQ,aAAA,YACA,YAAA,YAjBF,QAAgC,OAAA,kBAChC,ShDwzGN,SgDtzGQ,WAAA,kBAEF,ShDuzGN,SgDrzGQ,YAAA,kBACA,aAAA,YAEF,ShDszGN,SgDpzGQ,cAAA,kBAEF,ShDqzGN,SgDnzGQ,aAAA,kBACA,YAAA,YAjBF,QAAgC,OAAA,iBAChC,ShDw0GN,SgDt0GQ,WAAA,iBAEF,ShDu0GN,SgDr0GQ,YAAA,iBACA,aAAA,YAEF,ShDs0GN,SgDp0GQ,cAAA,iBAEF,ShDq0GN,SgDn0GQ,aAAA,iBACA,YAAA,YAjBF,QAAgC,OAAA,iBAChC,ShDw1GN,SgDt1GQ,WAAA,iBAEF,ShDu1GN,SgDr1GQ,YAAA,iBACA,aAAA,YAEF,ShDs1GN,SgDp1GQ,cAAA,iBAEF,ShDq1GN,SgDn1GQ,aAAA,iBACA,YAAA,YAjBF,QAAgC,OAAA,kBAChC,ShDw2GN,SgDt2GQ,WAAA,kBAEF,ShDu2GN,SgDr2GQ,YAAA,kBACA,aAAA,YAEF,ShDs2GN,SgDp2GQ,cAAA,kBAEF,ShDq2GN,SgDn2GQ,aAAA,kBACA,YAAA,YAjBF,QAAgC,OAAA,iBAChC,ShDw3GN,SgDt3GQ,WAAA,iBAEF,ShDu3GN,SgDr3GQ,YAAA,iBACA,aAAA,YAEF,ShDs3GN,SgDp3GQ,cAAA,iBAEF,ShDq3GN,SgDn3GQ,aAAA,iBACA,YAAA,YAjBF,QAAgC,QAAA,YAChC,ShDw4GN,SgDt4GQ,YAAA,YAEF,ShDu4GN,SgDr4GQ,aAAA,YACA,cAAA,YAEF,ShDs4GN,SgDp4GQ,eAAA,YAEF,ShDq4GN,SgDn4GQ,cAAA,YACA,aAAA,YAjBF,QAAgC,QAAA,kBAChC,ShDw5GN,SgDt5GQ,YAAA,kBAEF,ShDu5GN,SgDr5GQ,aAAA,kBACA,cAAA,YAEF,ShDs5GN,SgDp5GQ,eAAA,kBAEF,ShDq5GN,SgDn5GQ,cAAA,kBACA,aAAA,YAjBF,QAAgC,QAAA,iBAChC,ShDw6GN,SgDt6GQ,YAAA,iBAEF,ShDu6GN,SgDr6GQ,aAAA,iBACA,cAAA,YAEF,ShDs6GN,SgDp6GQ,eAAA,iBAEF,ShDq6GN,SgDn6GQ,cAAA,iBACA,aAAA,YAjBF,QAAgC,QAAA,iBAChC,ShDw7GN,SgDt7GQ,YAAA,iBAEF,ShDu7GN,SgDr7GQ,aAAA,iBACA,cAAA,YAEF,ShDs7GN,SgDp7GQ,eAAA,iBAEF,ShDq7GN,SgDn7GQ,cAAA,iBACA,aAAA,YAjBF,QAAgC,QAAA,kBAChC,ShDw8GN,SgDt8GQ,YAAA,kBAEF,ShDu8GN,SgDr8GQ,aAAA,kBACA,cAAA,YAEF,ShDs8GN,SgDp8GQ,eAAA,kBAEF,ShDq8GN,SgDn8GQ,cAAA,kBACA,aAAA,YAjBF,QAAgC,QAAA,iBAChC,ShDw9GN,SgDt9GQ,YAAA,iBAEF,ShDu9GN,SgDr9GQ,aAAA,iBACA,cAAA,YAEF,ShDs9GN,SgDp9GQ,eAAA,iBAEF,ShDq9GN,SgDn9GQ,cAAA,iBACA,aAAA,YAQF,SAAwB,OAAA,mBACxB,UhD+8GN,UgD78GQ,WAAA,mBAEF,UhD88GN,UgD58GQ,aAAA,mBAEF,UhD68GN,UgD38GQ,cAAA,mBAEF,UhD48GN,UgD18GQ,YAAA,mBAfF,SAAwB,OAAA,kBACxB,UhD69GN,UgD39GQ,WAAA,kBAEF,UhD49GN,UgD19GQ,aAAA,kBAEF,UhD29GN,UgDz9GQ,cAAA,kBAEF,UhD09GN,UgDx9GQ,YAAA,kBAfF,SAAwB,OAAA,kBACxB,UhD2+GN,UgDz+GQ,WAAA,kBAEF,UhD0+GN,UgDx+GQ,aAAA,kBAEF,UhDy+GN,UgDv+GQ,cAAA,kBAEF,UhDw+GN,UgDt+GQ,YAAA,kBAfF,SAAwB,OAAA,mBACxB,UhDy/GN,UgDv/GQ,WAAA,mBAEF,UhDw/GN,UgDt/GQ,aAAA,mBAEF,UhDu/GN,UgDr/GQ,cAAA,mBAEF,UhDs/GN,UgDp/GQ,YAAA,mBAfF,SAAwB,OAAA,kBACxB,UhDugHN,UgDrgHQ,WAAA,kBAEF,UhDsgHN,UgDpgHQ,aAAA,kBAEF,UhDqgHN,UgDngHQ,cAAA,kBAEF,UhDogHN,UgDlgHQ,YAAA,kBAMN,WAAmB,OAAA,eACnB,YhDggHF,YgD9/GI,WAAA,eAEF,YhD+/GF,YgD7/GI,YAAA,eACA,aAAA,kBAEF,YhD8/GF,YgD5/GI,cAAA,eAEF,YhD6/GF,YgD3/GI,aAAA,eACA,YAAA,gB/CbF,yB+ClDI,QAAgC,OAAA,YAChC,ShDgkHN,SgD9jHQ,WAAA,YAEF,ShD+jHN,SgD7jHQ,YAAA,YACA,aAAA,YAEF,ShD8jHN,SgD5jHQ,cAAA,YAEF,ShD6jHN,SgD3jHQ,aAAA,YACA,YAAA,YAjBF,QAAgC,OAAA,kBAChC,ShDglHN,SgD9kHQ,WAAA,kBAEF,ShD+kHN,SgD7kHQ,YAAA,kBACA,aAAA,YAEF,ShD8kHN,SgD5kHQ,cAAA,kBAEF,ShD6kHN,SgD3kHQ,aAAA,kBACA,YAAA,YAjBF,QAAgC,OAAA,iBAChC,ShDgmHN,SgD9lHQ,WAAA,iBAEF,ShD+lHN,SgD7lHQ,YAAA,iBACA,aAAA,YAEF,ShD8lHN,SgD5lHQ,cAAA,iBAEF,ShD6lHN,SgD3lHQ,aAAA,iBACA,YAAA,YAjBF,QAAgC,OAAA,iBAChC,ShDgnHN,SgD9mHQ,WAAA,iBAEF,ShD+mHN,SgD7mHQ,YAAA,iBACA,aAAA,YAEF,ShD8mHN,SgD5mHQ,cAAA,iBAEF,ShD6mHN,SgD3mHQ,aAAA,iBACA,YAAA,YAjBF,QAAgC,OAAA,kBAChC,ShDgoHN,SgD9nHQ,WAAA,kBAEF,ShD+nHN,SgD7nHQ,YAAA,kBACA,aAAA,YAEF,ShD8nHN,SgD5nHQ,cAAA,kBAEF,ShD6nHN,SgD3nHQ,aAAA,kBACA,YAAA,YAjBF,QAAgC,OAAA,iBAChC,ShDgpHN,SgD9oHQ,WAAA,iBAEF,ShD+oHN,SgD7oHQ,YAAA,iBACA,aAAA,YAEF,ShD8oHN,SgD5oHQ,cAAA,iBAEF,ShD6oHN,SgD3oHQ,aAAA,iBACA,YAAA,YAjBF,QAAgC,QAAA,YAChC,ShDgqHN,SgD9pHQ,YAAA,YAEF,ShD+pHN,SgD7pHQ,aAAA,YACA,cAAA,YAEF,ShD8pHN,SgD5pHQ,eAAA,YAEF,ShD6pHN,SgD3pHQ,cAAA,YACA,aAAA,YAjBF,QAAgC,QAAA,kBAChC,ShDgrHN,SgD9qHQ,YAAA,kBAEF,ShD+qHN,SgD7qHQ,aAAA,kBACA,cAAA,YAEF,ShD8qHN,SgD5qHQ,eAAA,kBAEF,ShD6qHN,SgD3qHQ,cAAA,kBACA,aAAA,YAjBF,QAAgC,QAAA,iBAChC,ShDgsHN,SgD9rHQ,YAAA,iBAEF,ShD+rHN,SgD7rHQ,aAAA,iBACA,cAAA,YAEF,ShD8rHN,SgD5rHQ,eAAA,iBAEF,ShD6rHN,SgD3rHQ,cAAA,iBACA,aAAA,YAjBF,QAAgC,QAAA,iBAChC,ShDgtHN,SgD9sHQ,YAAA,iBAEF,ShD+sHN,SgD7sHQ,aAAA,iBACA,cAAA,YAEF,ShD8sHN,SgD5sHQ,eAAA,iBAEF,ShD6sHN,SgD3sHQ,cAAA,iBACA,aAAA,YAjBF,QAAgC,QAAA,kBAChC,ShDguHN,SgD9tHQ,YAAA,kBAEF,ShD+tHN,SgD7tHQ,aAAA,kBACA,cAAA,YAEF,ShD8tHN,SgD5tHQ,eAAA,kBAEF,ShD6tHN,SgD3tHQ,cAAA,kBACA,aAAA,YAjBF,QAAgC,QAAA,iBAChC,ShDgvHN,SgD9uHQ,YAAA,iBAEF,ShD+uHN,SgD7uHQ,aAAA,iBACA,cAAA,YAEF,ShD8uHN,SgD5uHQ,eAAA,iBAEF,ShD6uHN,SgD3uHQ,cAAA,iBACA,aAAA,YAQF,SAAwB,OAAA,mBACxB,UhDuuHN,UgDruHQ,WAAA,mBAEF,UhDsuHN,UgDpuHQ,aAAA,mBAEF,UhDquHN,UgDnuHQ,cAAA,mBAEF,UhDouHN,UgDluHQ,YAAA,mBAfF,SAAwB,OAAA,kBACxB,UhDqvHN,UgDnvHQ,WAAA,kBAEF,UhDovHN,UgDlvHQ,aAAA,kBAEF,UhDmvHN,UgDjvHQ,cAAA,kBAEF,UhDkvHN,UgDhvHQ,YAAA,kBAfF,SAAwB,OAAA,kBACxB,UhDmwHN,UgDjwHQ,WAAA,kBAEF,UhDkwHN,UgDhwHQ,aAAA,kBAEF,UhDiwHN,UgD/vHQ,cAAA,kBAEF,UhDgwHN,UgD9vHQ,YAAA,kBAfF,SAAwB,OAAA,mBACxB,UhDixHN,UgD/wHQ,WAAA,mBAEF,UhDgxHN,UgD9wHQ,aAAA,mBAEF,UhD+wHN,UgD7wHQ,cAAA,mBAEF,UhD8wHN,UgD5wHQ,YAAA,mBAfF,SAAwB,OAAA,kBACxB,UhD+xHN,UgD7xHQ,WAAA,kBAEF,UhD8xHN,UgD5xHQ,aAAA,kBAEF,UhD6xHN,UgD3xHQ,cAAA,kBAEF,UhD4xHN,UgD1xHQ,YAAA,kBAMN,WAAmB,OAAA,eACnB,YhDwxHF,YgDtxHI,WAAA,eAEF,YhDuxHF,YgDrxHI,YAAA,eACA,aAAA,kBAEF,YhDsxHF,YgDpxHI,cAAA,eAEF,YhDqxHF,YgDnxHI,aAAA,eACA,YAAA,gB/CbF,yB+ClDI,QAAgC,OAAA,YAChC,ShDw1HN,SgDt1HQ,WAAA,YAEF,ShDu1HN,SgDr1HQ,YAAA,YACA,aAAA,YAEF,ShDs1HN,SgDp1HQ,cAAA,YAEF,ShDq1HN,SgDn1HQ,aAAA,YACA,YAAA,YAjBF,QAAgC,OAAA,kBAChC,ShDw2HN,SgDt2HQ,WAAA,kBAEF,ShDu2HN,SgDr2HQ,YAAA,kBACA,aAAA,YAEF,ShDs2HN,SgDp2HQ,cAAA,kBAEF,ShDq2HN,SgDn2HQ,aAAA,kBACA,YAAA,YAjBF,QAAgC,OAAA,iBAChC,ShDw3HN,SgDt3HQ,WAAA,iBAEF,ShDu3HN,SgDr3HQ,YAAA,iBACA,aAAA,YAEF,ShDs3HN,SgDp3HQ,cAAA,iBAEF,ShDq3HN,SgDn3HQ,aAAA,iBACA,YAAA,YAjBF,QAAgC,OAAA,iBAChC,ShDw4HN,SgDt4HQ,WAAA,iBAEF,ShDu4HN,SgDr4HQ,YAAA,iBACA,aAAA,YAEF,ShDs4HN,SgDp4HQ,cAAA,iBAEF,ShDq4HN,SgDn4HQ,aAAA,iBACA,YAAA,YAjBF,QAAgC,OAAA,kBAChC,ShDw5HN,SgDt5HQ,WAAA,kBAEF,ShDu5HN,SgDr5HQ,YAAA,kBACA,aAAA,YAEF,ShDs5HN,SgDp5HQ,cAAA,kBAEF,ShDq5HN,SgDn5HQ,aAAA,kBACA,YAAA,YAjBF,QAAgC,OAAA,iBAChC,ShDw6HN,SgDt6HQ,WAAA,iBAEF,ShDu6HN,SgDr6HQ,YAAA,iBACA,aAAA,YAEF,ShDs6HN,SgDp6HQ,cAAA,iBAEF,ShDq6HN,SgDn6HQ,aAAA,iBACA,YAAA,YAjBF,QAAgC,QAAA,YAChC,ShDw7HN,SgDt7HQ,YAAA,YAEF,ShDu7HN,SgDr7HQ,aAAA,YACA,cAAA,YAEF,ShDs7HN,SgDp7HQ,eAAA,YAEF,ShDq7HN,SgDn7HQ,cAAA,YACA,aAAA,YAjBF,QAAgC,QAAA,kBAChC,ShDw8HN,SgDt8HQ,YAAA,kBAEF,ShDu8HN,SgDr8HQ,aAAA,kBACA,cAAA,YAEF,ShDs8HN,SgDp8HQ,eAAA,kBAEF,ShDq8HN,SgDn8HQ,cAAA,kBACA,aAAA,YAjBF,QAAgC,QAAA,iBAChC,ShDw9HN,SgDt9HQ,YAAA,iBAEF,ShDu9HN,SgDr9HQ,aAAA,iBACA,cAAA,YAEF,ShDs9HN,SgDp9HQ,eAAA,iBAEF,ShDq9HN,SgDn9HQ,cAAA,iBACA,aAAA,YAjBF,QAAgC,QAAA,iBAChC,ShDw+HN,SgDt+HQ,YAAA,iBAEF,ShDu+HN,SgDr+HQ,aAAA,iBACA,cAAA,YAEF,ShDs+HN,SgDp+HQ,eAAA,iBAEF,ShDq+HN,SgDn+HQ,cAAA,iBACA,aAAA,YAjBF,QAAgC,QAAA,kBAChC,ShDw/HN,SgDt/HQ,YAAA,kBAEF,ShDu/HN,SgDr/HQ,aAAA,kBACA,cAAA,YAEF,ShDs/HN,SgDp/HQ,eAAA,kBAEF,ShDq/HN,SgDn/HQ,cAAA,kBACA,aAAA,YAjBF,QAAgC,QAAA,iBAChC,ShDwgIN,SgDtgIQ,YAAA,iBAEF,ShDugIN,SgDrgIQ,aAAA,iBACA,cAAA,YAEF,ShDsgIN,SgDpgIQ,eAAA,iBAEF,ShDqgIN,SgDngIQ,cAAA,iBACA,aAAA,YAQF,SAAwB,OAAA,mBACxB,UhD+/HN,UgD7/HQ,WAAA,mBAEF,UhD8/HN,UgD5/HQ,aAAA,mBAEF,UhD6/HN,UgD3/HQ,cAAA,mBAEF,UhD4/HN,UgD1/HQ,YAAA,mBAfF,SAAwB,OAAA,kBACxB,UhD6gIN,UgD3gIQ,WAAA,kBAEF,UhD4gIN,UgD1gIQ,aAAA,kBAEF,UhD2gIN,UgDzgIQ,cAAA,kBAEF,UhD0gIN,UgDxgIQ,YAAA,kBAfF,SAAwB,OAAA,kBACxB,UhD2hIN,UgDzhIQ,WAAA,kBAEF,UhD0hIN,UgDxhIQ,aAAA,kBAEF,UhDyhIN,UgDvhIQ,cAAA,kBAEF,UhDwhIN,UgDthIQ,YAAA,kBAfF,SAAwB,OAAA,mBACxB,UhDyiIN,UgDviIQ,WAAA,mBAEF,UhDwiIN,UgDtiIQ,aAAA,mBAEF,UhDuiIN,UgDriIQ,cAAA,mBAEF,UhDsiIN,UgDpiIQ,YAAA,mBAfF,SAAwB,OAAA,kBACxB,UhDujIN,UgDrjIQ,WAAA,kBAEF,UhDsjIN,UgDpjIQ,aAAA,kBAEF,UhDqjIN,UgDnjIQ,cAAA,kBAEF,UhDojIN,UgDljIQ,YAAA,kBAMN,WAAmB,OAAA,eACnB,YhDgjIF,YgD9iII,WAAA,eAEF,YhD+iIF,YgD7iII,YAAA,eACA,aAAA,kBAEF,YhD8iIF,YgD5iII,cAAA,eAEF,YhD6iIF,YgD3iII,aAAA,eACA,YAAA,gB/CbF,0B+ClDI,QAAgC,OAAA,YAChC,ShDgnIN,SgD9mIQ,WAAA,YAEF,ShD+mIN,SgD7mIQ,YAAA,YACA,aAAA,YAEF,ShD8mIN,SgD5mIQ,cAAA,YAEF,ShD6mIN,SgD3mIQ,aAAA,YACA,YAAA,YAjBF,QAAgC,OAAA,kBAChC,ShDgoIN,SgD9nIQ,WAAA,kBAEF,ShD+nIN,SgD7nIQ,YAAA,kBACA,aAAA,YAEF,ShD8nIN,SgD5nIQ,cAAA,kBAEF,ShD6nIN,SgD3nIQ,aAAA,kBACA,YAAA,YAjBF,QAAgC,OAAA,iBAChC,ShDgpIN,SgD9oIQ,WAAA,iBAEF,ShD+oIN,SgD7oIQ,YAAA,iBACA,aAAA,YAEF,ShD8oIN,SgD5oIQ,cAAA,iBAEF,ShD6oIN,SgD3oIQ,aAAA,iBACA,YAAA,YAjBF,QAAgC,OAAA,iBAChC,ShDgqIN,SgD9pIQ,WAAA,iBAEF,ShD+pIN,SgD7pIQ,YAAA,iBACA,aAAA,YAEF,ShD8pIN,SgD5pIQ,cAAA,iBAEF,ShD6pIN,SgD3pIQ,aAAA,iBACA,YAAA,YAjBF,QAAgC,OAAA,kBAChC,ShDgrIN,SgD9qIQ,WAAA,kBAEF,ShD+qIN,SgD7qIQ,YAAA,kBACA,aAAA,YAEF,ShD8qIN,SgD5qIQ,cAAA,kBAEF,ShD6qIN,SgD3qIQ,aAAA,kBACA,YAAA,YAjBF,QAAgC,OAAA,iBAChC,ShDgsIN,SgD9rIQ,WAAA,iBAEF,ShD+rIN,SgD7rIQ,YAAA,iBACA,aAAA,YAEF,ShD8rIN,SgD5rIQ,cAAA,iBAEF,ShD6rIN,SgD3rIQ,aAAA,iBACA,YAAA,YAjBF,QAAgC,QAAA,YAChC,ShDgtIN,SgD9sIQ,YAAA,YAEF,ShD+sIN,SgD7sIQ,aAAA,YACA,cAAA,YAEF,ShD8sIN,SgD5sIQ,eAAA,YAEF,ShD6sIN,SgD3sIQ,cAAA,YACA,aAAA,YAjBF,QAAgC,QAAA,kBAChC,ShDguIN,SgD9tIQ,YAAA,kBAEF,ShD+tIN,SgD7tIQ,aAAA,kBACA,cAAA,YAEF,ShD8tIN,SgD5tIQ,eAAA,kBAEF,ShD6tIN,SgD3tIQ,cAAA,kBACA,aAAA,YAjBF,QAAgC,QAAA,iBAChC,ShDgvIN,SgD9uIQ,YAAA,iBAEF,ShD+uIN,SgD7uIQ,aAAA,iBACA,cAAA,YAEF,ShD8uIN,SgD5uIQ,eAAA,iBAEF,ShD6uIN,SgD3uIQ,cAAA,iBACA,aAAA,YAjBF,QAAgC,QAAA,iBAChC,ShDgwIN,SgD9vIQ,YAAA,iBAEF,ShD+vIN,SgD7vIQ,aAAA,iBACA,cAAA,YAEF,ShD8vIN,SgD5vIQ,eAAA,iBAEF,ShD6vIN,SgD3vIQ,cAAA,iBACA,aAAA,YAjBF,QAAgC,QAAA,kBAChC,ShDgxIN,SgD9wIQ,YAAA,kBAEF,ShD+wIN,SgD7wIQ,aAAA,kBACA,cAAA,YAEF,ShD8wIN,SgD5wIQ,eAAA,kBAEF,ShD6wIN,SgD3wIQ,cAAA,kBACA,aAAA,YAjBF,QAAgC,QAAA,iBAChC,ShDgyIN,SgD9xIQ,YAAA,iBAEF,ShD+xIN,SgD7xIQ,aAAA,iBACA,cAAA,YAEF,ShD8xIN,SgD5xIQ,eAAA,iBAEF,ShD6xIN,SgD3xIQ,cAAA,iBACA,aAAA,YAQF,SAAwB,OAAA,mBACxB,UhDuxIN,UgDrxIQ,WAAA,mBAEF,UhDsxIN,UgDpxIQ,aAAA,mBAEF,UhDqxIN,UgDnxIQ,cAAA,mBAEF,UhDoxIN,UgDlxIQ,YAAA,mBAfF,SAAwB,OAAA,kBACxB,UhDqyIN,UgDnyIQ,WAAA,kBAEF,UhDoyIN,UgDlyIQ,aAAA,kBAEF,UhDmyIN,UgDjyIQ,cAAA,kBAEF,UhDkyIN,UgDhyIQ,YAAA,kBAfF,SAAwB,OAAA,kBACxB,UhDmzIN,UgDjzIQ,WAAA,kBAEF,UhDkzIN,UgDhzIQ,aAAA,kBAEF,UhDizIN,UgD/yIQ,cAAA,kBAEF,UhDgzIN,UgD9yIQ,YAAA,kBAfF,SAAwB,OAAA,mBACxB,UhDi0IN,UgD/zIQ,WAAA,mBAEF,UhDg0IN,UgD9zIQ,aAAA,mBAEF,UhD+zIN,UgD7zIQ,cAAA,mBAEF,UhD8zIN,UgD5zIQ,YAAA,mBAfF,SAAwB,OAAA,kBACxB,UhD+0IN,UgD70IQ,WAAA,kBAEF,UhD80IN,UgD50IQ,aAAA,kBAEF,UhD60IN,UgD30IQ,cAAA,kBAEF,UhD40IN,UgD10IQ,YAAA,kBAMN,WAAmB,OAAA,eACnB,YhDw0IF,YgDt0II,WAAA,eAEF,YhDu0IF,YgDr0II,YAAA,eACA,aAAA,kBAEF,YhDs0IF,YgDp0II,cAAA,eAEF,YhDq0IF,YgDn0II,aAAA,eACA,YAAA,gBCnEF,YAAwB,MAAA,gBACxB,aAAwB,MAAA,eACxB,YAAwB,MAAA,ehDoDxB,yBgDtDA,eAAwB,MAAA,gBACxB,gBAAwB,MAAA,eACxB,eAAwB,MAAA,gBhDoDxB,yBgDtDA,eAAwB,MAAA,gBACxB,gBAAwB,MAAA,eACxB,eAAwB,MAAA,gBhDoDxB,yBgDtDA,eAAwB,MAAA,gBACxB,gBAAwB,MAAA,eACxB,eAAwB,MAAA,gBhDoDxB,0BgDtDA,eAAwB,MAAA,gBACxB,gBAAwB,MAAA,eACxB,eAAwB,MAAA,gBCF5B,gBAAkB,YAAA,cAAA,CAAA,KAAA,CAAA,MAAA,CAAA,QAAA,CAAA,iBAAA,CAAA,aAAA,CAAA,oBAIlB,cAAiB,WAAA,kBACjB,WAAiB,YAAA,iBACjB,aAAiB,YAAA,iBACjB,eCTE,SAAA,OACA,cAAA,SACA,YAAA,ODeE,WAAwB,WAAA,gBACxB,YAAwB,WAAA,eACxB,aAAwB,WAAA,iBjDqCxB,yBiDvCA,cAAwB,WAAA,gBACxB,eAAwB,WAAA,eACxB,gBAAwB,WAAA,kBjDqCxB,yBiDvCA,cAAwB,WAAA,gBACxB,eAAwB,WAAA,eACxB,gBAAwB,WAAA,kBjDqCxB,yBiDvCA,cAAwB,WAAA,gBACxB,eAAwB,WAAA,eACxB,gBAAwB,WAAA,kBjDqCxB,0BiDvCA,cAAwB,WAAA,gBACxB,eAAwB,WAAA,eACxB,gBAAwB,WAAA,kBAM5B,gBAAmB,eAAA,oBACnB,gBAAmB,eAAA,oBACnB,iBAAmB,eAAA,qBAInB,mBAAuB,YAAA,cACvB,qBAAuB,YAAA,kBACvB,oBAAuB,YAAA,cACvB,kBAAuB,YAAA,cACvB,oBAAuB,YAAA,iBACvB,aAAuB,WAAA,iBAIvB,YAAc,MAAA,eEvCZ,cACE,MAAA,kBCUF,qBAAA,qBDLM,MAAA,kBANN,gBACE,MAAA,kBCUF,uBAAA,uBDLM,MAAA,kBANN,cACE,MAAA,kBCUF,qBAAA,qBDLM,MAAA,kBANN,WACE,MAAA,kBCUF,kBAAA,kBDLM,MAAA,kBANN,cACE,MAAA,kBCUF,qBAAA,qBDLM,MAAA,kBANN,aACE,MAAA,kBCUF,oBAAA,oBDLM,MAAA,kBANN,YACE,MAAA,kBCUF,mBAAA,mBDLM,MAAA,kBANN,WACE,MAAA,kBCUF,kBAAA,kBDLM,MAAA,kBANN,WACE,MAAA,kBCUF,kBAAA,kBDLM,MAAA,kBANN,aACE,MAAA,kBCUF,oBAAA,oBDLM,MAAA,kBFuCR,WAAa,MAAA,kBACb,YAAc,MAAA,kBAEd,eAAiB,MAAA,yBACjB,eAAiB,MAAA,+BAIjB,WIvDE,KAAA,CAAA,CAAA,EAAA,EACA,MAAA,YACA,YAAA,KACA,iBAAA,YACA,OAAA,EJuDF,sBAAwB,gBAAA,eAExB,YACE,WAAA,qBACA,cAAA,qBAKF,YAAc,MAAA,kBKhEd,UACI,MAAA,MAKJ,eACI,QAAA,EAAA,EAAA,EAAA,KADJ,2BAGQ,YAAA,KACA,aAAA,EAJR,+CAMY,aAAA,EANZ,gCASY,cAAA,KAAA,EAAA,EAAA,eATZ,yCAYY,cAAA,KACA,aAAA,EACA,cAAA,EAAA,KAAA,KAAA,YAdZ,+BAmBY,MAAA,MAnBZ,sCAuBY,UAAA,IAUZ,oCAGQ,KAAA,KACA,MAAA,KAJR,gCAQQ,QAAA,KAAA,KARR,6CAWY,MAAA,MACA,YAAA,KACA,aAAA,EAbZ,gDvD4lJE,0CuD1kJU,YAAA,EACA,aAAA,KAnBZ,kCAwBQ,YAAA,kBACA,aAAA,YAzBR,uCA8BY,YAAA,IACA,aAAA,EACA,MAAA,MAhCZ,kDAmCY,QAAA,IAAA,KAOZ,kCAEQ,MAAA,KAOR,cACI,aAAA,MACA,YAAA,EAIJ,wBAKoB,OAAA,EAAA,IAAA,EAAA,KALpB,kCASoB,MAAA,KATpB,oCAYwB,YAAA,EAZxB,uBAkBgB,cAAA,KACA,aAAA,EAnBhB,0BAsBoB,cAAA,KACA,aAAA,EAvBpB,0BA8BQ,KAAA,KACA,MAAA,KA/BR,iCAkCY,QAAA,QAlCZ,6CAyCgB,kBAAA,eAAA,UAAA,eAOhB,kDAa4B,YAAA,KACA,aAAA,IAd5B,qDAkB4B,cAAA,KACA,aAAA,EAnB5B,uDA0B4B,MAAA,KACA,KAAA,KA3B5B,0DAqCgC,MAAA,MACA,WAAA,MAtChC,mEA4CgC,KAAA,KACA,MAAA,EA7ChC,yBAyDQ,MAAA,EAzDR,wBA8DQ,aAAA,eACA,YAAA,YA/DR,kBAoEQ,KAAA,YACA,MAAA,etDnJJ,4BsDyJA,cvDy/IF,wBuDv/IM,aAAA,aAOR,QACI,KAAA,EACA,MAAA,MAGJ,YACI,MAAA,YtDxKA,4BsD4KA,QACI,MAAA,aAOR,WACI,MAAA,eACA,KAAA,OACA,MAAA,KAHJ,0CAQgB,MAAA,EACA,KAAA,KAMhB,8BAEQ,KAAA,EACA,MAAA,KCrQR,aACI,cAAA,cACA,aAAA,KAKJ,2EAGY,cAAA,KAHZ,wEAOY,KAAA,IACA,MAAA,KARZ,2EAcY,MAAA,MACA,YAAA,IACA,aAAA,EAhBZ,2CAqBQ,MAAA,MAOR,0CAGY,MAAA,KAHZ,kDAOY,WAAA,MAPZ,wDAWY,cAAA,EACA,aAAA,QAOZ,uBAGY,YAAA,KAOZ,kBACI,YAAA,EACA,aAAA,IAFJ,mCAKQ,YAAA,EACA,aAAA,IAMR,uCAEQ,WAAA,eAFR,6CAIY,YAAA,YACA,aAAA,KAOZ,6DAIgB,aAAA,EACA,YAAA,KAMhB,kDAGY,YAAA,EACA,aAAA,KAOZ,wCAEQ,MAAA,KAFR,4CAOgB,YAAA,MACA,IAAA,KAQhB,mEACI,KAAA,EACA,MAAA,KAGJ,WACI,UAAA,IACA,WAAA,MCxIJ,0CAGM,MAAA,MACA,YAAA,KACA,aAAA,EALN,2CASM,MAAA,KACA,KAAA,IASN,mDAGM,KAAA,IACA,MAAA,KAQN,6BAEI,cAAA,KAFJ,8BAMI,aAAA,KACA,YAAA,EASJ,mBAEI,YAAA,KACA,aAAA,EAMJ,gBAEI,cAAA,IACA,aAAA,EAHJ,wBAMM,KAAA,KACA,MAAA,EACA,YAAA,EACA,aAAA,MATN,uBAaM,KAAA,KACA,MAAA,EACA,aAAA,MACA,YAAA,EACA,aAAA,EACA,cAAA,IAlBN,oDAwBM,KAAA,KACA,MAAA,IACA,kBAAA,cAAA,UAAA,cAKN,uCAGM,aAAA,EAHN,sCAOM,aAAA,EAQN,aAEI,aAAA,EACA,cAAA,IAHJ,qBAMM,KAAA,KACA,MAAA,EACA,YAAA,EACA,aAAA,MATN,oBAaM,KAAA,EACA,MAAA,IACA,YAAA,EACA,aAAA,MAON,azDyvJE,SyDxvJA,czDyvJA,KyDtvJI,aAAA,GCpIN,eACI,MAAA,MACA,QAAA,EAAA,KAAA,KAAA,KAGJ,gBACI,aAAA,MACA,YAAA,EAGJ,cACI,cAAA,EADJ,wBAGQ,MAAA,gB1D63JN,iDACA,+B0Dj4JF,uCASY,MAAA,gBATZ,+BAYY,OAAA,KAAA,EAAA,EAAA,KAZZ,iDAeY,OAAA,KAAA,KAAA,EAAA,eAfZ,uCAkBY,aAAA,IAlBZ,iCAqBY,MAAA,gBACA,KAAA,YAtBZ,0BA0BQ,MAAA,gBACA,KAAA,YA3BR,mCA6BY,MAAA,YACA,KAAA,gBA9BZ,gCAiCY,KAAA,EACA,cAAA,KACA,aAAA,YACA,MAAA,eAKZ,yBACI,gBACI,aAAA,GAIR,yBACI,oCAGY,MAAA,eACA,KAAA,YAJZ,6BAQQ,MAAA,gBARR,mCAUY,WAAA,KACA,aAAA,eACA,cAAA,MAYhB,qBAEQ,YAAA,MAFR,iBAKQ,MAAA,IALR,yBAQQ,MAAA,MARR,2BAUY,MAAA,IAKZ,kBACI,OAAA,EAAA,EAAA,IAGJ,qCAGY,YAAA,KAAA,MAAA,+BACA,MAAA,MAKZ,6CAGY,aAAA,KAAA,MAAA,+BACA,MAAA,KACA,KAAA,MACA,YAAA,eANZ,yCASY,MAAA,KATZ,2CAWgB,MAAA,KACA,aAAA,IAZhB,kCAiBQ,MAAA,KACA,KAAA,MAlBR,0BAqBQ,aAAA,EACA,YAAA,KAtBR,sBAyBQ,WAAA,KAzBR,qBA4BQ,WAAA,KA5BR,kCA+BQ,WAAA,KAIR,sBAEQ,aAAA,KACA,WAAA,MACA,YAAA,EAJR,wBAQY,MAAA,MACA,YAAA,IAMZ,UACI,SAAA,SACA,KAAA,KACA,MAAA", "file": "app-rtl.min.css", "sourcesContent": ["//\r\n// Google font - Roboto & Nunito Sans\r\n//\r\n\r\n@import url('https://fonts.googleapis.com/css?family=Nunito+Sans:400,600,700|Roboto:300,400,500,700&display=swap');", "// \r\n// general.scss\r\n//\r\n\r\nhtml {\r\n  position: relative;\r\n  min-height: 100%;\r\n}\r\n\r\nbody {\r\n  padding-bottom: 60px;\r\n  overflow-x: hidden;\r\n}", "// \r\n// menu.scss\r\n//\r\n\r\n// Metis Menu Overwrite\r\n\r\n.metismenu {\r\n    padding: 0;\r\n\r\n    li {\r\n        list-style: none;\r\n    }\r\n    ul {\r\n        padding: 0;\r\n        li {\r\n            width: 100%;\r\n        }\r\n    }\r\n\r\n    .mm-collapse:not(.mm-show) {\r\n        display: none;\r\n    }\r\n    .mm-collapsing {\r\n        position: relative;\r\n        height: 0;\r\n        overflow: hidden;\r\n        transition-timing-function: ease;\r\n        transition-duration: .35s;\r\n        transition-property: height, visibility;\r\n    }\r\n}\r\n\r\n.nav-second-level,\r\n.nav-thrid-level {\r\n    li {\r\n        a {\r\n            padding: 8px 20px;\r\n            color: $menu-item;\r\n            display: block;\r\n            position: relative;\r\n            transition: all 0.4s;\r\n            &:focus,\r\n            &:hover {\r\n                color: $menu-item-hover;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.nav-second-level,\r\n.nav-third-level {\r\n    li.mm-active {\r\n        >a {\r\n            color: $menu-item-active;\r\n        }\r\n    }\r\n}\r\n\r\n// Wrapper\r\n#wrapper {\r\n    height: 100%;\r\n    overflow: hidden;\r\n    width: 100%;\r\n}\r\n\r\n//Content Page\r\n.content-page {\r\n    margin-left: $leftbar-width;\r\n    overflow: hidden;\r\n    padding: 0 15px 5px 15px;\r\n    min-height: 80vh;\r\n    margin-top: $topbar-height;\r\n}\r\n\r\n// Sidemenu\r\n.left-side-menu {\r\n    width: $leftbar-width;\r\n    background: $bg-leftbar-light;\r\n    bottom: 0;\r\n    padding: 20px 0;\r\n    position: fixed;\r\n    transition: all .2s ease-out;\r\n    top: $topbar-height;\r\n    box-shadow: $box-shadow;\r\n    z-index: 10;\r\n}\r\n\r\n// Sidebar\r\n#sidebar-menu {\r\n    >ul {\r\n        >li {\r\n            >a {\r\n                color: $menu-item;\r\n                display: block;\r\n                padding: 13px 20px;\r\n                position: relative;\r\n                transition: all 0.4s;\r\n                font-family: $font-family-secondary;\r\n                font-size: 15px;\r\n\r\n                &:hover,\r\n                &:focus,\r\n                &:active {\r\n                    color: $menu-item-hover;\r\n                    text-decoration: none;\r\n                    background: $light;\r\n                }\r\n                >span {\r\n                    vertical-align: middle;\r\n                }\r\n\r\n                i {\r\n                    display: inline-block;\r\n                    line-height: 1.0625rem;\r\n                    margin: 0 10px 0 3px;\r\n                    text-align: center;\r\n                    vertical-align: middle;\r\n                    width: 20px;\r\n                    font-size: 16px;\r\n                }\r\n                .drop-arrow {\r\n                    float: right;\r\n                    i {\r\n                        margin-right: 0;\r\n                    }\r\n                }\r\n            }\r\n            > a.active {\r\n                color: $menu-item-active;\r\n                background: $light;\r\n            }\r\n\r\n            > ul {\r\n                padding-left: 37px;\r\n\r\n                ul {\r\n                    padding-left: 20px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .menu-arrow {\r\n        transition: transform .15s;\r\n        position: absolute;\r\n        right: 20px;\r\n        display: inline-block;\r\n        font-family: 'Material Design Icons';\r\n        text-rendering: auto;\r\n        line-height: 1.5rem;\r\n        font-size: 1.1rem;\r\n        transform: translate(0, 0);\r\n        &:before {\r\n            content: \"\\F142\";\r\n        }\r\n    }\r\n    .badge{\r\n        margin-top: 4px;\r\n    }\r\n\r\n    li.mm-active {\r\n        > a {\r\n            > span.menu-arrow {\r\n                transform: rotate(90deg);\r\n            }\r\n        }\r\n    }\r\n\r\n    .menu-title {\r\n        padding: 10px 20px;\r\n        letter-spacing: .05em;\r\n        pointer-events: none;\r\n        cursor: default;\r\n        font-size: 0.6875rem;\r\n        text-transform: uppercase;\r\n        color: $menu-item;\r\n        font-weight: $font-weight-semibold;\r\n    }\r\n}\r\n\r\n// Enlarge menu\r\n.enlarged {\r\n\r\n    .logo-box {\r\n        width: $leftbar-width-collapsed !important;\r\n    }\r\n\r\n    .logo {\r\n        span.logo-lg {\r\n            display: none;\r\n        }\r\n        span.logo-sm {\r\n            display: block;\r\n        }\r\n    }\r\n    // Side menu\r\n    .left-side-menu {\r\n        position: absolute;\r\n        padding-top: 0;\r\n        width: $leftbar-width-collapsed !important;\r\n        z-index: 5;\r\n\r\n        .slimScrollDiv,\r\n        .slimscroll-menu {\r\n            overflow: inherit !important;\r\n            height: auto !important;\r\n        }\r\n        .slimScrollBar {\r\n            visibility: hidden;\r\n        }\r\n\r\n        // Sidebar Menu\r\n        #sidebar-menu {\r\n            .menu-title,\r\n            .menu-arrow,\r\n            .label,\r\n            .badge{\r\n                display: none !important;\r\n            }\r\n\r\n            > ul {\r\n                > li {\r\n                    position: relative;\r\n                    white-space: nowrap;\r\n\r\n                    > a {\r\n                        padding: 15px 20px;\r\n                        min-height: 54px;\r\n                        transition: none;\r\n            \r\n                        &:hover,\r\n                        &:active,\r\n                        &:focus {\r\n                            color: $menu-item-hover;\r\n                        }\r\n                        i {\r\n                            font-size: 1.125rem;\r\n                            margin-right: 20px;\r\n                        }\r\n\r\n                        span {\r\n                            display: none;\r\n                            padding-left: 25px;\r\n                        }\r\n                    }\r\n                    \r\n                    &:hover  {\r\n                        > a {\r\n                            position: relative;\r\n                            width: calc(190px + #{$leftbar-width-collapsed});\r\n                            color: $menu-item-active;\r\n                            background-color: $light;\r\n                            transition: none;\r\n\r\n                            span {\r\n                                display: inline;\r\n                            }\r\n                        }\r\n\r\n                        a.open,a.mm-active {\r\n                            :after {\r\n                                display: none;\r\n                            }\r\n                        }\r\n\r\n                        > ul {\r\n                            display: block;\r\n                            left: $leftbar-width-collapsed;\r\n                            position: absolute;\r\n                            width: 190px;\r\n                            height: auto !important;\r\n                            box-shadow: 3px 5px 10px 0 rgba(154,161,171,.2);\r\n\r\n                            ul {\r\n                                box-shadow: 3px 5px 10px 0 rgba(154,161,171,.2);\r\n                            }\r\n                            a {\r\n                                box-shadow: none;\r\n                                padding: 8px 20px;\r\n                                position: relative;\r\n                                width: 190px;\r\n                                z-index: 6;\r\n                                &:hover {\r\n                                    color: $menu-item-hover;\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n\r\n                ul {\r\n                    padding: 5px 0;\r\n                    z-index: 9999;\r\n                    display: none;\r\n                    background-color: $bg-leftbar-light;\r\n\r\n                    li {\r\n                        &:hover {\r\n                            > ul {\r\n                                display: block;\r\n                                left: 190px;\r\n                                margin-top: -36px;\r\n                                height: auto !important;\r\n                                position: absolute;\r\n                                width: 190px;\r\n                            }\r\n                        }\r\n\r\n                        > a {\r\n                            span.pull-right {\r\n                                position: absolute;\r\n                                right: 20px;\r\n                                top: 12px;\r\n                                transform: rotate(270deg);\r\n                            }\r\n                        }\r\n                    }\r\n                    li.active {\r\n                        a {\r\n                            color: $menu-item-active;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // Content Page\r\n    .content-page {\r\n        margin-left: $leftbar-width-collapsed !important;\r\n    }\r\n\r\n    //Footer\r\n    .footer {\r\n        left: $leftbar-width-collapsed !important;\r\n    }\r\n\r\n    //User box\r\n    .user-box {\r\n        display: none;\r\n    }\r\n}\r\n\r\n// Body min-height set\r\nbody.enlarged {\r\n    min-height: 1200px;\r\n}\r\n\r\n@include media-breakpoint-down(sm) {\r\n    body {\r\n        overflow-x: hidden;\r\n        padding-bottom: 80px;\r\n    }\r\n    .left-side-menu {\r\n        display: none;\r\n        z-index: 999 !important;\r\n    }\r\n    .sidebar-enable {\r\n        .left-side-menu {\r\n            display: block;\r\n        }\r\n    }\r\n    .content-page,.enlarged .content-page {\r\n        margin-left: 0 !important;\r\n        padding: 0 10px;\r\n    }\r\n    .pro-user-name {\r\n        display: none;\r\n    }\r\n    .logo-box {\r\n        display: none;\r\n    }\r\n}", "/*\r\nTemplate Name: Abstack - Responsive Bootstrap 4 Admin Dashboard\r\nAuthor: CoderThemes\r\nVersion: 2.0.0\r\nWebsite: https://coderthemes.com/\r\nContact: <EMAIL>\r\nFile: Main Css File\r\n*/\n@import url(\"https://fonts.googleapis.com/css?family=Nunito+Sans:400,600,700|Roboto:300,400,500,700&display=swap\");\nhtml {\n  position: relative;\n  min-height: 100%; }\n\nbody {\n  padding-bottom: 60px;\n  overflow-x: hidden; }\n\n.metismenu {\n  padding: 0; }\n  .metismenu li {\n    list-style: none; }\n  .metismenu ul {\n    padding: 0; }\n    .metismenu ul li {\n      width: 100%; }\n  .metismenu .mm-collapse:not(.mm-show) {\n    display: none; }\n  .metismenu .mm-collapsing {\n    position: relative;\n    height: 0;\n    overflow: hidden;\n    -webkit-transition-timing-function: ease;\n            transition-timing-function: ease;\n    -webkit-transition-duration: .35s;\n            transition-duration: .35s;\n    -webkit-transition-property: height, visibility;\n    transition-property: height, visibility; }\n\n.nav-second-level li a,\n.nav-thrid-level li a {\n  padding: 8px 20px;\n  color: #6e768e;\n  display: block;\n  position: relative;\n  -webkit-transition: all 0.4s;\n  transition: all 0.4s; }\n  .nav-second-level li a:focus, .nav-second-level li a:hover,\n  .nav-thrid-level li a:focus,\n  .nav-thrid-level li a:hover {\n    color: #348cd4; }\n\n.nav-second-level li.mm-active > a,\n.nav-third-level li.mm-active > a {\n  color: #348cd4; }\n\n#wrapper {\n  height: 100%;\n  overflow: hidden;\n  width: 100%; }\n\n.content-page {\n  margin-left: 240px;\n  overflow: hidden;\n  padding: 0 15px 5px 15px;\n  min-height: 80vh;\n  margin-top: 70px; }\n\n.left-side-menu {\n  width: 240px;\n  background: #fff;\n  bottom: 0;\n  padding: 20px 0;\n  position: fixed;\n  -webkit-transition: all .2s ease-out;\n  transition: all .2s ease-out;\n  top: 70px;\n  -webkit-box-shadow: 0px 0px 35px 0px rgba(154, 161, 171, 0.15);\n          box-shadow: 0px 0px 35px 0px rgba(154, 161, 171, 0.15);\n  z-index: 10; }\n\n#sidebar-menu > ul > li > a {\n  color: #6e768e;\n  display: block;\n  padding: 13px 20px;\n  position: relative;\n  -webkit-transition: all 0.4s;\n  transition: all 0.4s;\n  font-family: \"Roboto\", sans-serif;\n  font-size: 15px; }\n  #sidebar-menu > ul > li > a:hover, #sidebar-menu > ul > li > a:focus, #sidebar-menu > ul > li > a:active {\n    color: #348cd4;\n    text-decoration: none;\n    background: #f7f7f7; }\n  #sidebar-menu > ul > li > a > span {\n    vertical-align: middle; }\n  #sidebar-menu > ul > li > a i {\n    display: inline-block;\n    line-height: 1.0625rem;\n    margin: 0 10px 0 3px;\n    text-align: center;\n    vertical-align: middle;\n    width: 20px;\n    font-size: 16px; }\n  #sidebar-menu > ul > li > a .drop-arrow {\n    float: right; }\n    #sidebar-menu > ul > li > a .drop-arrow i {\n      margin-right: 0; }\n\n#sidebar-menu > ul > li > a.active {\n  color: #348cd4;\n  background: #f7f7f7; }\n\n#sidebar-menu > ul > li > ul {\n  padding-left: 37px; }\n  #sidebar-menu > ul > li > ul ul {\n    padding-left: 20px; }\n\n#sidebar-menu .menu-arrow {\n  -webkit-transition: -webkit-transform .15s;\n  transition: -webkit-transform .15s;\n  transition: transform .15s;\n  transition: transform .15s, -webkit-transform .15s;\n  position: absolute;\n  right: 20px;\n  display: inline-block;\n  font-family: 'Material Design Icons';\n  text-rendering: auto;\n  line-height: 1.5rem;\n  font-size: 1.1rem;\n  -webkit-transform: translate(0, 0);\n          transform: translate(0, 0); }\n  #sidebar-menu .menu-arrow:before {\n    content: \"\\F142\"; }\n\n#sidebar-menu .badge {\n  margin-top: 4px; }\n\n#sidebar-menu li.mm-active > a > span.menu-arrow {\n  -webkit-transform: rotate(90deg);\n          transform: rotate(90deg); }\n\n#sidebar-menu .menu-title {\n  padding: 10px 20px;\n  letter-spacing: .05em;\n  pointer-events: none;\n  cursor: default;\n  font-size: 0.6875rem;\n  text-transform: uppercase;\n  color: #6e768e;\n  font-weight: 600; }\n\n.enlarged .logo-box {\n  width: 70px !important; }\n\n.enlarged .logo span.logo-lg {\n  display: none; }\n\n.enlarged .logo span.logo-sm {\n  display: block; }\n\n.enlarged .left-side-menu {\n  position: absolute;\n  padding-top: 0;\n  width: 70px !important;\n  z-index: 5; }\n  .enlarged .left-side-menu .slimScrollDiv,\n  .enlarged .left-side-menu .slimscroll-menu {\n    overflow: inherit !important;\n    height: auto !important; }\n  .enlarged .left-side-menu .slimScrollBar {\n    visibility: hidden; }\n  .enlarged .left-side-menu #sidebar-menu .menu-title,\n  .enlarged .left-side-menu #sidebar-menu .menu-arrow,\n  .enlarged .left-side-menu #sidebar-menu .label,\n  .enlarged .left-side-menu #sidebar-menu .badge {\n    display: none !important; }\n  .enlarged .left-side-menu #sidebar-menu > ul > li {\n    position: relative;\n    white-space: nowrap; }\n    .enlarged .left-side-menu #sidebar-menu > ul > li > a {\n      padding: 15px 20px;\n      min-height: 54px;\n      -webkit-transition: none;\n      transition: none; }\n      .enlarged .left-side-menu #sidebar-menu > ul > li > a:hover, .enlarged .left-side-menu #sidebar-menu > ul > li > a:active, .enlarged .left-side-menu #sidebar-menu > ul > li > a:focus {\n        color: #348cd4; }\n      .enlarged .left-side-menu #sidebar-menu > ul > li > a i {\n        font-size: 1.125rem;\n        margin-right: 20px; }\n      .enlarged .left-side-menu #sidebar-menu > ul > li > a span {\n        display: none;\n        padding-left: 25px; }\n    .enlarged .left-side-menu #sidebar-menu > ul > li:hover > a {\n      position: relative;\n      width: calc(190px + 70px);\n      color: #348cd4;\n      background-color: #f7f7f7;\n      -webkit-transition: none;\n      transition: none; }\n      .enlarged .left-side-menu #sidebar-menu > ul > li:hover > a span {\n        display: inline; }\n    .enlarged .left-side-menu #sidebar-menu > ul > li:hover a.open :after, .enlarged .left-side-menu #sidebar-menu > ul > li:hover a.mm-active :after {\n      display: none; }\n    .enlarged .left-side-menu #sidebar-menu > ul > li:hover > ul {\n      display: block;\n      left: 70px;\n      position: absolute;\n      width: 190px;\n      height: auto !important;\n      -webkit-box-shadow: 3px 5px 10px 0 rgba(154, 161, 171, 0.2);\n              box-shadow: 3px 5px 10px 0 rgba(154, 161, 171, 0.2); }\n      .enlarged .left-side-menu #sidebar-menu > ul > li:hover > ul ul {\n        -webkit-box-shadow: 3px 5px 10px 0 rgba(154, 161, 171, 0.2);\n                box-shadow: 3px 5px 10px 0 rgba(154, 161, 171, 0.2); }\n      .enlarged .left-side-menu #sidebar-menu > ul > li:hover > ul a {\n        -webkit-box-shadow: none;\n                box-shadow: none;\n        padding: 8px 20px;\n        position: relative;\n        width: 190px;\n        z-index: 6; }\n        .enlarged .left-side-menu #sidebar-menu > ul > li:hover > ul a:hover {\n          color: #348cd4; }\n  .enlarged .left-side-menu #sidebar-menu > ul ul {\n    padding: 5px 0;\n    z-index: 9999;\n    display: none;\n    background-color: #fff; }\n    .enlarged .left-side-menu #sidebar-menu > ul ul li:hover > ul {\n      display: block;\n      left: 190px;\n      margin-top: -36px;\n      height: auto !important;\n      position: absolute;\n      width: 190px; }\n    .enlarged .left-side-menu #sidebar-menu > ul ul li > a span.pull-right {\n      position: absolute;\n      right: 20px;\n      top: 12px;\n      -webkit-transform: rotate(270deg);\n              transform: rotate(270deg); }\n    .enlarged .left-side-menu #sidebar-menu > ul ul li.active a {\n      color: #348cd4; }\n\n.enlarged .content-page {\n  margin-left: 70px !important; }\n\n.enlarged .footer {\n  left: 70px !important; }\n\n.enlarged .user-box {\n  display: none; }\n\nbody.enlarged {\n  min-height: 1200px; }\n\n@media (max-width: 767.98px) {\n  body {\n    overflow-x: hidden;\n    padding-bottom: 80px; }\n  .left-side-menu {\n    display: none;\n    z-index: 999 !important; }\n  .sidebar-enable .left-side-menu {\n    display: block; }\n  .content-page, .enlarged .content-page {\n    margin-left: 0 !important;\n    padding: 0 10px; }\n  .pro-user-name {\n    display: none; }\n  .logo-box {\n    display: none; } }\n\n.logo {\n  display: block;\n  line-height: 70px; }\n  .logo span.logo-lg {\n    display: block; }\n  .logo span.logo-sm {\n    display: none; }\n  .logo .logo-lg-text-dark {\n    color: #323a46;\n    font-weight: 700;\n    font-size: 22px;\n    text-transform: uppercase; }\n  .logo .logo-lg-text-light {\n    color: #fff;\n    font-weight: 700;\n    font-size: 22px;\n    text-transform: uppercase; }\n\n.logo-box {\n  height: 70px;\n  width: 240px;\n  float: left; }\n\n.navbar-custom {\n  background: #348cd4;\n  -webkit-box-shadow: 0px 0px 35px 0px rgba(154, 161, 171, 0.15);\n          box-shadow: 0px 0px 35px 0px rgba(154, 161, 171, 0.15);\n  padding: 0 10px 0 0;\n  position: fixed;\n  left: 0;\n  right: 0;\n  height: 70px;\n  z-index: 100;\n  /* Search */ }\n  .navbar-custom .topnav-menu > li {\n    float: left; }\n  .navbar-custom .topnav-menu .nav-link {\n    padding: 0 15px;\n    color: rgba(255, 255, 255, 0.6);\n    min-width: 32px;\n    display: block;\n    line-height: 70px;\n    text-align: center;\n    max-height: 70px; }\n  .navbar-custom .dropdown.show .nav-link {\n    background-color: rgba(255, 255, 255, 0.05); }\n  .navbar-custom .app-search {\n    overflow: hidden;\n    height: 70px;\n    display: table;\n    max-width: 200px;\n    margin-right: 20px; }\n    .navbar-custom .app-search .app-search-box {\n      display: table-cell;\n      vertical-align: middle; }\n      .navbar-custom .app-search .app-search-box input::-webkit-input-placeholder {\n        font-size: 0.8125rem;\n        color: rgba(255, 255, 255, 0.7); }\n    .navbar-custom .app-search .form-control {\n      border: none;\n      height: 38px;\n      padding-left: 20px;\n      padding-right: 0;\n      color: #fff;\n      background-color: rgba(255, 255, 255, 0.07);\n      -webkit-box-shadow: none;\n              box-shadow: none;\n      border-radius: 30px 0 0 30px; }\n    .navbar-custom .app-search .input-group-append {\n      margin-left: 0;\n      z-index: 4; }\n    .navbar-custom .app-search .btn {\n      background-color: rgba(255, 255, 255, 0.07);\n      color: #fff;\n      border-color: transparent;\n      border-radius: 0 30px 30px 0;\n      -webkit-box-shadow: none !important;\n              box-shadow: none !important; }\n  .navbar-custom .button-menu-mobile {\n    border: none;\n    color: #fff;\n    display: inline-block;\n    height: 70px;\n    line-height: 70px;\n    width: 60px;\n    background-color: transparent;\n    font-size: 24px;\n    cursor: pointer; }\n  .navbar-custom .button-menu-mobile.disable-btn {\n    display: none; }\n\n/* Notification */\n.noti-scroll {\n  max-height: 230px; }\n\n.notification-list {\n  margin-left: 0; }\n  .notification-list .noti-title {\n    background-color: #fff;\n    padding: 15px 20px; }\n  .notification-list .noti-icon {\n    font-size: 21px;\n    vertical-align: middle; }\n  .notification-list .noti-icon-badge {\n    display: inline-block;\n    position: absolute;\n    top: 16px;\n    right: 10px; }\n  .notification-list .notify-item {\n    padding: 12px 20px; }\n    .notification-list .notify-item .notify-icon {\n      float: left;\n      height: 36px;\n      width: 36px;\n      font-size: 18px;\n      line-height: 36px;\n      text-align: center;\n      margin-top: 4px;\n      margin-right: 10px;\n      border-radius: 50%;\n      color: #fff; }\n    .notification-list .notify-item .notify-details {\n      margin-bottom: 5px;\n      overflow: hidden;\n      margin-left: 45px;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n      color: #414d5f;\n      font-weight: 500; }\n      .notification-list .notify-item .notify-details b {\n        font-weight: 500; }\n      .notification-list .notify-item .notify-details small {\n        display: block; }\n      .notification-list .notify-item .notify-details span {\n        display: block;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        white-space: nowrap;\n        font-size: 13px; }\n    .notification-list .notify-item .user-msg {\n      margin-left: 45px;\n      white-space: normal;\n      line-height: 16px; }\n  .notification-list .profile-dropdown .notify-item {\n    padding: 7px 20px; }\n\n.profile-dropdown {\n  width: 170px; }\n  .profile-dropdown i {\n    vertical-align: middle;\n    margin-right: 5px; }\n\n.nav-user {\n  padding: 0 12px !important; }\n  .nav-user img {\n    height: 32px;\n    width: 32px; }\n\n.page-title-box {\n  padding: 0px 20px;\n  margin: 0 -27px 30px -27px;\n  background-color: #fff;\n  -webkit-box-shadow: 0px 0px 35px 0px rgba(154, 161, 171, 0.15);\n          box-shadow: 0px 0px 35px 0px rgba(154, 161, 171, 0.15); }\n  .page-title-box .page-title {\n    font-size: 18px;\n    margin: 0;\n    line-height: 50px;\n    font-weight: 700; }\n  .page-title-box .page-title-right {\n    float: right; }\n  .page-title-box .breadcrumb {\n    margin-bottom: 0;\n    padding: 14px 0; }\n\n@media (max-width: 767.98px) {\n  .page-title-box .page-title {\n    display: block;\n    white-space: nowrap;\n    text-overflow: ellipsis;\n    overflow: hidden; }\n  .page-title-box .breadcrumb {\n    display: none; } }\n\n@media (max-width: 640px) {\n  .page-title-box .page-title-right {\n    display: none; } }\n\n@media (max-width: 419px) {\n  .page-title-box .breadcrumb, .nav-user span {\n    display: none; } }\n\n.footer {\n  bottom: 0;\n  padding: 19px 15px 20px;\n  position: absolute;\n  right: 0;\n  color: #98a6ad;\n  left: 240px;\n  border-top: 1px solid #dee2e6;\n  text-align: center; }\n\n@media (max-width: 767.98px) {\n  .footer {\n    left: 0 !important;\n    text-align: center; } }\n\n.right-bar {\n  background-color: #fff;\n  -webkit-box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);\n          box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);\n  display: block;\n  position: fixed;\n  -webkit-transition: all 200ms ease-out;\n  transition: all 200ms ease-out;\n  width: 260px;\n  z-index: 9999;\n  float: right !important;\n  right: -270px;\n  top: 0;\n  bottom: 0; }\n  .right-bar .rightbar-title {\n    background: #348cd4;\n    padding: 27px 25px;\n    color: #fff; }\n  .right-bar .right-bar-toggle {\n    background-color: #414b5b;\n    height: 24px;\n    width: 24px;\n    line-height: 24px;\n    color: #fff;\n    text-align: center;\n    border-radius: 50%;\n    margin-top: -4px; }\n    .right-bar .right-bar-toggle:hover {\n      background-color: #475364; }\n  .right-bar .user-box {\n    padding: 25px;\n    text-align: center; }\n    .right-bar .user-box .user-img {\n      position: relative;\n      height: 64px;\n      width: 64px;\n      margin: 0 auto 15px auto; }\n      .right-bar .user-box .user-img .user-edit {\n        position: absolute;\n        right: -5px;\n        bottom: 0px;\n        height: 24px;\n        width: 24px;\n        background-color: #fff;\n        line-height: 24px;\n        border-radius: 50%;\n        -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);\n                box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12); }\n    .right-bar .user-box h5 {\n      margin-bottom: 2px; }\n      .right-bar .user-box h5 a {\n        color: #323a46; }\n\n.rightbar-overlay {\n  background-color: rgba(50, 58, 70, 0.55);\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n  display: none;\n  z-index: 9998;\n  -webkit-transition: all .2s ease-out;\n  transition: all .2s ease-out; }\n\n.right-bar-enabled .right-bar {\n  right: 0; }\n\n.right-bar-enabled .rightbar-overlay {\n  display: block; }\n\n@media (max-width: 767.98px) {\n  .right-bar {\n    overflow: auto; }\n    .right-bar .slimscroll-menu {\n      height: auto !important; } }\n\n.activity-widget .activity-list {\n  position: relative;\n  border-left: 2px dashed #ced4da;\n  padding-left: 24px;\n  padding-bottom: 2px; }\n  .activity-widget .activity-list::after {\n    content: \"\";\n    position: absolute;\n    left: -7px;\n    top: 6px;\n    width: 12px;\n    height: 12px;\n    background-color: #fff;\n    border: 2px solid #348cd4;\n    border-radius: 50%; }\n\n.inbox-widget .inbox-item {\n  overflow: hidden;\n  padding: 0.625rem 0;\n  position: relative; }\n  .inbox-widget .inbox-item .inbox-item-img {\n    display: block;\n    float: left;\n    margin-right: 15px;\n    margin-top: 4px; }\n    .inbox-widget .inbox-item .inbox-item-img img {\n      width: 40px; }\n  .inbox-widget .inbox-item .inbox-item-author {\n    display: block;\n    margin-bottom: 0px;\n    font-weight: 600; }\n    .inbox-widget .inbox-item .inbox-item-author a {\n      color: #6c757d; }\n  .inbox-widget .inbox-item .inbox-item-text {\n    color: #98a6ad;\n    display: block;\n    margin: 0;\n    overflow: hidden; }\n  .inbox-widget .inbox-item .inbox-item-date {\n    color: #98a6ad;\n    font-size: 0.6875rem;\n    position: absolute;\n    right: 5px;\n    top: 10px; }\n\n.width-xs {\n  min-width: 80px; }\n\n.width-sm {\n  min-width: 95px; }\n\n.width-md {\n  min-width: 110px; }\n\n.width-lg {\n  min-width: 140px; }\n\n.width-xl {\n  min-width: 160px; }\n\n.font-family-secondary {\n  font-family: \"Roboto\", sans-serif; }\n\n.avatar-xs {\n  height: 1.5rem;\n  width: 1.5rem; }\n\n.avatar-sm {\n  height: 2.25rem;\n  width: 2.25rem; }\n\n.avatar-md {\n  height: 3.5rem;\n  width: 3.5rem; }\n\n.avatar-lg {\n  height: 4.5rem;\n  width: 4.5rem; }\n\n.avatar-xl {\n  height: 6rem;\n  width: 6rem; }\n\n.avatar-xxl {\n  height: 7.5rem;\n  width: 7.5rem; }\n\n.avatar-title {\n  -webkit-box-align: center;\n      -ms-flex-align: center;\n          align-items: center;\n  color: #fff;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  height: 100%;\n  -webkit-box-pack: center;\n      -ms-flex-pack: center;\n          justify-content: center;\n  width: 100%; }\n\n.avatar-group {\n  padding-left: 12px; }\n  .avatar-group .avatar-group-item {\n    margin: 0 0 10px -12px;\n    display: inline-block;\n    border: 2px solid #fff;\n    border-radius: 50%; }\n\n.font-weight-medium {\n  font-weight: 500; }\n\n.font-weight-semibold {\n  font-weight: 600; }\n\n.sp-line-1,\n.sp-line-2,\n.sp-line-3,\n.sp-line-4 {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-box-orient: vertical; }\n\n.sp-line-1 {\n  -webkit-line-clamp: 1; }\n\n.sp-line-2 {\n  -webkit-line-clamp: 2; }\n\n.sp-line-3 {\n  -webkit-line-clamp: 3; }\n\n.sp-line-4 {\n  -webkit-line-clamp: 4; }\n\n.pull-in {\n  margin-left: -1.5rem;\n  margin-right: -1.5rem; }\n\n.social-list-item {\n  height: 2rem;\n  width: 2rem;\n  line-height: calc(2rem - 4px);\n  display: block;\n  border: 2px solid #adb5bd;\n  border-radius: 50%;\n  color: #adb5bd; }\n\n.checkbox label {\n  display: inline-block;\n  padding-left: 8px;\n  position: relative;\n  font-weight: normal; }\n  .checkbox label::before {\n    -o-transition: 0.3s ease-in-out;\n    -webkit-transition: 0.3s ease-in-out;\n    background-color: #fff;\n    border-radius: 3px;\n    border: 2px solid #98a6ad;\n    content: \"\";\n    display: inline-block;\n    height: 18px;\n    left: 0;\n    margin-left: -18px;\n    position: absolute;\n    transition: 0.3s ease-in-out;\n    width: 18px;\n    outline: none !important;\n    top: 2px; }\n  .checkbox label::after {\n    color: #6c757d;\n    display: inline-block;\n    font-size: 11px;\n    height: 18px;\n    left: 0;\n    margin-left: -18px;\n    padding-left: 3px;\n    padding-top: 2px;\n    position: absolute;\n    top: 0;\n    width: 18px; }\n\n.checkbox input[type=\"checkbox\"] {\n  cursor: pointer;\n  opacity: 0;\n  z-index: 1;\n  outline: none !important; }\n  .checkbox input[type=\"checkbox\"]:disabled + label {\n    opacity: 0.65; }\n\n.checkbox input[type=\"checkbox\"]:focus + label::before {\n  outline-offset: -2px;\n  outline: none; }\n\n.checkbox input[type=\"checkbox\"]:checked + label::after {\n  content: \"\";\n  position: absolute;\n  top: 6px;\n  left: 7px;\n  display: table;\n  width: 4px;\n  height: 8px;\n  border: 2px solid #6c757d;\n  border-top-width: 0;\n  border-left-width: 0;\n  -webkit-transform: rotate(45deg);\n  transform: rotate(45deg); }\n\n.checkbox input[type=\"checkbox\"]:disabled + label::before {\n  background-color: #f7f7f7;\n  cursor: not-allowed; }\n\n.checkbox.checkbox-circle label::before {\n  border-radius: 50%; }\n\n.checkbox.checkbox-inline {\n  margin-top: 0; }\n\n.checkbox.checkbox-single input {\n  height: 18px;\n  width: 18px;\n  position: absolute; }\n\n.checkbox.checkbox-single label {\n  height: 18px;\n  width: 18px; }\n  .checkbox.checkbox-single label:before {\n    margin-left: 0; }\n  .checkbox.checkbox-single label:after {\n    margin-left: 0; }\n\n.checkbox-primary input[type=\"checkbox\"]:checked + label::before {\n  background-color: #348cd4;\n  border-color: #348cd4; }\n\n.checkbox-primary input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-secondary input[type=\"checkbox\"]:checked + label::before {\n  background-color: #6c757d;\n  border-color: #6c757d; }\n\n.checkbox-secondary input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-success input[type=\"checkbox\"]:checked + label::before {\n  background-color: #3ec396;\n  border-color: #3ec396; }\n\n.checkbox-success input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-info input[type=\"checkbox\"]:checked + label::before {\n  background-color: #4fbde9;\n  border-color: #4fbde9; }\n\n.checkbox-info input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-warning input[type=\"checkbox\"]:checked + label::before {\n  background-color: #f9bc0b;\n  border-color: #f9bc0b; }\n\n.checkbox-warning input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-danger input[type=\"checkbox\"]:checked + label::before {\n  background-color: #f36270;\n  border-color: #f36270; }\n\n.checkbox-danger input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-light input[type=\"checkbox\"]:checked + label::before {\n  background-color: #f7f7f7;\n  border-color: #f7f7f7; }\n\n.checkbox-light input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-dark input[type=\"checkbox\"]:checked + label::before {\n  background-color: #323a46;\n  border-color: #323a46; }\n\n.checkbox-dark input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-pink input[type=\"checkbox\"]:checked + label::before {\n  background-color: #e061c9;\n  border-color: #e061c9; }\n\n.checkbox-pink input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.checkbox-purple input[type=\"checkbox\"]:checked + label::before {\n  background-color: #9368f3;\n  border-color: #9368f3; }\n\n.checkbox-purple input[type=\"checkbox\"]:checked + label::after {\n  border-color: #fff; }\n\n.radio label {\n  display: inline-block;\n  padding-left: 8px;\n  position: relative;\n  font-weight: normal; }\n  .radio label::before {\n    -o-transition: border 0.5s ease-in-out;\n    -webkit-transition: border 0.5s ease-in-out;\n    background-color: #fff;\n    border-radius: 50%;\n    border: 2px solid #98a6ad;\n    content: \"\";\n    display: inline-block;\n    height: 18px;\n    left: 0;\n    margin-left: -18px;\n    position: absolute;\n    transition: border 0.5s ease-in-out;\n    width: 18px;\n    outline: none !important; }\n  .radio label::after {\n    -moz-transition: -moz-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);\n    -ms-transform: scale(0, 0);\n    -o-transform: scale(0, 0);\n    -o-transition: -o-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);\n    -webkit-transform: scale(0, 0);\n    -webkit-transition: -webkit-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);\n    background-color: #6c757d;\n    border-radius: 50%;\n    content: \" \";\n    display: inline-block;\n    height: 10px;\n    left: 6px;\n    margin-left: -20px;\n    position: absolute;\n    top: 4px;\n    transform: scale(0, 0);\n    transition: -webkit-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);\n    transition: transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);\n    transition: transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33), -webkit-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);\n    width: 10px; }\n\n.radio input[type=\"radio\"] {\n  cursor: pointer;\n  opacity: 0;\n  z-index: 1;\n  outline: none !important; }\n  .radio input[type=\"radio\"]:disabled + label {\n    opacity: 0.65; }\n\n.radio input[type=\"radio\"]:focus + label::before {\n  outline-offset: -2px;\n  outline: 5px auto -webkit-focus-ring-color;\n  outline: thin dotted; }\n\n.radio input[type=\"radio\"]:checked + label::after {\n  -webkit-transform: scale(1, 1);\n  transform: scale(1, 1); }\n\n.radio input[type=\"radio\"]:disabled + label::before {\n  cursor: not-allowed; }\n\n.radio.radio-inline {\n  margin-top: 0; }\n\n.radio.radio-single label {\n  height: 17px; }\n\n.radio-primary input[type=\"radio\"] + label::after {\n  background-color: #348cd4; }\n\n.radio-primary input[type=\"radio\"]:checked + label::before {\n  border-color: #348cd4; }\n\n.radio-primary input[type=\"radio\"]:checked + label::after {\n  background-color: #348cd4; }\n\n.radio-secondary input[type=\"radio\"] + label::after {\n  background-color: #6c757d; }\n\n.radio-secondary input[type=\"radio\"]:checked + label::before {\n  border-color: #6c757d; }\n\n.radio-secondary input[type=\"radio\"]:checked + label::after {\n  background-color: #6c757d; }\n\n.radio-success input[type=\"radio\"] + label::after {\n  background-color: #3ec396; }\n\n.radio-success input[type=\"radio\"]:checked + label::before {\n  border-color: #3ec396; }\n\n.radio-success input[type=\"radio\"]:checked + label::after {\n  background-color: #3ec396; }\n\n.radio-info input[type=\"radio\"] + label::after {\n  background-color: #4fbde9; }\n\n.radio-info input[type=\"radio\"]:checked + label::before {\n  border-color: #4fbde9; }\n\n.radio-info input[type=\"radio\"]:checked + label::after {\n  background-color: #4fbde9; }\n\n.radio-warning input[type=\"radio\"] + label::after {\n  background-color: #f9bc0b; }\n\n.radio-warning input[type=\"radio\"]:checked + label::before {\n  border-color: #f9bc0b; }\n\n.radio-warning input[type=\"radio\"]:checked + label::after {\n  background-color: #f9bc0b; }\n\n.radio-danger input[type=\"radio\"] + label::after {\n  background-color: #f36270; }\n\n.radio-danger input[type=\"radio\"]:checked + label::before {\n  border-color: #f36270; }\n\n.radio-danger input[type=\"radio\"]:checked + label::after {\n  background-color: #f36270; }\n\n.radio-light input[type=\"radio\"] + label::after {\n  background-color: #f7f7f7; }\n\n.radio-light input[type=\"radio\"]:checked + label::before {\n  border-color: #f7f7f7; }\n\n.radio-light input[type=\"radio\"]:checked + label::after {\n  background-color: #f7f7f7; }\n\n.radio-dark input[type=\"radio\"] + label::after {\n  background-color: #323a46; }\n\n.radio-dark input[type=\"radio\"]:checked + label::before {\n  border-color: #323a46; }\n\n.radio-dark input[type=\"radio\"]:checked + label::after {\n  background-color: #323a46; }\n\n.radio-pink input[type=\"radio\"] + label::after {\n  background-color: #e061c9; }\n\n.radio-pink input[type=\"radio\"]:checked + label::before {\n  border-color: #e061c9; }\n\n.radio-pink input[type=\"radio\"]:checked + label::after {\n  background-color: #e061c9; }\n\n.radio-purple input[type=\"radio\"] + label::after {\n  background-color: #9368f3; }\n\n.radio-purple input[type=\"radio\"]:checked + label::before {\n  border-color: #9368f3; }\n\n.radio-purple input[type=\"radio\"]:checked + label::after {\n  background-color: #9368f3; }\n\n@media print {\n  .left-side-menu,\n  .right-bar,\n  .page-title-box,\n  .navbar-custom,\n  .footer {\n    display: none; }\n  .card-body,\n  .content-page,\n  .right-bar,\n  .content,\n  body {\n    padding: 0;\n    margin: 0; } }\n\n/* =============\r\n   Widgets\r\n============= */\n.tilebox-one {\n  background: url(\"../images/bg-1.png\");\n  background-size: cover;\n  border: 4px solid #fff; }\n  .tilebox-one i {\n    background: #348cd4;\n    background: #348cd4;\n    font-size: 24px;\n    height: 50px;\n    line-height: 50px;\n    width: 50px;\n    text-align: center;\n    color: #fff !important;\n    border-radius: 50%;\n    -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);\n            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12); }\n\n/* Inbox-widget */\n.inbox-widget .inbox-item {\n  border-bottom: 1px solid #edeff1;\n  overflow: hidden;\n  padding: 10px 0;\n  position: relative; }\n  .inbox-widget .inbox-item .inbox-item-img {\n    display: block;\n    float: left;\n    margin-right: 15px;\n    width: 40px; }\n  .inbox-widget .inbox-item img {\n    width: 40px; }\n  .inbox-widget .inbox-item .inbox-item-author {\n    color: #323a46;\n    display: block;\n    margin: 0; }\n  .inbox-widget .inbox-item .inbox-item-text {\n    color: #98a6ad;\n    display: block;\n    font-size: 14px;\n    margin: 0; }\n  .inbox-widget .inbox-item .inbox-item-date {\n    color: #98a6ad;\n    font-size: 11px;\n    position: absolute;\n    right: 7px;\n    top: 7px; }\n\n/* Comment List */\n.comment-list .comment-box-item {\n  position: relative; }\n  .comment-list .comment-box-item .commnet-item-date {\n    color: #98a6ad;\n    font-size: 11px;\n    position: absolute;\n    right: 7px;\n    top: 2px; }\n  .comment-list .comment-box-item .commnet-item-msg {\n    color: #323a46;\n    display: block;\n    margin: 10px 0;\n    font-weight: normal;\n    font-size: 15px;\n    line-height: 24px; }\n  .comment-list .comment-box-item .commnet-item-user {\n    color: #98a6ad;\n    display: block;\n    font-size: 14px;\n    margin: 0; }\n\n.comment-list a + a {\n  margin-top: 15px;\n  display: block; }\n\n/* Transaction */\n.transaction-list li {\n  padding: 7px 0;\n  border-bottom: 1px solid #edeff1;\n  clear: both;\n  position: relative; }\n\n.transaction-list i {\n  width: 20px;\n  position: absolute;\n  top: 10px;\n  font-size: 12px; }\n\n.transaction-list .tran-text {\n  padding-left: 25px;\n  white-space: nowrap;\n  display: inline-block;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  width: 150px; }\n\n.transaction-list .tran-price {\n  margin-left: 30px; }\n\n/*!\r\n * Waves v0.7.6\r\n * http://fian.my.id/Waves \r\n * \r\n * Copyright 2014-2018 Alfiana E. Sibuea and other contributors \r\n * Released under the MIT license \r\n * https://github.com/fians/Waves/blob/master/LICENSE */\n.waves-effect {\n  position: relative;\n  cursor: pointer;\n  display: inline-block;\n  overflow: hidden;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  -webkit-tap-highlight-color: transparent; }\n\n.waves-effect .waves-ripple {\n  position: absolute;\n  border-radius: 50%;\n  width: 100px;\n  height: 100px;\n  margin-top: -50px;\n  margin-left: -50px;\n  opacity: 0;\n  background: rgba(0, 0, 0, 0.2);\n  background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  -webkit-transition: all 0.5s ease-out;\n  transition: all 0.5s ease-out;\n  -webkit-transition-property: -webkit-transform, opacity;\n  -webkit-transition-property: opacity, -webkit-transform;\n  transition-property: opacity, -webkit-transform;\n  transition-property: transform, opacity;\n  transition-property: transform, opacity, -webkit-transform;\n  -webkit-transform: scale(0) translate(0, 0);\n  transform: scale(0) translate(0, 0);\n  pointer-events: none; }\n\n.waves-effect.waves-light .waves-ripple {\n  background: rgba(255, 255, 255, 0.4);\n  background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%); }\n\n.waves-effect.waves-classic .waves-ripple {\n  background: rgba(0, 0, 0, 0.2); }\n\n.waves-effect.waves-classic.waves-light .waves-ripple {\n  background: rgba(255, 255, 255, 0.4); }\n\n.waves-notransition {\n  -webkit-transition: none !important;\n  transition: none !important; }\n\n.waves-button,\n.waves-circle {\n  -webkit-transform: translateZ(0);\n  transform: translateZ(0);\n  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%); }\n\n.waves-button,\n.waves-button:hover,\n.waves-button:visited,\n.waves-button-input {\n  white-space: nowrap;\n  vertical-align: middle;\n  cursor: pointer;\n  border: none;\n  outline: none;\n  color: inherit;\n  background-color: rgba(0, 0, 0, 0);\n  font-size: 1em;\n  line-height: 1em;\n  text-align: center;\n  text-decoration: none;\n  z-index: 1; }\n\n.waves-button {\n  padding: 0.85em 1.1em;\n  border-radius: 0.2em; }\n\n.waves-button-input {\n  margin: 0;\n  padding: 0.85em 1.1em; }\n\n.waves-input-wrapper {\n  border-radius: 0.2em;\n  vertical-align: bottom; }\n\n.waves-input-wrapper.waves-button {\n  padding: 0; }\n\n.waves-input-wrapper .waves-button-input {\n  position: relative;\n  top: 0;\n  left: 0;\n  z-index: 1; }\n\n.waves-circle {\n  text-align: center;\n  width: 2.5em;\n  height: 2.5em;\n  line-height: 2.5em;\n  border-radius: 50%; }\n\n.waves-float {\n  -webkit-mask-image: none;\n  -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\n  box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\n  -webkit-transition: all 300ms;\n  transition: all 300ms; }\n\n.waves-float:active {\n  -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\n  box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3); }\n\n.waves-block {\n  display: block; }\n\n.slimScrollDiv {\n  height: auto !important; }\n\n.jq-toast-single {\n  padding: 15px;\n  font-family: \"Nunito Sans\", sans-serif;\n  background-color: #348cd4;\n  font-size: 13px;\n  line-height: 22px; }\n  .jq-toast-single h2 {\n    font-family: \"Nunito Sans\", sans-serif; }\n  .jq-toast-single a {\n    font-size: 0.875rem; }\n    .jq-toast-single a:hover {\n      color: #fff; }\n\n.jq-has-icon {\n  padding: 10px 10px 10px 50px; }\n\n.close-jq-toast-single {\n  position: absolute;\n  top: -12px;\n  right: -12px;\n  font-size: 20px;\n  cursor: pointer;\n  height: 32px;\n  width: 32px;\n  background-color: #323a46;\n  border-radius: 50%;\n  text-align: center;\n  line-height: 32px; }\n\n.jq-toast-loader {\n  height: 3px;\n  top: 0;\n  border-radius: 0; }\n\n.jq-icon-primary {\n  background-color: #348cd4;\n  color: #fff;\n  border-color: #348cd4; }\n\n.jq-icon-secondary {\n  background-color: #6c757d;\n  color: #fff;\n  border-color: #6c757d; }\n\n.jq-icon-success {\n  background-color: #3ec396;\n  color: #fff;\n  border-color: #3ec396; }\n\n.jq-icon-info {\n  background-color: #4fbde9;\n  color: #fff;\n  border-color: #4fbde9; }\n\n.jq-icon-warning {\n  background-color: #f9bc0b;\n  color: #fff;\n  border-color: #f9bc0b; }\n\n.jq-icon-danger {\n  background-color: #f36270;\n  color: #fff;\n  border-color: #f36270; }\n\n.jq-icon-light {\n  background-color: #f7f7f7;\n  color: #fff;\n  border-color: #f7f7f7; }\n\n.jq-icon-dark {\n  background-color: #323a46;\n  color: #fff;\n  border-color: #323a46; }\n\n.jq-icon-pink {\n  background-color: #e061c9;\n  color: #fff;\n  border-color: #e061c9; }\n\n.jq-icon-purple {\n  background-color: #9368f3;\n  color: #fff;\n  border-color: #9368f3; }\n\n.jq-icon-error {\n  background-color: #f36270;\n  color: #fff;\n  border-color: #f36270; }\n\n.swal2-modal {\n  font-family: \"Nunito Sans\", sans-serif;\n  -webkit-box-shadow: 0 10px 33px rgba(0, 0, 0, 0.1);\n          box-shadow: 0 10px 33px rgba(0, 0, 0, 0.1);\n  border: 2px solid #348cd4; }\n  .swal2-modal .swal2-title {\n    font-size: 24px; }\n  .swal2-modal .swal2-content {\n    font-size: 16px; }\n  .swal2-modal .swal2-spacer {\n    margin: 10px 0; }\n  .swal2-modal .swal2-file, .swal2-modal .swal2-input, .swal2-modal .swal2-textarea {\n    border: 2px solid #dee2e6;\n    font-size: 16px;\n    -webkit-box-shadow: none;\n            box-shadow: none; }\n  .swal2-modal .swal2-confirm.btn-confirm {\n    background-color: #348cd4 !important;\n    font-size: 0.875rem; }\n  .swal2-modal .swal2-cancel.btn-cancel {\n    background-color: #f36270 !important;\n    font-size: 0.875rem; }\n  .swal2-modal .swal2-styled:focus {\n    -webkit-box-shadow: none !important;\n            box-shadow: none !important; }\n\n.swal2-icon.swal2-question {\n  color: #348cd4;\n  border-color: #348cd4; }\n\n.swal2-icon.swal2-success {\n  border-color: #3ec396; }\n  .swal2-icon.swal2-success .line, .swal2-icon.swal2-success [class^=swal2-success-line][class$=long],\n  .swal2-icon.swal2-success [class^=swal2-success-line] {\n    background-color: #3ec396; }\n  .swal2-icon.swal2-success .placeholder, .swal2-icon.swal2-success .swal2-success-ring {\n    border-color: #3ec396; }\n\n.swal2-icon.swal2-warning {\n  color: #f9bc0b;\n  border-color: #f9bc0b; }\n\n.swal2-icon.swal2-error {\n  border-color: #f36270; }\n  .swal2-icon.swal2-error .line {\n    background-color: #f36270; }\n\n.swal2-modal .swal2-file:focus, .swal2-modal .swal2-input:focus, .swal2-modal .swal2-textarea:focus {\n  outline: 0;\n  border: 2px solid #348cd4; }\n\n.swal2-container.swal2-shown {\n  background-color: rgba(255, 255, 255, 0.9); }\n\n.flotTip {\n  padding: 8px 12px;\n  background-color: #fff;\n  z-index: 99;\n  color: #323a46;\n  -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);\n          box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);\n  opacity: 1;\n  border-radius: 3px; }\n\n.legend tr {\n  height: 30px;\n  font-family: \"Roboto\", sans-serif; }\n\n.legendLabel {\n  padding-left: 5px !important;\n  line-height: 10px;\n  padding-right: 20px;\n  font-size: 13px;\n  font-weight: 500;\n  color: #98a6ad;\n  text-transform: uppercase; }\n\n.legendColorBox div div {\n  border-radius: 50%; }\n\n@media (max-width: 767.98px) {\n  .legendLabel {\n    display: none; } }\n\n.morris-chart text {\n  font-family: \"Roboto\", sans-serif !important; }\n\n.morris-hover {\n  position: absolute;\n  z-index: 10; }\n  .morris-hover.morris-default-style {\n    font-size: 12px;\n    text-align: center;\n    border-radius: 5px;\n    padding: 10px 12px;\n    background: #fff;\n    color: #323a46;\n    -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);\n            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);\n    font-family: \"Nunito Sans\", sans-serif; }\n    .morris-hover.morris-default-style .morris-hover-row-label {\n      font-weight: bold;\n      margin: 0.25em 0;\n      font-family: \"Roboto\", sans-serif; }\n    .morris-hover.morris-default-style .morris-hover-point {\n      white-space: nowrap;\n      margin: 0.1em 0;\n      color: #fff; }\n\n.chartjs-chart {\n  margin: auto;\n  position: relative;\n  width: 100%; }\n\n.jqstooltip {\n  -webkit-box-sizing: content-box;\n          box-sizing: content-box;\n  width: auto !important;\n  height: auto !important;\n  background-color: #fff !important;\n  -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);\n          box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);\n  padding: 5px 10px !important;\n  border-radius: 3px;\n  border-color: #fff !important; }\n\n.jqsfield {\n  color: #323a46 !important;\n  font-size: 12px !important;\n  line-height: 18px !important;\n  font-family: \"Nunito Sans\", sans-serif !important;\n  font-weight: 700 !important; }\n\n/* =============\r\n   Maps\r\n============= */\n.gmaps,\n.gmaps-panaroma {\n  height: 300px;\n  background: #eeeeee;\n  border-radius: 3px; }\n\n.gmaps-overlay {\n  display: block;\n  text-align: center;\n  color: #fff;\n  font-size: 16px;\n  line-height: 40px;\n  background: #348cd4;\n  border-radius: 4px;\n  padding: 10px 20px; }\n\n.gmaps-overlay_arrow {\n  left: 50%;\n  margin-left: -16px;\n  width: 0;\n  height: 0;\n  position: absolute; }\n\n.gmaps-overlay_arrow.above {\n  bottom: -15px;\n  border-left: 16px solid transparent;\n  border-right: 16px solid transparent;\n  border-top: 16px solid #348cd4; }\n\n.gmaps-overlay_arrow.below {\n  top: -15px;\n  border-left: 16px solid transparent;\n  border-right: 16px solid transparent;\n  border-bottom: 16px solid #348cd4; }\n\n.gmaps-full {\n  z-index: 99;\n  margin: 0 -20px -10px -20px; }\n  .gmaps-full .gmaps-full1 {\n    height: 80vh;\n    width: 100%; }\n\n.jvectormap-label {\n  border: none;\n  background: #323a46;\n  color: #fff;\n  font-family: \"Roboto\", sans-serif;\n  font-size: 0.875rem;\n  padding: 5px 8px; }\n\n/* Mapael Map */\n.mapael .map {\n  position: relative; }\n  .mapael .map .zoomIn {\n    top: 25px; }\n  .mapael .map .zoomOut {\n    top: 50px; }\n\n.mapael .mapTooltip {\n  position: absolute;\n  background-color: #348cd4;\n  opacity: 0.95;\n  border-radius: 3px;\n  padding: 2px 10px;\n  z-index: 1000;\n  max-width: 200px;\n  display: none;\n  color: #fff;\n  font-family: \"Roboto\", sans-serif; }\n\n.mapael .zoomIn,\n.mapael .zoomOut,\n.mapael .zoomReset {\n  display: inline-block;\n  text-align: center;\n  vertical-align: middle;\n  border-radius: 2px;\n  font-weight: 500;\n  cursor: pointer;\n  background-color: #348cd4;\n  text-decoration: none;\n  color: #fff;\n  font-size: 14px;\n  position: absolute;\n  top: 0;\n  left: 10px;\n  width: 24px;\n  height: 24px;\n  line-height: 24px; }\n\n.mapael .plotLegend text {\n  font-family: \"Nunito Sans\", sans-serif !important; }\n\n.calendar {\n  float: left;\n  margin-bottom: 0; }\n\n.fc-view {\n  margin-top: 30px; }\n\n.none-border .modal-footer {\n  border-top: none; }\n\n.fc-toolbar {\n  margin: 15px 0 5px 0; }\n  .fc-toolbar h2 {\n    font-size: 1.25rem;\n    line-height: 1.875rem;\n    text-transform: uppercase; }\n\n.fc-day-grid-event .fc-time {\n  font-weight: 700; }\n\n.fc-day {\n  background: #fff; }\n\n.fc-toolbar .fc-state-active,\n.fc-toolbar .ui-state-active,\n.fc-toolbar button:focus,\n.fc-toolbar button:hover,\n.fc-toolbar .ui-state-hover {\n  z-index: 0; }\n\n.fc th.fc-widget-header {\n  background: #f1f5f7;\n  font-size: 13px;\n  line-height: 20px;\n  padding: 10px 0;\n  text-transform: uppercase; }\n\n.fc-unthemed th,\n.fc-unthemed td,\n.fc-unthemed thead,\n.fc-unthemed tbody,\n.fc-unthemed .fc-divider,\n.fc-unthemed .fc-row,\n.fc-unthemed .fc-popover {\n  border-color: #dee2e6; }\n\n.fc-ltr .fc-basic-view .fc-day-top .fc-day-number {\n  float: right;\n  margin: 5px;\n  font-family: \"Roboto\", sans-serif;\n  font-size: 12px; }\n\n.fc-button {\n  background: #f1f5f7;\n  border: none;\n  color: #6c757d;\n  text-transform: capitalize;\n  -webkit-box-shadow: none;\n          box-shadow: none;\n  border-radius: 3px;\n  margin: 0 3px;\n  padding: 6px 12px;\n  height: auto; }\n\n.fc-text-arrow {\n  font-family: inherit;\n  font-size: 1rem; }\n\n.fc-state-hover {\n  background: #f1f5f7; }\n\n.fc-state-highlight {\n  background: #dee2e6; }\n\n.fc-state-down,\n.fc-state-active,\n.fc-state-disabled {\n  background: #348cd4;\n  color: #fff;\n  text-shadow: none; }\n\n.fc-cell-overlay {\n  background: #dee2e6; }\n\n.fc-unthemed .fc-today {\n  background: #fff; }\n\n.fc-event {\n  border-radius: 2px;\n  border: none;\n  cursor: move;\n  font-size: 0.8125rem;\n  margin: 5px 7px;\n  padding: 5px 5px;\n  text-align: center; }\n\n.external-event {\n  cursor: move;\n  margin: 10px 0;\n  padding: 8px 10px;\n  color: #fff; }\n\n.fc-basic-view td.fc-week-number span {\n  padding-right: 8px; }\n\n.fc-basic-view td.fc-day-number {\n  padding-right: 8px; }\n\n.fc-basic-view .fc-content {\n  color: #fff; }\n\n.fc-time-grid-event .fc-content {\n  color: #fff; }\n\n@media (max-width: 767.98px) {\n  .fc-toolbar .fc-left, .fc-toolbar .fc-right, .fc-toolbar .fc-center {\n    float: none;\n    display: block;\n    clear: both;\n    margin: 10px 0; }\n  .fc .fc-toolbar > * > * {\n    float: none; }\n  .fc-today-button {\n    display: none; } }\n\n/* =============\r\n   Summernote\r\n============= */\n@font-face {\n  font-family: \"summernote\";\n  font-style: normal;\n  font-weight: normal;\n  src: url(\"../fonts/summernote.eot\");\n  src: url(\"../fonts/summernote.eot?#iefix\") format(\"embedded-opentype\"), url(\"../fonts/summernote.woff?\") format(\"woff\"), url(\"../fonts/summernote.ttf?\") format(\"truetype\"); }\n\n.note-editor.note-frame {\n  border: 1px solid #ced4da;\n  -webkit-box-shadow: none;\n          box-shadow: none;\n  margin: 0; }\n  .note-editor.note-frame .note-statusbar {\n    background-color: #fcfcfc;\n    border-top: 1px solid #f7f7f7; }\n  .note-editor.note-frame .note-editable {\n    border: none; }\n\n.note-status-output {\n  display: none; }\n\n.note-editable {\n  border: none;\n  border-radius: 0.2rem;\n  padding: 0.45rem 0.9rem; }\n  .note-editable p:last-of-type {\n    margin-bottom: 0; }\n\n.note-popover .popover-content .note-color .dropdown-menu,\n.card-header.note-toolbar .note-color .dropdown-menu {\n  min-width: 344px; }\n\n.note-toolbar {\n  z-index: 1; }\n\n.select2-container .select2-selection--single {\n  border: 1px solid #ced4da;\n  height: 38px;\n  outline: none; }\n  .select2-container .select2-selection--single .select2-selection__rendered {\n    line-height: 36px;\n    padding-left: 12px; }\n  .select2-container .select2-selection--single .select2-selection__arrow {\n    height: 34px;\n    width: 34px;\n    right: 3px; }\n    .select2-container .select2-selection--single .select2-selection__arrow b {\n      border-color: #d1d1d1 transparent transparent transparent;\n      border-width: 6px 6px 0 6px; }\n\n.select2-container--open .select2-selection--single .select2-selection__arrow b {\n  border-color: transparent transparent #d1d1d1 transparent !important;\n  border-width: 0 6px 6px 6px !important; }\n\n.select2-results__option {\n  padding: 6px 12px; }\n\n.select2-dropdown {\n  border: 1px solid #eaeaea;\n  -webkit-box-shadow: 0px 0px 35px 0px rgba(154, 161, 171, 0.15);\n          box-shadow: 0px 0px 35px 0px rgba(154, 161, 171, 0.15); }\n\n.select2-container--default .select2-search--dropdown {\n  padding: 10px;\n  background-color: white; }\n  .select2-container--default .select2-search--dropdown .select2-search__field {\n    border: 1px solid #eaeaea;\n    outline: none; }\n\n.select2-container--default .select2-results__option--highlighted[aria-selected] {\n  background: #348cd4; }\n\n.select2-container--default .select2-results__option[aria-selected=true] {\n  background-color: #f7f7f7;\n  color: #323a46; }\n  .select2-container--default .select2-results__option[aria-selected=true]:hover {\n    background: #348cd4;\n    color: #fff; }\n\n.select2-container .select2-selection--multiple {\n  min-height: 38px;\n  border: 1px solid #ced4da !important; }\n  .select2-container .select2-selection--multiple .select2-selection__rendered {\n    padding: 1px 10px; }\n  .select2-container .select2-selection--multiple .select2-search__field {\n    border: 0; }\n  .select2-container .select2-selection--multiple .select2-selection__choice {\n    background: #348cd4;\n    border: none;\n    color: #fff;\n    border-radius: 3px;\n    padding: 0 7px;\n    margin-top: 7px; }\n  .select2-container .select2-selection--multiple .select2-selection__choice__remove {\n    color: #fff;\n    margin-right: 5px; }\n    .select2-container .select2-selection--multiple .select2-selection__choice__remove:hover {\n      color: #fff; }\n\n.autocomplete-suggestions {\n  border: 1px solid #f9f9f9;\n  background: #fff;\n  cursor: default;\n  overflow: auto;\n  max-height: 200px !important;\n  -webkit-box-shadow: 0 2px 2px rgba(0, 0, 0, 0.15);\n          box-shadow: 0 2px 2px rgba(0, 0, 0, 0.15); }\n  .autocomplete-suggestions strong {\n    font-weight: bold;\n    color: #323a46; }\n\n.autocomplete-suggestion {\n  padding: 5px 10px;\n  white-space: nowrap;\n  overflow: hidden; }\n\n.autocomplete-no-suggestion {\n  padding: 5px; }\n\n.autocomplete-selected {\n  background: #f7f7f7;\n  cursor: pointer; }\n\n.autocomplete-group {\n  padding: 5px;\n  font-weight: 500;\n  font-family: \"Roboto\", sans-serif; }\n  .autocomplete-group strong {\n    font-weight: bold;\n    font-size: 16px;\n    color: #323a46;\n    display: block; }\n\n/* Bootstrap tagsinput */\n.bootstrap-tagsinput {\n  -webkit-box-shadow: none;\n          box-shadow: none;\n  padding: 4px 7px 4px;\n  border: 1px solid #efefef;\n  width: 100%; }\n  .bootstrap-tagsinput .label-info {\n    background: #348cd4;\n    display: inline-block;\n    font-size: 11px;\n    margin: 3px 1px;\n    padding: 0 5px;\n    border-radius: 3px; }\n\n.bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {\n  width: 100% !important; }\n\n.bootstrap-select .dropdown-menu .dropdown-menu li a {\n  display: block;\n  width: 100%;\n  clear: both;\n  font-weight: 400;\n  color: #323a46;\n  text-align: inherit;\n  white-space: nowrap;\n  background: 0 0;\n  border: 0; }\n  .bootstrap-select .dropdown-menu .dropdown-menu li a:hover {\n    background: #348cd4;\n    color: #fff; }\n\n.bootstrap-select .dropdown-toggle:after {\n  content: \"\\F140\";\n  display: inline-block;\n  font-family: \"Material Design Icons\"; }\n\n.bootstrap-select .dropdown-toggle:focus {\n  outline: none !important;\n  outline-offset: 0; }\n\n.bootstrap-select a {\n  outline: none !important; }\n\n.bootstrap-select .inner {\n  overflow-y: inherit !important; }\n\n.bootstrap-select > .btn-pink.bs-placeholder {\n  color: #fff !important; }\n\n.parsley-errors-list {\n  margin: 0;\n  padding: 0; }\n  .parsley-errors-list > li {\n    list-style: none;\n    color: #f36270;\n    margin-top: 5px;\n    padding-left: 20px;\n    position: relative; }\n    .parsley-errors-list > li:before {\n      content: \"\\F159\";\n      font-family: \"Material Design Icons\";\n      position: absolute;\n      left: 2px;\n      top: -1px; }\n\n.parsley-error {\n  border-color: #f36270; }\n\n.parsley-success {\n  border-color: #3ec396; }\n\n.bootstrap-timepicker-widget table td input {\n  width: 35px;\n  border: 0px; }\n\n.bootstrap-timepicker-widget table td a:hover {\n  background-color: transparent;\n  border: 1px solid transparent; }\n\n/* Daterange Picker */\n.daterangepicker td.active, .daterangepicker td.active:hover {\n  background: #348cd4;\n  background: #348cd4; }\n\n.daterangepicker .input-mini.active {\n  border: 1px solid rgba(50, 58, 70, 0.3); }\n\n.daterangepicker .ranges li {\n  border-radius: 2px;\n  color: #323a46;\n  font-weight: 600;\n  font-size: 12px; }\n\n.daterangepicker select.hourselect, .daterangepicker select.minuteselect,\n.daterangepicker select.secondselect, .daterangepicker select.ampmselect {\n  border: 1px solid rgba(50, 58, 70, 0.3);\n  padding: 2px;\n  width: 60px; }\n\n.daterangepicker .ranges li.active, .daterangepicker .ranges li:hover {\n  background: #348cd4;\n  background: #348cd4;\n  border: 1px solid #348cd4;\n  color: #fff; }\n\n.daterangepicker select.monthselect, .daterangepicker select.yearselect {\n  border-color: #98a6ad; }\n\n/* Clock picker */\n.clockpicker-canvas line {\n  stroke: #73b0e1; }\n\n.clockpicker-canvas-bearing, .clockpicker-canvas-fg, .clockpicker-canvas-bg {\n  fill: #73b0e1; }\n\n.clockpicker-popover .btn-default {\n  background-color: #348cd4;\n  color: #fff; }\n\n.wizard > .steps {\n  position: relative;\n  display: block;\n  width: 100%; }\n  .wizard > .steps > ul > li {\n    width: 25%; }\n  .wizard > .steps a {\n    font-size: 16px;\n    margin: 0 0.5em 0.5em; }\n  .wizard > .steps .number {\n    border-radius: 50%;\n    background-color: rgba(255, 255, 255, 0.3);\n    display: inline-block;\n    line-height: 30px;\n    margin-right: 10px;\n    width: 30px;\n    text-align: center;\n    font-size: 1.429em; }\n  .wizard > .steps a, .wizard > .steps a:hover, .wizard > .steps a:active {\n    display: block;\n    width: auto;\n    padding: 1em 1em;\n    text-decoration: none;\n    border-radius: 2px; }\n  .wizard > .steps .disabled a {\n    border: 1px solid #ededed;\n    color: #323a46;\n    cursor: default; }\n  .wizard > .steps .current a, .wizard > .steps .current a:hover, .wizard > .steps .current a:active {\n    background: #348cd4;\n    color: #fff;\n    cursor: default; }\n    .wizard > .steps .current a .number, .wizard > .steps .current a:hover .number, .wizard > .steps .current a:active .number {\n      color: #fff; }\n  .wizard > .steps .done a, .wizard > .steps .done a:hover, .wizard > .steps .done a:active {\n    background: #88bce6;\n    color: #fff; }\n  .wizard > .steps .error a, .wizard > .steps .error a:hover, .wizard > .steps .error a:active {\n    background: snow;\n    color: #fff;\n    border-color: #fde7e9; }\n\n.wizard > .steps > ul > li, .wizard > .actions > ul > li {\n  float: left;\n  position: relative; }\n\n.wizard > .content {\n  display: block;\n  min-height: 240px;\n  overflow: hidden;\n  position: relative;\n  width: auto;\n  padding: 20px; }\n  .wizard > .content > .body {\n    padding: 0;\n    position: relative;\n    width: 95%; }\n    .wizard > .content > .body ul {\n      list-style: disc !important; }\n      .wizard > .content > .body ul > li {\n        display: block;\n        line-height: 30px; }\n    .wizard > .content > .body > iframe {\n      border: 0 none;\n      width: 100%;\n      height: 100%; }\n    .wizard > .content > .body input {\n      display: block;\n      border-color: #dee2e6; }\n      .wizard > .content > .body input:focus {\n        border-color: #dee2e6; }\n      .wizard > .content > .body input[type=\"checkbox\"] {\n        display: inline-block; }\n      .wizard > .content > .body input.error {\n        background: white;\n        border: 1px solid snow;\n        color: #f36270; }\n    .wizard > .content > .body label {\n      display: inline-block;\n      margin-bottom: 0.5em;\n      margin-top: 10px; }\n      .wizard > .content > .body label.error {\n        color: #f36270;\n        font-size: 12px; }\n\n.wizard > .actions {\n  position: relative;\n  display: block;\n  text-align: right;\n  width: 100%;\n  margin-top: 15px; }\n  .wizard > .actions > ul {\n    display: inline-block;\n    text-align: right; }\n    .wizard > .actions > ul > li {\n      margin: 0 0.5em; }\n  .wizard > .actions a, .wizard > .actions a:hover, .wizard > .actions a:active {\n    background: #348cd4;\n    color: #fff;\n    display: block;\n    padding: 0.5em 1em;\n    text-decoration: none;\n    border-radius: 2px; }\n  .wizard > .actions .disabled a, .wizard > .actions .disabled a:hover, .wizard > .actions .disabled a:active {\n    background: #fff;\n    color: #323a46;\n    cursor: default;\n    border: 1px solid #f7f7f7; }\n\n.wizard.vertical > .steps {\n  display: inline;\n  float: left;\n  width: 30%; }\n  .wizard.vertical > .steps > ul > li {\n    float: none;\n    width: 100%; }\n\n.wizard.vertical > .content {\n  width: 65%;\n  margin: 0 2.5% 0.5em;\n  display: inline;\n  float: left; }\n\n.wizard.vertical > .actions {\n  display: inline;\n  float: right;\n  width: 95%;\n  margin: 0 2.5%;\n  margin-top: 15px !important; }\n  .wizard.vertical > .actions > ul > li {\n    margin: 0 0 0 1em; }\n\n/*\r\n  Common \r\n*/\n.wizard, .tabcontrol {\n  display: block;\n  width: 100%;\n  overflow: hidden;\n  /* Accessibility */ }\n  .wizard a, .tabcontrol a {\n    outline: 0; }\n  .wizard ul, .tabcontrol ul {\n    list-style: none !important;\n    padding: 0;\n    margin: 0; }\n    .wizard ul > li, .tabcontrol ul > li {\n      display: block;\n      padding: 0; }\n  .wizard > .steps .current-info, .tabcontrol > .steps .current-info {\n    position: absolute;\n    left: -999em; }\n  .wizard > .content > .title, .tabcontrol > .content > .title {\n    position: absolute;\n    left: -999em; }\n\n@media (max-width: 767.98px) {\n  .wizard > .steps > ul > li, .wizard.vertical > .steps, .wizard.vertical > .content {\n    width: 100%; } }\n\n.editable-clear-x {\n  background: url(\"../images/clear.png\") center center no-repeat; }\n\n.editableform-loading {\n  background: url(\"../images/loading.gif\") center center no-repeat; }\n\n.editable-checklist label {\n  display: block; }\n\n.dropzone {\n  border: 2px dashed rgba(50, 58, 70, 0.3);\n  background: #fff;\n  border-radius: 6px; }\n\n.dataTables_wrapper.container-fluid {\n  padding: 0; }\n\ntable.dataTable {\n  border-collapse: collapse !important;\n  margin-bottom: 15px !important; }\n  table.dataTable tbody > tr.selected, table.dataTable tbody > tr > .selected {\n    background-color: #348cd4; }\n    table.dataTable tbody > tr.selected td, table.dataTable tbody > tr > .selected td {\n      border-color: #348cd4; }\n  table.dataTable tbody td:focus {\n    outline: none !important; }\n  table.dataTable tbody th.focus, table.dataTable tbody td.focus {\n    outline: 2px solid #348cd4 !important;\n    outline-offset: -1px;\n    color: #348cd4;\n    background-color: rgba(52, 140, 212, 0.15); }\n\n.dataTables_info {\n  font-weight: 600; }\n\ntable.dataTable.dtr-inline.collapsed > tbody > tr[role=row] > td:first-child:before, table.dataTable.dtr-inline.collapsed > tbody > tr[role=row] > th:first-child:before {\n  -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);\n          box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);\n  background-color: #348cd4;\n  top: 0.85rem; }\n\ntable.dataTable.dtr-inline.collapsed > tbody > tr.parent > td:first-child:before, table.dataTable.dtr-inline.collapsed > tbody > tr.parent > th:first-child:before {\n  background-color: #f36270;\n  top: 0.85rem; }\n\ndiv.dt-button-info {\n  background-color: #348cd4;\n  border: none;\n  color: #fff;\n  -webkit-box-shadow: none;\n          box-shadow: none;\n  border-radius: 3px;\n  text-align: center;\n  z-index: 21; }\n  div.dt-button-info h2 {\n    border-bottom: none;\n    background-color: rgba(255, 255, 255, 0.2);\n    color: #fff; }\n\n@media (max-width: 767.98px) {\n  li.paginate_button.previous, li.paginate_button.next {\n    display: inline-block; }\n  li.paginate_button {\n    display: none; }\n  .dataTables_paginate ul {\n    text-align: center;\n    display: block;\n    margin: 1.5rem 0 0 !important; }\n  div.dt-buttons {\n    display: inline-table;\n    margin-bottom: 1.5rem; } }\n\n.activate-select .sorting_1 {\n  background-color: #f1f5f7; }\n\n.table-rep-plugin .dropdown-menu li.checkbox-row {\n  padding: 7px 15px; }\n\n.table-rep-plugin .table-responsive {\n  border: none; }\n\n.table-rep-plugin tbody th {\n  font-size: 14px;\n  font-weight: normal; }\n\n.table-rep-plugin .checkbox-row {\n  padding-left: 40px; }\n  .table-rep-plugin .checkbox-row label {\n    display: inline-block;\n    padding-left: 5px;\n    position: relative;\n    margin-bottom: 0; }\n    .table-rep-plugin .checkbox-row label::before {\n      -o-transition: 0.3s ease-in-out;\n      -webkit-transition: 0.3s ease-in-out;\n      background-color: #fff;\n      border-radius: 3px;\n      border: 1px solid #98a6ad;\n      content: \"\";\n      display: inline-block;\n      height: 17px;\n      left: 0;\n      margin-left: -20px;\n      position: absolute;\n      transition: 0.3s ease-in-out;\n      width: 17px;\n      outline: none; }\n    .table-rep-plugin .checkbox-row label::after {\n      color: #dee2e6;\n      display: inline-block;\n      font-size: 11px;\n      height: 16px;\n      left: 0;\n      margin-left: -20px;\n      padding-left: 3px;\n      padding-top: 1px;\n      position: absolute;\n      top: -1px;\n      width: 16px; }\n  .table-rep-plugin .checkbox-row input[type=\"checkbox\"] {\n    cursor: pointer;\n    opacity: 0;\n    z-index: 1;\n    outline: none; }\n    .table-rep-plugin .checkbox-row input[type=\"checkbox\"]:disabled + label {\n      opacity: 0.65; }\n  .table-rep-plugin .checkbox-row input[type=\"checkbox\"]:focus + label::before {\n    outline-offset: -2px;\n    outline: none; }\n  .table-rep-plugin .checkbox-row input[type=\"checkbox\"]:checked + label::after {\n    content: \"\\f00c\";\n    font-family: 'Font Awesome 5 Free';\n    font-weight: 900; }\n  .table-rep-plugin .checkbox-row input[type=\"checkbox\"]:disabled + label::before {\n    background-color: #f7f7f7;\n    cursor: not-allowed; }\n  .table-rep-plugin .checkbox-row input[type=\"checkbox\"]:checked + label::before {\n    background-color: #fff;\n    border-color: #348cd4; }\n  .table-rep-plugin .checkbox-row input[type=\"checkbox\"]:checked + label::after {\n    color: #348cd4; }\n\n.table-rep-plugin table.focus-on tbody tr.focused th, .table-rep-plugin table.focus-on tbody tr.focused td,\n.table-rep-plugin .sticky-table-header {\n  background: #348cd4;\n  background: #348cd4;\n  color: #fff;\n  border-color: #348cd4; }\n  .table-rep-plugin table.focus-on tbody tr.focused th table, .table-rep-plugin table.focus-on tbody tr.focused td table,\n  .table-rep-plugin .sticky-table-header table {\n    color: #fff; }\n\n.table-rep-plugin .fixed-solution .sticky-table-header {\n  top: 70px !important; }\n\n.table-rep-plugin .btn-default {\n  background-color: #fff;\n  border: 1px solid rgba(50, 58, 70, 0.3);\n  color: #323a46; }\n\n.table-rep-plugin .btn-primary {\n  background: #348cd4;\n  border-color: #348cd4;\n  color: #fff; }\n\n.table-rep-plugin .btn-group.pull-right {\n  float: right; }\n  .table-rep-plugin .btn-group.pull-right .dropdown-menu {\n    left: auto;\n    right: 0; }\n\n.table-rep-plugin .btn-toolbar {\n  display: block; }\n\n.tablesaw thead {\n  background: #f1f5f7;\n  background-image: none;\n  border: none; }\n  .tablesaw thead th {\n    text-shadow: none; }\n  .tablesaw thead tr:first-child th {\n    border: none;\n    font-weight: 500;\n    font-family: \"Roboto\", sans-serif; }\n\n.tablesaw td {\n  border-top: 1px solid #f1f5f7 !important; }\n\n.tablesaw td,\n.tablesaw tbody th {\n  font-size: inherit;\n  line-height: inherit;\n  padding: 10px !important; }\n\n.tablesaw-stack tbody tr,\n.tablesaw tbody tr {\n  border-bottom: none; }\n\n.tablesaw-bar .btn-select.btn-small:after,\n.tablesaw-bar .btn-select.btn-micro:after {\n  font-size: 8px;\n  padding-right: 10px; }\n\n.tablesaw-swipe .tablesaw-cell-persist {\n  -webkit-box-shadow: none;\n          box-shadow: none;\n  border-color: #f1f5f7; }\n\n.tablesaw-enhanced .tablesaw-bar .btn {\n  text-shadow: none;\n  background-image: none;\n  text-transform: none;\n  border: 1px solid #dee2e6;\n  padding: 3px 10px;\n  color: #323a46; }\n  .tablesaw-enhanced .tablesaw-bar .btn:after {\n    display: none; }\n\n.tablesaw-enhanced .tablesaw-bar .btn.btn-select:hover {\n  background: #fff; }\n\n.tablesaw-enhanced .tablesaw-bar .btn:hover,\n.tablesaw-enhanced .tablesaw-bar .btn:focus,\n.tablesaw-enhanced .tablesaw-bar .btn:active {\n  color: #348cd4 !important;\n  background-color: #f1f5f7;\n  outline: none !important;\n  -webkit-box-shadow: none !important;\n          box-shadow: none !important;\n  background-image: none; }\n\n.tablesaw-columntoggle-popup .btn-group {\n  display: block; }\n\n.tablesaw-swipe .tablesaw-swipe-cellpersist {\n  border-right: 2px solid #f1f5f7; }\n\n.tablesaw-sortable-btn {\n  cursor: pointer; }\n\n.tablesaw-swipe-cellpersist {\n  width: auto !important; }\n\n.button-list {\n  margin-left: -8px;\n  margin-bottom: -12px; }\n  .button-list .btn {\n    margin-bottom: 12px;\n    margin-left: 8px; }\n\n.icons-list-demo div {\n  cursor: pointer;\n  line-height: 45px;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  display: block;\n  overflow: hidden; }\n  .icons-list-demo div p {\n    margin-bottom: 0;\n    line-height: inherit; }\n\n.icons-list-demo i {\n  text-align: center;\n  vertical-align: middle;\n  font-size: 22px;\n  width: 50px;\n  height: 50px;\n  line-height: 50px;\n  margin-right: 12px;\n  color: rgba(50, 58, 70, 0.7);\n  border-radius: 3px;\n  display: inline-block;\n  -webkit-transition: all 0.2s;\n  transition: all 0.2s; }\n\n.icons-list-demo .col-lg-4 {\n  background-clip: padding-box;\n  margin-top: 10px; }\n  .icons-list-demo .col-lg-4:hover i {\n    color: #fff;\n    background: #348cd4; }\n\n.grid-structure .grid-container {\n  background-color: #f1f5f7;\n  margin-top: 10px;\n  font-size: .8rem;\n  font-weight: 500;\n  padding: 10px 20px; }\n\n.home-btn {\n  position: absolute;\n  top: 15px;\n  right: 25px; }\n\n.checkmark {\n  width: 100px;\n  margin: 0 auto;\n  padding: 20px 0; }\n\n.path {\n  stroke-dasharray: 1000;\n  stroke-dashoffset: 0;\n  animation: dash 2s ease-in-out;\n  -webkit-animation: dash 2s ease-in-out; }\n\n.spin {\n  animation: spin 2s;\n  -webkit-animation: spin 2s;\n  transform-origin: 50% 50%;\n  -webkit-transform-origin: 50% 50%; }\n\n@keyframes dash {\n  0% {\n    stroke-dashoffset: 1000; }\n  100% {\n    stroke-dashoffset: 0; } }\n\n@keyframes spin {\n  0% {\n    -webkit-transform: rotate(0deg); }\n  100% {\n    -webkit-transform: rotate(360deg); } }\n\n/* =============\r\n   Email\r\n============= */\n.inbox-leftbar {\n  width: 240px;\n  float: left;\n  padding: 0 20px 20px 10px; }\n\n.inbox-rightbar {\n  margin-left: 250px; }\n\n.message-list {\n  display: block;\n  padding-left: 0; }\n  .message-list li {\n    position: relative;\n    display: block;\n    height: 50px;\n    line-height: 50px;\n    cursor: default;\n    -webkit-transition-duration: .3s;\n            transition-duration: .3s; }\n    .message-list li a {\n      color: #6c757d; }\n    .message-list li:hover {\n      background: rgba(152, 166, 173, 0.15);\n      -webkit-transition-duration: .05s;\n              transition-duration: .05s; }\n    .message-list li .col-mail {\n      float: left;\n      position: relative; }\n    .message-list li .col-mail-1 {\n      width: 320px; }\n      .message-list li .col-mail-1 .star-toggle,\n      .message-list li .col-mail-1 .checkbox-wrapper-mail,\n      .message-list li .col-mail-1 .dot {\n        display: block;\n        float: left; }\n      .message-list li .col-mail-1 .dot {\n        border: 4px solid transparent;\n        border-radius: 100px;\n        margin: 22px 26px 0;\n        height: 0;\n        width: 0;\n        line-height: 0;\n        font-size: 0; }\n      .message-list li .col-mail-1 .checkbox-wrapper-mail {\n        margin: 15px 10px 0 20px; }\n      .message-list li .col-mail-1 .star-toggle {\n        margin-top: 18px;\n        margin-left: 5px; }\n      .message-list li .col-mail-1 .title {\n        position: absolute;\n        top: 15px;\n        left: 110px;\n        right: 0;\n        text-overflow: ellipsis;\n        overflow: hidden;\n        white-space: nowrap; }\n    .message-list li .col-mail-2 {\n      position: absolute;\n      top: 0;\n      left: 320px;\n      right: 0;\n      bottom: 0; }\n      .message-list li .col-mail-2 .subject,\n      .message-list li .col-mail-2 .date {\n        position: absolute;\n        top: 0; }\n      .message-list li .col-mail-2 .subject {\n        left: 0;\n        right: 200px;\n        text-overflow: ellipsis;\n        overflow: hidden;\n        white-space: nowrap; }\n      .message-list li .col-mail-2 .date {\n        right: 0;\n        width: 170px;\n        padding-left: 80px; }\n  .message-list li.active, .message-list li.selected {\n    background: rgba(152, 166, 173, 0.15);\n    -webkit-transition-duration: .05s;\n            transition-duration: .05s; }\n  .message-list li.active,\n  .message-list li.active:hover {\n    -webkit-box-shadow: inset 3px 0 0 #348cd4;\n            box-shadow: inset 3px 0 0 #348cd4; }\n  .message-list li.unread a {\n    font-weight: 600;\n    color: #272e37; }\n  .message-list li.blue-dot .col-mail-1 .dot {\n    border-color: #348cd4; }\n  .message-list li.orange-dot .col-mail-1 .dot {\n    border-color: #f9bc0b; }\n  .message-list li.green-dot .col-mail-1 .dot {\n    border-color: #3ec396; }\n  .message-list .checkbox-wrapper-mail {\n    cursor: pointer;\n    height: 20px;\n    width: 20px;\n    position: relative;\n    display: inline-block;\n    -webkit-box-shadow: inset 0 0 0 1px #98a6ad;\n            box-shadow: inset 0 0 0 1px #98a6ad;\n    border-radius: 1px; }\n    .message-list .checkbox-wrapper-mail input {\n      opacity: 0;\n      cursor: pointer; }\n    .message-list .checkbox-wrapper-mail input:checked ~ label {\n      opacity: 1; }\n    .message-list .checkbox-wrapper-mail label {\n      position: absolute;\n      top: 3px;\n      left: 3px;\n      right: 3px;\n      bottom: 3px;\n      cursor: pointer;\n      background: #98a6ad;\n      opacity: 0;\n      margin-bottom: 0 !important;\n      -webkit-transition-duration: .05s;\n              transition-duration: .05s; }\n    .message-list .checkbox-wrapper-mail label:active {\n      background: #87949b; }\n\n.mail-list a {\n  font-family: \"Roboto\", sans-serif;\n  vertical-align: middle;\n  color: #6c757d;\n  padding: 10px 12px;\n  display: block; }\n\n@media (max-width: 648px) {\n  .inbox-leftbar {\n    width: 100%; }\n  .inbox-rightbar {\n    margin-left: 0; } }\n\n@media (max-width: 520px) {\n  .message-list li .col-mail-1 {\n    width: 150px; }\n    .message-list li .col-mail-1 .title {\n      left: 80px; }\n  .message-list li .col-mail-2 {\n    left: 160px; }\n    .message-list li .col-mail-2 .date {\n      text-align: right;\n      padding-right: 10px;\n      padding-left: 20px; } }\n\n/* =============\r\n   Timeline\r\n============= */\n.timeline {\n  border-collapse: collapse;\n  border-spacing: 0;\n  display: table;\n  margin-bottom: 50px;\n  position: relative;\n  table-layout: fixed;\n  width: 100%; }\n  .timeline .time-show {\n    margin-bottom: 30px;\n    margin-right: -75px;\n    margin-top: 30px;\n    position: relative; }\n    .timeline .time-show a {\n      color: #fff; }\n  .timeline:before {\n    background-color: rgba(65, 77, 95, 0.3);\n    bottom: 0;\n    content: \"\";\n    left: 50%;\n    position: absolute;\n    top: 30px;\n    width: 2px;\n    z-index: 0; }\n  .timeline .timeline-icon {\n    -webkit-border-radius: 50%;\n    background: #414d5f;\n    border-radius: 50%;\n    color: #fff;\n    display: block;\n    height: 20px;\n    left: -54px;\n    margin-top: -10px;\n    position: absolute;\n    text-align: center;\n    top: 50%;\n    width: 20px; }\n    .timeline .timeline-icon i {\n      color: #fff;\n      font-size: 13px;\n      margin-top: 1px;\n      position: absolute;\n      left: 4px; }\n  .timeline .time-icon:before {\n    font-size: 16px;\n    margin-top: 5px; }\n\nh3.timeline-title {\n  color: #414d5f;\n  font-size: 20px;\n  font-weight: 400;\n  margin: 0 0 5px;\n  text-transform: uppercase; }\n\n.timeline-item {\n  display: table-row; }\n  .timeline-item:before {\n    content: \"\";\n    display: block;\n    width: 50%; }\n  .timeline-item .timeline-desk .arrow {\n    border-bottom: 12px solid transparent;\n    border-right: 12px solid rgba(247, 247, 247, 0.3) !important;\n    border-top: 12px solid transparent;\n    display: block;\n    height: 0;\n    left: -12px;\n    margin-top: -12px;\n    position: absolute;\n    top: 50%;\n    width: 0; }\n  .timeline-item .timeline-desk .timeline-box {\n    padding: 20px; }\n  .timeline-item .timeline-date {\n    margin-bottom: 10px; }\n\n.timeline-item.alt:after {\n  content: \"\";\n  display: block;\n  width: 50%; }\n\n.timeline-item.alt .timeline-desk .arrow-alt {\n  border-bottom: 12px solid transparent;\n  border-left: 12px solid rgba(247, 247, 247, 0.9) !important;\n  border-top: 12px solid transparent;\n  display: block;\n  height: 0;\n  left: auto;\n  margin-top: -12px;\n  position: absolute;\n  right: -12px;\n  top: 50%;\n  width: 0; }\n\n.timeline-item.alt .timeline-desk .album {\n  float: right;\n  margin-top: 20px; }\n  .timeline-item.alt .timeline-desk .album a {\n    float: right;\n    margin-left: 5px; }\n\n.timeline-item.alt .timeline-icon {\n  left: auto;\n  right: -56px; }\n\n.timeline-item.alt:before {\n  display: none; }\n\n.timeline-item.alt .panel {\n  margin-left: 0;\n  margin-right: 45px; }\n\n.timeline-item.alt h4 {\n  text-align: right; }\n\n.timeline-item.alt p {\n  text-align: right; }\n\n.timeline-item.alt .timeline-date {\n  text-align: right; }\n\n.timeline-desk {\n  display: table-cell;\n  vertical-align: top;\n  width: 50%; }\n  .timeline-desk h4 {\n    font-size: 16px;\n    margin: 0; }\n  .timeline-desk .panel {\n    background: rgba(247, 247, 247, 0.9);\n    display: block;\n    margin-bottom: 5px;\n    margin-left: 45px;\n    position: relative;\n    text-align: left;\n    border: 0; }\n  .timeline-desk h5 span {\n    color: #414d5f;\n    display: block;\n    font-size: 12px;\n    margin-bottom: 4px; }\n  .timeline-desk p {\n    color: #999999;\n    font-size: 14px;\n    margin-bottom: 0; }\n  .timeline-desk .album {\n    margin-top: 12px; }\n    .timeline-desk .album a {\n      float: left;\n      margin-right: 5px; }\n    .timeline-desk .album img {\n      height: 36px;\n      width: auto;\n      border-radius: 3px; }\n  .timeline-desk .notification {\n    background: none repeat scroll 0 0 #fff;\n    margin-top: 20px;\n    padding: 8px; }\n\n/* =============\r\n   Account Pages\r\n============= */\n.home-wrapper {\n  margin: 10% 0; }\n\n.bg-accpunt-pages {\n  background-color: #348cd4;\n  background: #348cd4;\n  padding-bottom: 0;\n  min-height: 100px; }\n\n.wrapper-page {\n  display: table;\n  height: 100vh;\n  width: 100%; }\n\n.account-box {\n  position: relative;\n  max-width: 460px;\n  margin: 20px auto;\n  background-color: #fff;\n  border-radius: 5px; }\n  .account-box .account-content {\n    padding: 30px; }\n  .account-box .account-btn {\n    position: absolute;\n    left: 0;\n    right: 0; }\n\n.account-logo-box {\n  padding: 30px 30px 0 30px; }\n\n.text-error {\n  color: #348cd4;\n  text-shadow: rgba(52, 140, 212, 0.3) 5px 1px, rgba(52, 140, 212, 0.2) 10px 3px;\n  font-size: 84px;\n  font-weight: 700;\n  line-height: 90px; }\n\n.checkmark {\n  width: 100px;\n  margin: 0 auto;\n  padding: 20px 0; }\n\n.path {\n  stroke-dasharray: 1000;\n  stroke-dashoffset: 0;\n  animation: dash 2s ease-in-out;\n  -webkit-animation: dash 2s ease-in-out; }\n\n.spin {\n  animation: spin 2s;\n  -webkit-animation: spin 2s;\n  transform-origin: 50% 50%;\n  -webkit-transform-origin: 50% 50%; }\n\n@-webkit-keyframes dash {\n  0% {\n    stroke-dashoffset: 1000; }\n  100% {\n    stroke-dashoffset: 0; } }\n\n@keyframes dash {\n  0% {\n    stroke-dashoffset: 1000; }\n  100% {\n    stroke-dashoffset: 0; } }\n\n@-webkit-keyframes spin {\n  0% {\n    -webkit-transform: rotate(0deg); }\n  100% {\n    -webkit-transform: rotate(360deg); } }\n\n@keyframes spin {\n  0% {\n    -webkit-transform: rotate(0deg); }\n  100% {\n    -webkit-transform: rotate(360deg); } }\n\n@-webkit-keyframes text {\n  0% {\n    opacity: 0; }\n  100% {\n    opacity: 1; } }\n\n@keyframes text {\n  0% {\n    opacity: 0; }\n  100% {\n    opacity: 1; } }\n\n/* =============\r\n   Pricing\r\n============= */\n.pricing-column .ribbon-pricing {\n  width: 160px;\n  margin: -15px auto -10px;\n  padding-bottom: 2px;\n  line-height: 22px;\n  text-align: center;\n  z-index: 1;\n  position: relative; }\n\n.pricing-column .plan-title {\n  font-family: \"Roboto\", sans-serif;\n  letter-spacing: 1px; }\n\n.pricing-column .plan-price {\n  font-size: 48px;\n  font-family: \"Roboto\", sans-serif; }\n\n.pricing-column .plan-duration {\n  font-size: 15px;\n  color: rgba(255, 255, 255, 0.7); }\n\n.pricing-column .plan-stats {\n  padding: 30px 20px 15px; }\n  .pricing-column .plan-stats li {\n    margin-bottom: 15px;\n    line-height: 24px; }\n\nhtml {\n  direction: rtl; }\n\nbody {\n  text-align: right; }\n\n.dropdown-menu.show {\n  text-align: right;\n  left: auto !important;\n  right: 0;\n  bottom: auto; }\n\n.dropdown-menu-right {\n  right: auto !important;\n  left: 0 !important; }\n  .dropdown-menu-right.show {\n    left: 0 !important; }\n\n.btn-group, .btn-group-vertical {\n  direction: ltr; }\n\nul {\n  padding-right: 0; }\n\n.pagination .page-item:first-child .page-link {\n  margin-right: 0;\n  border-top-left-radius: 0px;\n  border-bottom-left-radius: 0px;\n  border-top-right-radius: 0.25rem;\n  border-bottom-right-radius: 0.25rem; }\n\n.pagination .page-item:last-child .page-link {\n  border-top-right-radius: 0px;\n  border-bottom-right-radius: 0px;\n  border-top-left-radius: 0.25rem;\n  border-bottom-left-radius: 0.25rem; }\n\n.blockquote-reverse {\n  text-align: left !important; }\n\ndd {\n  margin-right: 0; }\n\n.modal-header .close {\n  margin: -1rem auto -1rem -1rem;\n  left: 0px; }\n\n.modal-footer > :not(:first-child) {\n  margin-right: .25rem;\n  margin-left: 0; }\n\n.modal-footer > :not(:last-child) {\n  margin-left: .25rem;\n  margin-right: 0; }\n\n.alert-dismissible {\n  padding-left: 3.9rem;\n  padding-right: 1.25rem; }\n  .alert-dismissible .close {\n    left: 0;\n    right: auto; }\n\n.breadcrumb-item + .breadcrumb-item {\n  padding-right: 0.5rem;\n  padding-left: 0px; }\n  .breadcrumb-item + .breadcrumb-item::before {\n    padding-left: 0.5rem;\n    content: \"\\F141\";\n    padding-right: 0px; }\n\n.form-check-inline {\n  margin-left: .75rem;\n  margin-right: 0; }\n\n.custom-control {\n  padding-right: 1.5rem;\n  padding-left: 0; }\n\n.custom-control-label::before {\n  left: auto;\n  right: -1.5rem; }\n\n.custom-control-label::after {\n  left: auto;\n  right: -1.5rem; }\n\n.custom-switch {\n  padding-right: 2.25rem;\n  padding-left: 0; }\n  .custom-switch .custom-control-label::before {\n    right: -2.25rem;\n    left: auto; }\n  .custom-switch .custom-control-label::after {\n    right: calc(-2.25rem + 2px);\n    left: auto; }\n  .custom-switch .custom-control-input:checked ~ .custom-control-label::after {\n    -webkit-transform: translateX(-0.75rem);\n            transform: translateX(-0.75rem); }\n\n.custom-file-label::after {\n  right: auto;\n  left: 0;\n  border-right: inherit; }\n\n.input-group-prepend {\n  margin-left: -1px;\n  margin-right: 0; }\n\n.input-group-append {\n  margin-right: -1px;\n  margin-left: 0; }\n\n.input-group > .input-group-prepend > .btn,\n.input-group > .input-group-prepend > .input-group-text,\n.input-group > .input-group-append:not(:last-child) > .btn,\n.input-group > .input-group-append:not(:last-child) > .input-group-text,\n.input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle),\n.input-group > .input-group-append:last-child > .input-group-text:not(:last-child),\n.input-group > .custom-select:not(:last-child),\n.input-group > .form-control:not(:last-child) {\n  border-top-right-radius: 0.2rem;\n  border-bottom-right-radius: 0.2rem;\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0; }\n\n.input-group > .input-group-append > .btn,\n.input-group > .input-group-append > .input-group-text,\n.input-group > .input-group-prepend:not(:first-child) > .btn,\n.input-group > .input-group-prepend:not(:first-child) > .input-group-text,\n.input-group > .input-group-prepend:first-child > .btn:not(:first-child),\n.input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child),\n.input-group > .custom-select:not(:first-child),\n.input-group > .form-control:not(:first-child) {\n  border-top-left-radius: 0.2rem;\n  border-bottom-left-radius: 0.2rem;\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0; }\n\n.m-0 {\n  margin: 0 !important; }\n\n.mt-0,\n.my-0 {\n  margin-top: 0 !important; }\n\n.mr-0,\n.mx-0 {\n  margin-left: 0 !important;\n  margin-right: 0 !important; }\n\n.mb-0,\n.my-0 {\n  margin-bottom: 0 !important; }\n\n.ml-0,\n.mx-0 {\n  margin-right: 0 !important;\n  margin-left: 0 !important; }\n\n.m-1 {\n  margin: 0.375rem !important; }\n\n.mt-1,\n.my-1 {\n  margin-top: 0.375rem !important; }\n\n.mr-1,\n.mx-1 {\n  margin-left: 0.375rem !important;\n  margin-right: 0 !important; }\n\n.mb-1,\n.my-1 {\n  margin-bottom: 0.375rem !important; }\n\n.ml-1,\n.mx-1 {\n  margin-right: 0.375rem !important;\n  margin-left: 0 !important; }\n\n.m-2 {\n  margin: 0.75rem !important; }\n\n.mt-2,\n.my-2 {\n  margin-top: 0.75rem !important; }\n\n.mr-2,\n.mx-2 {\n  margin-left: 0.75rem !important;\n  margin-right: 0 !important; }\n\n.mb-2,\n.my-2 {\n  margin-bottom: 0.75rem !important; }\n\n.ml-2,\n.mx-2 {\n  margin-right: 0.75rem !important;\n  margin-left: 0 !important; }\n\n.m-3 {\n  margin: 1.5rem !important; }\n\n.mt-3,\n.my-3 {\n  margin-top: 1.5rem !important; }\n\n.mr-3,\n.mx-3 {\n  margin-left: 1.5rem !important;\n  margin-right: 0 !important; }\n\n.mb-3,\n.my-3 {\n  margin-bottom: 1.5rem !important; }\n\n.ml-3,\n.mx-3 {\n  margin-right: 1.5rem !important;\n  margin-left: 0 !important; }\n\n.m-4 {\n  margin: 2.25rem !important; }\n\n.mt-4,\n.my-4 {\n  margin-top: 2.25rem !important; }\n\n.mr-4,\n.mx-4 {\n  margin-left: 2.25rem !important;\n  margin-right: 0 !important; }\n\n.mb-4,\n.my-4 {\n  margin-bottom: 2.25rem !important; }\n\n.ml-4,\n.mx-4 {\n  margin-right: 2.25rem !important;\n  margin-left: 0 !important; }\n\n.m-5 {\n  margin: 4.5rem !important; }\n\n.mt-5,\n.my-5 {\n  margin-top: 4.5rem !important; }\n\n.mr-5,\n.mx-5 {\n  margin-left: 4.5rem !important;\n  margin-right: 0 !important; }\n\n.mb-5,\n.my-5 {\n  margin-bottom: 4.5rem !important; }\n\n.ml-5,\n.mx-5 {\n  margin-right: 4.5rem !important;\n  margin-left: 0 !important; }\n\n.p-0 {\n  padding: 0 !important; }\n\n.pt-0,\n.py-0 {\n  padding-top: 0 !important; }\n\n.pr-0,\n.px-0 {\n  padding-left: 0 !important;\n  padding-right: 0 !important; }\n\n.pb-0,\n.py-0 {\n  padding-bottom: 0 !important; }\n\n.pl-0,\n.px-0 {\n  padding-right: 0 !important;\n  padding-left: 0 !important; }\n\n.p-1 {\n  padding: 0.375rem !important; }\n\n.pt-1,\n.py-1 {\n  padding-top: 0.375rem !important; }\n\n.pr-1,\n.px-1 {\n  padding-left: 0.375rem !important;\n  padding-right: 0 !important; }\n\n.pb-1,\n.py-1 {\n  padding-bottom: 0.375rem !important; }\n\n.pl-1,\n.px-1 {\n  padding-right: 0.375rem !important;\n  padding-left: 0 !important; }\n\n.p-2 {\n  padding: 0.75rem !important; }\n\n.pt-2,\n.py-2 {\n  padding-top: 0.75rem !important; }\n\n.pr-2,\n.px-2 {\n  padding-left: 0.75rem !important;\n  padding-right: 0 !important; }\n\n.pb-2,\n.py-2 {\n  padding-bottom: 0.75rem !important; }\n\n.pl-2,\n.px-2 {\n  padding-right: 0.75rem !important;\n  padding-left: 0 !important; }\n\n.p-3 {\n  padding: 1.5rem !important; }\n\n.pt-3,\n.py-3 {\n  padding-top: 1.5rem !important; }\n\n.pr-3,\n.px-3 {\n  padding-left: 1.5rem !important;\n  padding-right: 0 !important; }\n\n.pb-3,\n.py-3 {\n  padding-bottom: 1.5rem !important; }\n\n.pl-3,\n.px-3 {\n  padding-right: 1.5rem !important;\n  padding-left: 0 !important; }\n\n.p-4 {\n  padding: 2.25rem !important; }\n\n.pt-4,\n.py-4 {\n  padding-top: 2.25rem !important; }\n\n.pr-4,\n.px-4 {\n  padding-left: 2.25rem !important;\n  padding-right: 0 !important; }\n\n.pb-4,\n.py-4 {\n  padding-bottom: 2.25rem !important; }\n\n.pl-4,\n.px-4 {\n  padding-right: 2.25rem !important;\n  padding-left: 0 !important; }\n\n.p-5 {\n  padding: 4.5rem !important; }\n\n.pt-5,\n.py-5 {\n  padding-top: 4.5rem !important; }\n\n.pr-5,\n.px-5 {\n  padding-left: 4.5rem !important;\n  padding-right: 0 !important; }\n\n.pb-5,\n.py-5 {\n  padding-bottom: 4.5rem !important; }\n\n.pl-5,\n.px-5 {\n  padding-right: 4.5rem !important;\n  padding-left: 0 !important; }\n\n.m-n1 {\n  margin: -0.375rem !important; }\n\n.mt-n1,\n.my-n1 {\n  margin-top: -0.375rem !important; }\n\n.mr-n1,\n.mx-n1 {\n  margin-right: -0.375rem !important; }\n\n.mb-n1,\n.my-n1 {\n  margin-bottom: -0.375rem !important; }\n\n.ml-n1,\n.mx-n1 {\n  margin-left: -0.375rem !important; }\n\n.m-n2 {\n  margin: -0.75rem !important; }\n\n.mt-n2,\n.my-n2 {\n  margin-top: -0.75rem !important; }\n\n.mr-n2,\n.mx-n2 {\n  margin-right: -0.75rem !important; }\n\n.mb-n2,\n.my-n2 {\n  margin-bottom: -0.75rem !important; }\n\n.ml-n2,\n.mx-n2 {\n  margin-left: -0.75rem !important; }\n\n.m-n3 {\n  margin: -1.5rem !important; }\n\n.mt-n3,\n.my-n3 {\n  margin-top: -1.5rem !important; }\n\n.mr-n3,\n.mx-n3 {\n  margin-right: -1.5rem !important; }\n\n.mb-n3,\n.my-n3 {\n  margin-bottom: -1.5rem !important; }\n\n.ml-n3,\n.mx-n3 {\n  margin-left: -1.5rem !important; }\n\n.m-n4 {\n  margin: -2.25rem !important; }\n\n.mt-n4,\n.my-n4 {\n  margin-top: -2.25rem !important; }\n\n.mr-n4,\n.mx-n4 {\n  margin-right: -2.25rem !important; }\n\n.mb-n4,\n.my-n4 {\n  margin-bottom: -2.25rem !important; }\n\n.ml-n4,\n.mx-n4 {\n  margin-left: -2.25rem !important; }\n\n.m-n5 {\n  margin: -4.5rem !important; }\n\n.mt-n5,\n.my-n5 {\n  margin-top: -4.5rem !important; }\n\n.mr-n5,\n.mx-n5 {\n  margin-right: -4.5rem !important; }\n\n.mb-n5,\n.my-n5 {\n  margin-bottom: -4.5rem !important; }\n\n.ml-n5,\n.mx-n5 {\n  margin-left: -4.5rem !important; }\n\n.m-auto {\n  margin: auto !important; }\n\n.mt-auto,\n.my-auto {\n  margin-top: auto !important; }\n\n.mr-auto,\n.mx-auto {\n  margin-left: auto !important;\n  margin-right: inherit !important; }\n\n.mb-auto,\n.my-auto {\n  margin-bottom: auto !important; }\n\n.ml-auto,\n.mx-auto {\n  margin-right: auto !important;\n  margin-left: auto !important; }\n\n@media (min-width: 576px) {\n  .m-sm-0 {\n    margin: 0 !important; }\n  .mt-sm-0,\n  .my-sm-0 {\n    margin-top: 0 !important; }\n  .mr-sm-0,\n  .mx-sm-0 {\n    margin-left: 0 !important;\n    margin-right: 0 !important; }\n  .mb-sm-0,\n  .my-sm-0 {\n    margin-bottom: 0 !important; }\n  .ml-sm-0,\n  .mx-sm-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important; }\n  .m-sm-1 {\n    margin: 0.375rem !important; }\n  .mt-sm-1,\n  .my-sm-1 {\n    margin-top: 0.375rem !important; }\n  .mr-sm-1,\n  .mx-sm-1 {\n    margin-left: 0.375rem !important;\n    margin-right: 0 !important; }\n  .mb-sm-1,\n  .my-sm-1 {\n    margin-bottom: 0.375rem !important; }\n  .ml-sm-1,\n  .mx-sm-1 {\n    margin-right: 0.375rem !important;\n    margin-left: 0 !important; }\n  .m-sm-2 {\n    margin: 0.75rem !important; }\n  .mt-sm-2,\n  .my-sm-2 {\n    margin-top: 0.75rem !important; }\n  .mr-sm-2,\n  .mx-sm-2 {\n    margin-left: 0.75rem !important;\n    margin-right: 0 !important; }\n  .mb-sm-2,\n  .my-sm-2 {\n    margin-bottom: 0.75rem !important; }\n  .ml-sm-2,\n  .mx-sm-2 {\n    margin-right: 0.75rem !important;\n    margin-left: 0 !important; }\n  .m-sm-3 {\n    margin: 1.5rem !important; }\n  .mt-sm-3,\n  .my-sm-3 {\n    margin-top: 1.5rem !important; }\n  .mr-sm-3,\n  .mx-sm-3 {\n    margin-left: 1.5rem !important;\n    margin-right: 0 !important; }\n  .mb-sm-3,\n  .my-sm-3 {\n    margin-bottom: 1.5rem !important; }\n  .ml-sm-3,\n  .mx-sm-3 {\n    margin-right: 1.5rem !important;\n    margin-left: 0 !important; }\n  .m-sm-4 {\n    margin: 2.25rem !important; }\n  .mt-sm-4,\n  .my-sm-4 {\n    margin-top: 2.25rem !important; }\n  .mr-sm-4,\n  .mx-sm-4 {\n    margin-left: 2.25rem !important;\n    margin-right: 0 !important; }\n  .mb-sm-4,\n  .my-sm-4 {\n    margin-bottom: 2.25rem !important; }\n  .ml-sm-4,\n  .mx-sm-4 {\n    margin-right: 2.25rem !important;\n    margin-left: 0 !important; }\n  .m-sm-5 {\n    margin: 4.5rem !important; }\n  .mt-sm-5,\n  .my-sm-5 {\n    margin-top: 4.5rem !important; }\n  .mr-sm-5,\n  .mx-sm-5 {\n    margin-left: 4.5rem !important;\n    margin-right: 0 !important; }\n  .mb-sm-5,\n  .my-sm-5 {\n    margin-bottom: 4.5rem !important; }\n  .ml-sm-5,\n  .mx-sm-5 {\n    margin-right: 4.5rem !important;\n    margin-left: 0 !important; }\n  .p-sm-0 {\n    padding: 0 !important; }\n  .pt-sm-0,\n  .py-sm-0 {\n    padding-top: 0 !important; }\n  .pr-sm-0,\n  .px-sm-0 {\n    padding-left: 0 !important;\n    padding-right: 0 !important; }\n  .pb-sm-0,\n  .py-sm-0 {\n    padding-bottom: 0 !important; }\n  .pl-sm-0,\n  .px-sm-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important; }\n  .p-sm-1 {\n    padding: 0.375rem !important; }\n  .pt-sm-1,\n  .py-sm-1 {\n    padding-top: 0.375rem !important; }\n  .pr-sm-1,\n  .px-sm-1 {\n    padding-left: 0.375rem !important;\n    padding-right: 0 !important; }\n  .pb-sm-1,\n  .py-sm-1 {\n    padding-bottom: 0.375rem !important; }\n  .pl-sm-1,\n  .px-sm-1 {\n    padding-right: 0.375rem !important;\n    padding-left: 0 !important; }\n  .p-sm-2 {\n    padding: 0.75rem !important; }\n  .pt-sm-2,\n  .py-sm-2 {\n    padding-top: 0.75rem !important; }\n  .pr-sm-2,\n  .px-sm-2 {\n    padding-left: 0.75rem !important;\n    padding-right: 0 !important; }\n  .pb-sm-2,\n  .py-sm-2 {\n    padding-bottom: 0.75rem !important; }\n  .pl-sm-2,\n  .px-sm-2 {\n    padding-right: 0.75rem !important;\n    padding-left: 0 !important; }\n  .p-sm-3 {\n    padding: 1.5rem !important; }\n  .pt-sm-3,\n  .py-sm-3 {\n    padding-top: 1.5rem !important; }\n  .pr-sm-3,\n  .px-sm-3 {\n    padding-left: 1.5rem !important;\n    padding-right: 0 !important; }\n  .pb-sm-3,\n  .py-sm-3 {\n    padding-bottom: 1.5rem !important; }\n  .pl-sm-3,\n  .px-sm-3 {\n    padding-right: 1.5rem !important;\n    padding-left: 0 !important; }\n  .p-sm-4 {\n    padding: 2.25rem !important; }\n  .pt-sm-4,\n  .py-sm-4 {\n    padding-top: 2.25rem !important; }\n  .pr-sm-4,\n  .px-sm-4 {\n    padding-left: 2.25rem !important;\n    padding-right: 0 !important; }\n  .pb-sm-4,\n  .py-sm-4 {\n    padding-bottom: 2.25rem !important; }\n  .pl-sm-4,\n  .px-sm-4 {\n    padding-right: 2.25rem !important;\n    padding-left: 0 !important; }\n  .p-sm-5 {\n    padding: 4.5rem !important; }\n  .pt-sm-5,\n  .py-sm-5 {\n    padding-top: 4.5rem !important; }\n  .pr-sm-5,\n  .px-sm-5 {\n    padding-left: 4.5rem !important;\n    padding-right: 0 !important; }\n  .pb-sm-5,\n  .py-sm-5 {\n    padding-bottom: 4.5rem !important; }\n  .pl-sm-5,\n  .px-sm-5 {\n    padding-right: 4.5rem !important;\n    padding-left: 0 !important; }\n  .m-sm-n1 {\n    margin: -0.375rem !important; }\n  .mt-sm-n1,\n  .my-sm-n1 {\n    margin-top: -0.375rem !important; }\n  .mr-sm-n1,\n  .mx-sm-n1 {\n    margin-right: -0.375rem !important; }\n  .mb-sm-n1,\n  .my-sm-n1 {\n    margin-bottom: -0.375rem !important; }\n  .ml-sm-n1,\n  .mx-sm-n1 {\n    margin-left: -0.375rem !important; }\n  .m-sm-n2 {\n    margin: -0.75rem !important; }\n  .mt-sm-n2,\n  .my-sm-n2 {\n    margin-top: -0.75rem !important; }\n  .mr-sm-n2,\n  .mx-sm-n2 {\n    margin-right: -0.75rem !important; }\n  .mb-sm-n2,\n  .my-sm-n2 {\n    margin-bottom: -0.75rem !important; }\n  .ml-sm-n2,\n  .mx-sm-n2 {\n    margin-left: -0.75rem !important; }\n  .m-sm-n3 {\n    margin: -1.5rem !important; }\n  .mt-sm-n3,\n  .my-sm-n3 {\n    margin-top: -1.5rem !important; }\n  .mr-sm-n3,\n  .mx-sm-n3 {\n    margin-right: -1.5rem !important; }\n  .mb-sm-n3,\n  .my-sm-n3 {\n    margin-bottom: -1.5rem !important; }\n  .ml-sm-n3,\n  .mx-sm-n3 {\n    margin-left: -1.5rem !important; }\n  .m-sm-n4 {\n    margin: -2.25rem !important; }\n  .mt-sm-n4,\n  .my-sm-n4 {\n    margin-top: -2.25rem !important; }\n  .mr-sm-n4,\n  .mx-sm-n4 {\n    margin-right: -2.25rem !important; }\n  .mb-sm-n4,\n  .my-sm-n4 {\n    margin-bottom: -2.25rem !important; }\n  .ml-sm-n4,\n  .mx-sm-n4 {\n    margin-left: -2.25rem !important; }\n  .m-sm-n5 {\n    margin: -4.5rem !important; }\n  .mt-sm-n5,\n  .my-sm-n5 {\n    margin-top: -4.5rem !important; }\n  .mr-sm-n5,\n  .mx-sm-n5 {\n    margin-right: -4.5rem !important; }\n  .mb-sm-n5,\n  .my-sm-n5 {\n    margin-bottom: -4.5rem !important; }\n  .ml-sm-n5,\n  .mx-sm-n5 {\n    margin-left: -4.5rem !important; }\n  .m-sm-auto {\n    margin: auto !important; }\n  .mt-sm-auto,\n  .my-sm-auto {\n    margin-top: auto !important; }\n  .mr-sm-auto,\n  .mx-sm-auto {\n    margin-left: auto !important;\n    margin-right: inherit !important; }\n  .mb-sm-auto,\n  .my-sm-auto {\n    margin-bottom: auto !important; }\n  .ml-sm-auto,\n  .mx-sm-auto {\n    margin-right: auto !important;\n    margin-left: auto !important; } }\n\n@media (min-width: 768px) {\n  .m-md-0 {\n    margin: 0 !important; }\n  .mt-md-0,\n  .my-md-0 {\n    margin-top: 0 !important; }\n  .mr-md-0,\n  .mx-md-0 {\n    margin-left: 0 !important;\n    margin-right: 0 !important; }\n  .mb-md-0,\n  .my-md-0 {\n    margin-bottom: 0 !important; }\n  .ml-md-0,\n  .mx-md-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important; }\n  .m-md-1 {\n    margin: 0.375rem !important; }\n  .mt-md-1,\n  .my-md-1 {\n    margin-top: 0.375rem !important; }\n  .mr-md-1,\n  .mx-md-1 {\n    margin-left: 0.375rem !important;\n    margin-right: 0 !important; }\n  .mb-md-1,\n  .my-md-1 {\n    margin-bottom: 0.375rem !important; }\n  .ml-md-1,\n  .mx-md-1 {\n    margin-right: 0.375rem !important;\n    margin-left: 0 !important; }\n  .m-md-2 {\n    margin: 0.75rem !important; }\n  .mt-md-2,\n  .my-md-2 {\n    margin-top: 0.75rem !important; }\n  .mr-md-2,\n  .mx-md-2 {\n    margin-left: 0.75rem !important;\n    margin-right: 0 !important; }\n  .mb-md-2,\n  .my-md-2 {\n    margin-bottom: 0.75rem !important; }\n  .ml-md-2,\n  .mx-md-2 {\n    margin-right: 0.75rem !important;\n    margin-left: 0 !important; }\n  .m-md-3 {\n    margin: 1.5rem !important; }\n  .mt-md-3,\n  .my-md-3 {\n    margin-top: 1.5rem !important; }\n  .mr-md-3,\n  .mx-md-3 {\n    margin-left: 1.5rem !important;\n    margin-right: 0 !important; }\n  .mb-md-3,\n  .my-md-3 {\n    margin-bottom: 1.5rem !important; }\n  .ml-md-3,\n  .mx-md-3 {\n    margin-right: 1.5rem !important;\n    margin-left: 0 !important; }\n  .m-md-4 {\n    margin: 2.25rem !important; }\n  .mt-md-4,\n  .my-md-4 {\n    margin-top: 2.25rem !important; }\n  .mr-md-4,\n  .mx-md-4 {\n    margin-left: 2.25rem !important;\n    margin-right: 0 !important; }\n  .mb-md-4,\n  .my-md-4 {\n    margin-bottom: 2.25rem !important; }\n  .ml-md-4,\n  .mx-md-4 {\n    margin-right: 2.25rem !important;\n    margin-left: 0 !important; }\n  .m-md-5 {\n    margin: 4.5rem !important; }\n  .mt-md-5,\n  .my-md-5 {\n    margin-top: 4.5rem !important; }\n  .mr-md-5,\n  .mx-md-5 {\n    margin-left: 4.5rem !important;\n    margin-right: 0 !important; }\n  .mb-md-5,\n  .my-md-5 {\n    margin-bottom: 4.5rem !important; }\n  .ml-md-5,\n  .mx-md-5 {\n    margin-right: 4.5rem !important;\n    margin-left: 0 !important; }\n  .p-md-0 {\n    padding: 0 !important; }\n  .pt-md-0,\n  .py-md-0 {\n    padding-top: 0 !important; }\n  .pr-md-0,\n  .px-md-0 {\n    padding-left: 0 !important;\n    padding-right: 0 !important; }\n  .pb-md-0,\n  .py-md-0 {\n    padding-bottom: 0 !important; }\n  .pl-md-0,\n  .px-md-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important; }\n  .p-md-1 {\n    padding: 0.375rem !important; }\n  .pt-md-1,\n  .py-md-1 {\n    padding-top: 0.375rem !important; }\n  .pr-md-1,\n  .px-md-1 {\n    padding-left: 0.375rem !important;\n    padding-right: 0 !important; }\n  .pb-md-1,\n  .py-md-1 {\n    padding-bottom: 0.375rem !important; }\n  .pl-md-1,\n  .px-md-1 {\n    padding-right: 0.375rem !important;\n    padding-left: 0 !important; }\n  .p-md-2 {\n    padding: 0.75rem !important; }\n  .pt-md-2,\n  .py-md-2 {\n    padding-top: 0.75rem !important; }\n  .pr-md-2,\n  .px-md-2 {\n    padding-left: 0.75rem !important;\n    padding-right: 0 !important; }\n  .pb-md-2,\n  .py-md-2 {\n    padding-bottom: 0.75rem !important; }\n  .pl-md-2,\n  .px-md-2 {\n    padding-right: 0.75rem !important;\n    padding-left: 0 !important; }\n  .p-md-3 {\n    padding: 1.5rem !important; }\n  .pt-md-3,\n  .py-md-3 {\n    padding-top: 1.5rem !important; }\n  .pr-md-3,\n  .px-md-3 {\n    padding-left: 1.5rem !important;\n    padding-right: 0 !important; }\n  .pb-md-3,\n  .py-md-3 {\n    padding-bottom: 1.5rem !important; }\n  .pl-md-3,\n  .px-md-3 {\n    padding-right: 1.5rem !important;\n    padding-left: 0 !important; }\n  .p-md-4 {\n    padding: 2.25rem !important; }\n  .pt-md-4,\n  .py-md-4 {\n    padding-top: 2.25rem !important; }\n  .pr-md-4,\n  .px-md-4 {\n    padding-left: 2.25rem !important;\n    padding-right: 0 !important; }\n  .pb-md-4,\n  .py-md-4 {\n    padding-bottom: 2.25rem !important; }\n  .pl-md-4,\n  .px-md-4 {\n    padding-right: 2.25rem !important;\n    padding-left: 0 !important; }\n  .p-md-5 {\n    padding: 4.5rem !important; }\n  .pt-md-5,\n  .py-md-5 {\n    padding-top: 4.5rem !important; }\n  .pr-md-5,\n  .px-md-5 {\n    padding-left: 4.5rem !important;\n    padding-right: 0 !important; }\n  .pb-md-5,\n  .py-md-5 {\n    padding-bottom: 4.5rem !important; }\n  .pl-md-5,\n  .px-md-5 {\n    padding-right: 4.5rem !important;\n    padding-left: 0 !important; }\n  .m-md-n1 {\n    margin: -0.375rem !important; }\n  .mt-md-n1,\n  .my-md-n1 {\n    margin-top: -0.375rem !important; }\n  .mr-md-n1,\n  .mx-md-n1 {\n    margin-right: -0.375rem !important; }\n  .mb-md-n1,\n  .my-md-n1 {\n    margin-bottom: -0.375rem !important; }\n  .ml-md-n1,\n  .mx-md-n1 {\n    margin-left: -0.375rem !important; }\n  .m-md-n2 {\n    margin: -0.75rem !important; }\n  .mt-md-n2,\n  .my-md-n2 {\n    margin-top: -0.75rem !important; }\n  .mr-md-n2,\n  .mx-md-n2 {\n    margin-right: -0.75rem !important; }\n  .mb-md-n2,\n  .my-md-n2 {\n    margin-bottom: -0.75rem !important; }\n  .ml-md-n2,\n  .mx-md-n2 {\n    margin-left: -0.75rem !important; }\n  .m-md-n3 {\n    margin: -1.5rem !important; }\n  .mt-md-n3,\n  .my-md-n3 {\n    margin-top: -1.5rem !important; }\n  .mr-md-n3,\n  .mx-md-n3 {\n    margin-right: -1.5rem !important; }\n  .mb-md-n3,\n  .my-md-n3 {\n    margin-bottom: -1.5rem !important; }\n  .ml-md-n3,\n  .mx-md-n3 {\n    margin-left: -1.5rem !important; }\n  .m-md-n4 {\n    margin: -2.25rem !important; }\n  .mt-md-n4,\n  .my-md-n4 {\n    margin-top: -2.25rem !important; }\n  .mr-md-n4,\n  .mx-md-n4 {\n    margin-right: -2.25rem !important; }\n  .mb-md-n4,\n  .my-md-n4 {\n    margin-bottom: -2.25rem !important; }\n  .ml-md-n4,\n  .mx-md-n4 {\n    margin-left: -2.25rem !important; }\n  .m-md-n5 {\n    margin: -4.5rem !important; }\n  .mt-md-n5,\n  .my-md-n5 {\n    margin-top: -4.5rem !important; }\n  .mr-md-n5,\n  .mx-md-n5 {\n    margin-right: -4.5rem !important; }\n  .mb-md-n5,\n  .my-md-n5 {\n    margin-bottom: -4.5rem !important; }\n  .ml-md-n5,\n  .mx-md-n5 {\n    margin-left: -4.5rem !important; }\n  .m-md-auto {\n    margin: auto !important; }\n  .mt-md-auto,\n  .my-md-auto {\n    margin-top: auto !important; }\n  .mr-md-auto,\n  .mx-md-auto {\n    margin-left: auto !important;\n    margin-right: inherit !important; }\n  .mb-md-auto,\n  .my-md-auto {\n    margin-bottom: auto !important; }\n  .ml-md-auto,\n  .mx-md-auto {\n    margin-right: auto !important;\n    margin-left: auto !important; } }\n\n@media (min-width: 992px) {\n  .m-lg-0 {\n    margin: 0 !important; }\n  .mt-lg-0,\n  .my-lg-0 {\n    margin-top: 0 !important; }\n  .mr-lg-0,\n  .mx-lg-0 {\n    margin-left: 0 !important;\n    margin-right: 0 !important; }\n  .mb-lg-0,\n  .my-lg-0 {\n    margin-bottom: 0 !important; }\n  .ml-lg-0,\n  .mx-lg-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important; }\n  .m-lg-1 {\n    margin: 0.375rem !important; }\n  .mt-lg-1,\n  .my-lg-1 {\n    margin-top: 0.375rem !important; }\n  .mr-lg-1,\n  .mx-lg-1 {\n    margin-left: 0.375rem !important;\n    margin-right: 0 !important; }\n  .mb-lg-1,\n  .my-lg-1 {\n    margin-bottom: 0.375rem !important; }\n  .ml-lg-1,\n  .mx-lg-1 {\n    margin-right: 0.375rem !important;\n    margin-left: 0 !important; }\n  .m-lg-2 {\n    margin: 0.75rem !important; }\n  .mt-lg-2,\n  .my-lg-2 {\n    margin-top: 0.75rem !important; }\n  .mr-lg-2,\n  .mx-lg-2 {\n    margin-left: 0.75rem !important;\n    margin-right: 0 !important; }\n  .mb-lg-2,\n  .my-lg-2 {\n    margin-bottom: 0.75rem !important; }\n  .ml-lg-2,\n  .mx-lg-2 {\n    margin-right: 0.75rem !important;\n    margin-left: 0 !important; }\n  .m-lg-3 {\n    margin: 1.5rem !important; }\n  .mt-lg-3,\n  .my-lg-3 {\n    margin-top: 1.5rem !important; }\n  .mr-lg-3,\n  .mx-lg-3 {\n    margin-left: 1.5rem !important;\n    margin-right: 0 !important; }\n  .mb-lg-3,\n  .my-lg-3 {\n    margin-bottom: 1.5rem !important; }\n  .ml-lg-3,\n  .mx-lg-3 {\n    margin-right: 1.5rem !important;\n    margin-left: 0 !important; }\n  .m-lg-4 {\n    margin: 2.25rem !important; }\n  .mt-lg-4,\n  .my-lg-4 {\n    margin-top: 2.25rem !important; }\n  .mr-lg-4,\n  .mx-lg-4 {\n    margin-left: 2.25rem !important;\n    margin-right: 0 !important; }\n  .mb-lg-4,\n  .my-lg-4 {\n    margin-bottom: 2.25rem !important; }\n  .ml-lg-4,\n  .mx-lg-4 {\n    margin-right: 2.25rem !important;\n    margin-left: 0 !important; }\n  .m-lg-5 {\n    margin: 4.5rem !important; }\n  .mt-lg-5,\n  .my-lg-5 {\n    margin-top: 4.5rem !important; }\n  .mr-lg-5,\n  .mx-lg-5 {\n    margin-left: 4.5rem !important;\n    margin-right: 0 !important; }\n  .mb-lg-5,\n  .my-lg-5 {\n    margin-bottom: 4.5rem !important; }\n  .ml-lg-5,\n  .mx-lg-5 {\n    margin-right: 4.5rem !important;\n    margin-left: 0 !important; }\n  .p-lg-0 {\n    padding: 0 !important; }\n  .pt-lg-0,\n  .py-lg-0 {\n    padding-top: 0 !important; }\n  .pr-lg-0,\n  .px-lg-0 {\n    padding-left: 0 !important;\n    padding-right: 0 !important; }\n  .pb-lg-0,\n  .py-lg-0 {\n    padding-bottom: 0 !important; }\n  .pl-lg-0,\n  .px-lg-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important; }\n  .p-lg-1 {\n    padding: 0.375rem !important; }\n  .pt-lg-1,\n  .py-lg-1 {\n    padding-top: 0.375rem !important; }\n  .pr-lg-1,\n  .px-lg-1 {\n    padding-left: 0.375rem !important;\n    padding-right: 0 !important; }\n  .pb-lg-1,\n  .py-lg-1 {\n    padding-bottom: 0.375rem !important; }\n  .pl-lg-1,\n  .px-lg-1 {\n    padding-right: 0.375rem !important;\n    padding-left: 0 !important; }\n  .p-lg-2 {\n    padding: 0.75rem !important; }\n  .pt-lg-2,\n  .py-lg-2 {\n    padding-top: 0.75rem !important; }\n  .pr-lg-2,\n  .px-lg-2 {\n    padding-left: 0.75rem !important;\n    padding-right: 0 !important; }\n  .pb-lg-2,\n  .py-lg-2 {\n    padding-bottom: 0.75rem !important; }\n  .pl-lg-2,\n  .px-lg-2 {\n    padding-right: 0.75rem !important;\n    padding-left: 0 !important; }\n  .p-lg-3 {\n    padding: 1.5rem !important; }\n  .pt-lg-3,\n  .py-lg-3 {\n    padding-top: 1.5rem !important; }\n  .pr-lg-3,\n  .px-lg-3 {\n    padding-left: 1.5rem !important;\n    padding-right: 0 !important; }\n  .pb-lg-3,\n  .py-lg-3 {\n    padding-bottom: 1.5rem !important; }\n  .pl-lg-3,\n  .px-lg-3 {\n    padding-right: 1.5rem !important;\n    padding-left: 0 !important; }\n  .p-lg-4 {\n    padding: 2.25rem !important; }\n  .pt-lg-4,\n  .py-lg-4 {\n    padding-top: 2.25rem !important; }\n  .pr-lg-4,\n  .px-lg-4 {\n    padding-left: 2.25rem !important;\n    padding-right: 0 !important; }\n  .pb-lg-4,\n  .py-lg-4 {\n    padding-bottom: 2.25rem !important; }\n  .pl-lg-4,\n  .px-lg-4 {\n    padding-right: 2.25rem !important;\n    padding-left: 0 !important; }\n  .p-lg-5 {\n    padding: 4.5rem !important; }\n  .pt-lg-5,\n  .py-lg-5 {\n    padding-top: 4.5rem !important; }\n  .pr-lg-5,\n  .px-lg-5 {\n    padding-left: 4.5rem !important;\n    padding-right: 0 !important; }\n  .pb-lg-5,\n  .py-lg-5 {\n    padding-bottom: 4.5rem !important; }\n  .pl-lg-5,\n  .px-lg-5 {\n    padding-right: 4.5rem !important;\n    padding-left: 0 !important; }\n  .m-lg-n1 {\n    margin: -0.375rem !important; }\n  .mt-lg-n1,\n  .my-lg-n1 {\n    margin-top: -0.375rem !important; }\n  .mr-lg-n1,\n  .mx-lg-n1 {\n    margin-right: -0.375rem !important; }\n  .mb-lg-n1,\n  .my-lg-n1 {\n    margin-bottom: -0.375rem !important; }\n  .ml-lg-n1,\n  .mx-lg-n1 {\n    margin-left: -0.375rem !important; }\n  .m-lg-n2 {\n    margin: -0.75rem !important; }\n  .mt-lg-n2,\n  .my-lg-n2 {\n    margin-top: -0.75rem !important; }\n  .mr-lg-n2,\n  .mx-lg-n2 {\n    margin-right: -0.75rem !important; }\n  .mb-lg-n2,\n  .my-lg-n2 {\n    margin-bottom: -0.75rem !important; }\n  .ml-lg-n2,\n  .mx-lg-n2 {\n    margin-left: -0.75rem !important; }\n  .m-lg-n3 {\n    margin: -1.5rem !important; }\n  .mt-lg-n3,\n  .my-lg-n3 {\n    margin-top: -1.5rem !important; }\n  .mr-lg-n3,\n  .mx-lg-n3 {\n    margin-right: -1.5rem !important; }\n  .mb-lg-n3,\n  .my-lg-n3 {\n    margin-bottom: -1.5rem !important; }\n  .ml-lg-n3,\n  .mx-lg-n3 {\n    margin-left: -1.5rem !important; }\n  .m-lg-n4 {\n    margin: -2.25rem !important; }\n  .mt-lg-n4,\n  .my-lg-n4 {\n    margin-top: -2.25rem !important; }\n  .mr-lg-n4,\n  .mx-lg-n4 {\n    margin-right: -2.25rem !important; }\n  .mb-lg-n4,\n  .my-lg-n4 {\n    margin-bottom: -2.25rem !important; }\n  .ml-lg-n4,\n  .mx-lg-n4 {\n    margin-left: -2.25rem !important; }\n  .m-lg-n5 {\n    margin: -4.5rem !important; }\n  .mt-lg-n5,\n  .my-lg-n5 {\n    margin-top: -4.5rem !important; }\n  .mr-lg-n5,\n  .mx-lg-n5 {\n    margin-right: -4.5rem !important; }\n  .mb-lg-n5,\n  .my-lg-n5 {\n    margin-bottom: -4.5rem !important; }\n  .ml-lg-n5,\n  .mx-lg-n5 {\n    margin-left: -4.5rem !important; }\n  .m-lg-auto {\n    margin: auto !important; }\n  .mt-lg-auto,\n  .my-lg-auto {\n    margin-top: auto !important; }\n  .mr-lg-auto,\n  .mx-lg-auto {\n    margin-left: auto !important;\n    margin-right: inherit !important; }\n  .mb-lg-auto,\n  .my-lg-auto {\n    margin-bottom: auto !important; }\n  .ml-lg-auto,\n  .mx-lg-auto {\n    margin-right: auto !important;\n    margin-left: auto !important; } }\n\n@media (min-width: 1200px) {\n  .m-xl-0 {\n    margin: 0 !important; }\n  .mt-xl-0,\n  .my-xl-0 {\n    margin-top: 0 !important; }\n  .mr-xl-0,\n  .mx-xl-0 {\n    margin-left: 0 !important;\n    margin-right: 0 !important; }\n  .mb-xl-0,\n  .my-xl-0 {\n    margin-bottom: 0 !important; }\n  .ml-xl-0,\n  .mx-xl-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important; }\n  .m-xl-1 {\n    margin: 0.375rem !important; }\n  .mt-xl-1,\n  .my-xl-1 {\n    margin-top: 0.375rem !important; }\n  .mr-xl-1,\n  .mx-xl-1 {\n    margin-left: 0.375rem !important;\n    margin-right: 0 !important; }\n  .mb-xl-1,\n  .my-xl-1 {\n    margin-bottom: 0.375rem !important; }\n  .ml-xl-1,\n  .mx-xl-1 {\n    margin-right: 0.375rem !important;\n    margin-left: 0 !important; }\n  .m-xl-2 {\n    margin: 0.75rem !important; }\n  .mt-xl-2,\n  .my-xl-2 {\n    margin-top: 0.75rem !important; }\n  .mr-xl-2,\n  .mx-xl-2 {\n    margin-left: 0.75rem !important;\n    margin-right: 0 !important; }\n  .mb-xl-2,\n  .my-xl-2 {\n    margin-bottom: 0.75rem !important; }\n  .ml-xl-2,\n  .mx-xl-2 {\n    margin-right: 0.75rem !important;\n    margin-left: 0 !important; }\n  .m-xl-3 {\n    margin: 1.5rem !important; }\n  .mt-xl-3,\n  .my-xl-3 {\n    margin-top: 1.5rem !important; }\n  .mr-xl-3,\n  .mx-xl-3 {\n    margin-left: 1.5rem !important;\n    margin-right: 0 !important; }\n  .mb-xl-3,\n  .my-xl-3 {\n    margin-bottom: 1.5rem !important; }\n  .ml-xl-3,\n  .mx-xl-3 {\n    margin-right: 1.5rem !important;\n    margin-left: 0 !important; }\n  .m-xl-4 {\n    margin: 2.25rem !important; }\n  .mt-xl-4,\n  .my-xl-4 {\n    margin-top: 2.25rem !important; }\n  .mr-xl-4,\n  .mx-xl-4 {\n    margin-left: 2.25rem !important;\n    margin-right: 0 !important; }\n  .mb-xl-4,\n  .my-xl-4 {\n    margin-bottom: 2.25rem !important; }\n  .ml-xl-4,\n  .mx-xl-4 {\n    margin-right: 2.25rem !important;\n    margin-left: 0 !important; }\n  .m-xl-5 {\n    margin: 4.5rem !important; }\n  .mt-xl-5,\n  .my-xl-5 {\n    margin-top: 4.5rem !important; }\n  .mr-xl-5,\n  .mx-xl-5 {\n    margin-left: 4.5rem !important;\n    margin-right: 0 !important; }\n  .mb-xl-5,\n  .my-xl-5 {\n    margin-bottom: 4.5rem !important; }\n  .ml-xl-5,\n  .mx-xl-5 {\n    margin-right: 4.5rem !important;\n    margin-left: 0 !important; }\n  .p-xl-0 {\n    padding: 0 !important; }\n  .pt-xl-0,\n  .py-xl-0 {\n    padding-top: 0 !important; }\n  .pr-xl-0,\n  .px-xl-0 {\n    padding-left: 0 !important;\n    padding-right: 0 !important; }\n  .pb-xl-0,\n  .py-xl-0 {\n    padding-bottom: 0 !important; }\n  .pl-xl-0,\n  .px-xl-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important; }\n  .p-xl-1 {\n    padding: 0.375rem !important; }\n  .pt-xl-1,\n  .py-xl-1 {\n    padding-top: 0.375rem !important; }\n  .pr-xl-1,\n  .px-xl-1 {\n    padding-left: 0.375rem !important;\n    padding-right: 0 !important; }\n  .pb-xl-1,\n  .py-xl-1 {\n    padding-bottom: 0.375rem !important; }\n  .pl-xl-1,\n  .px-xl-1 {\n    padding-right: 0.375rem !important;\n    padding-left: 0 !important; }\n  .p-xl-2 {\n    padding: 0.75rem !important; }\n  .pt-xl-2,\n  .py-xl-2 {\n    padding-top: 0.75rem !important; }\n  .pr-xl-2,\n  .px-xl-2 {\n    padding-left: 0.75rem !important;\n    padding-right: 0 !important; }\n  .pb-xl-2,\n  .py-xl-2 {\n    padding-bottom: 0.75rem !important; }\n  .pl-xl-2,\n  .px-xl-2 {\n    padding-right: 0.75rem !important;\n    padding-left: 0 !important; }\n  .p-xl-3 {\n    padding: 1.5rem !important; }\n  .pt-xl-3,\n  .py-xl-3 {\n    padding-top: 1.5rem !important; }\n  .pr-xl-3,\n  .px-xl-3 {\n    padding-left: 1.5rem !important;\n    padding-right: 0 !important; }\n  .pb-xl-3,\n  .py-xl-3 {\n    padding-bottom: 1.5rem !important; }\n  .pl-xl-3,\n  .px-xl-3 {\n    padding-right: 1.5rem !important;\n    padding-left: 0 !important; }\n  .p-xl-4 {\n    padding: 2.25rem !important; }\n  .pt-xl-4,\n  .py-xl-4 {\n    padding-top: 2.25rem !important; }\n  .pr-xl-4,\n  .px-xl-4 {\n    padding-left: 2.25rem !important;\n    padding-right: 0 !important; }\n  .pb-xl-4,\n  .py-xl-4 {\n    padding-bottom: 2.25rem !important; }\n  .pl-xl-4,\n  .px-xl-4 {\n    padding-right: 2.25rem !important;\n    padding-left: 0 !important; }\n  .p-xl-5 {\n    padding: 4.5rem !important; }\n  .pt-xl-5,\n  .py-xl-5 {\n    padding-top: 4.5rem !important; }\n  .pr-xl-5,\n  .px-xl-5 {\n    padding-left: 4.5rem !important;\n    padding-right: 0 !important; }\n  .pb-xl-5,\n  .py-xl-5 {\n    padding-bottom: 4.5rem !important; }\n  .pl-xl-5,\n  .px-xl-5 {\n    padding-right: 4.5rem !important;\n    padding-left: 0 !important; }\n  .m-xl-n1 {\n    margin: -0.375rem !important; }\n  .mt-xl-n1,\n  .my-xl-n1 {\n    margin-top: -0.375rem !important; }\n  .mr-xl-n1,\n  .mx-xl-n1 {\n    margin-right: -0.375rem !important; }\n  .mb-xl-n1,\n  .my-xl-n1 {\n    margin-bottom: -0.375rem !important; }\n  .ml-xl-n1,\n  .mx-xl-n1 {\n    margin-left: -0.375rem !important; }\n  .m-xl-n2 {\n    margin: -0.75rem !important; }\n  .mt-xl-n2,\n  .my-xl-n2 {\n    margin-top: -0.75rem !important; }\n  .mr-xl-n2,\n  .mx-xl-n2 {\n    margin-right: -0.75rem !important; }\n  .mb-xl-n2,\n  .my-xl-n2 {\n    margin-bottom: -0.75rem !important; }\n  .ml-xl-n2,\n  .mx-xl-n2 {\n    margin-left: -0.75rem !important; }\n  .m-xl-n3 {\n    margin: -1.5rem !important; }\n  .mt-xl-n3,\n  .my-xl-n3 {\n    margin-top: -1.5rem !important; }\n  .mr-xl-n3,\n  .mx-xl-n3 {\n    margin-right: -1.5rem !important; }\n  .mb-xl-n3,\n  .my-xl-n3 {\n    margin-bottom: -1.5rem !important; }\n  .ml-xl-n3,\n  .mx-xl-n3 {\n    margin-left: -1.5rem !important; }\n  .m-xl-n4 {\n    margin: -2.25rem !important; }\n  .mt-xl-n4,\n  .my-xl-n4 {\n    margin-top: -2.25rem !important; }\n  .mr-xl-n4,\n  .mx-xl-n4 {\n    margin-right: -2.25rem !important; }\n  .mb-xl-n4,\n  .my-xl-n4 {\n    margin-bottom: -2.25rem !important; }\n  .ml-xl-n4,\n  .mx-xl-n4 {\n    margin-left: -2.25rem !important; }\n  .m-xl-n5 {\n    margin: -4.5rem !important; }\n  .mt-xl-n5,\n  .my-xl-n5 {\n    margin-top: -4.5rem !important; }\n  .mr-xl-n5,\n  .mx-xl-n5 {\n    margin-right: -4.5rem !important; }\n  .mb-xl-n5,\n  .my-xl-n5 {\n    margin-bottom: -4.5rem !important; }\n  .ml-xl-n5,\n  .mx-xl-n5 {\n    margin-left: -4.5rem !important; }\n  .m-xl-auto {\n    margin: auto !important; }\n  .mt-xl-auto,\n  .my-xl-auto {\n    margin-top: auto !important; }\n  .mr-xl-auto,\n  .mx-xl-auto {\n    margin-left: auto !important;\n    margin-right: inherit !important; }\n  .mb-xl-auto,\n  .my-xl-auto {\n    margin-bottom: auto !important; }\n  .ml-xl-auto,\n  .mx-xl-auto {\n    margin-right: auto !important;\n    margin-left: auto !important; } }\n\n.float-left {\n  float: right !important; }\n\n.float-right {\n  float: left !important; }\n\n.float-none {\n  float: none !important; }\n\n@media (min-width: 576px) {\n  .float-sm-left {\n    float: right !important; }\n  .float-sm-right {\n    float: left !important; }\n  .float-sm-none {\n    float: none !important; } }\n\n@media (min-width: 768px) {\n  .float-md-left {\n    float: right !important; }\n  .float-md-right {\n    float: left !important; }\n  .float-md-none {\n    float: none !important; } }\n\n@media (min-width: 992px) {\n  .float-lg-left {\n    float: right !important; }\n  .float-lg-right {\n    float: left !important; }\n  .float-lg-none {\n    float: none !important; } }\n\n@media (min-width: 1200px) {\n  .float-xl-left {\n    float: right !important; }\n  .float-xl-right {\n    float: left !important; }\n  .float-xl-none {\n    float: none !important; } }\n\n.text-monospace {\n  font-family: SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !important; }\n\n.text-justify {\n  text-align: justify !important; }\n\n.text-wrap {\n  white-space: normal !important; }\n\n.text-nowrap {\n  white-space: nowrap !important; }\n\n.text-truncate {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap; }\n\n.text-left {\n  text-align: right !important; }\n\n.text-right {\n  text-align: left !important; }\n\n.text-center {\n  text-align: center !important; }\n\n@media (min-width: 576px) {\n  .text-sm-left {\n    text-align: right !important; }\n  .text-sm-right {\n    text-align: left !important; }\n  .text-sm-center {\n    text-align: center !important; } }\n\n@media (min-width: 768px) {\n  .text-md-left {\n    text-align: right !important; }\n  .text-md-right {\n    text-align: left !important; }\n  .text-md-center {\n    text-align: center !important; } }\n\n@media (min-width: 992px) {\n  .text-lg-left {\n    text-align: right !important; }\n  .text-lg-right {\n    text-align: left !important; }\n  .text-lg-center {\n    text-align: center !important; } }\n\n@media (min-width: 1200px) {\n  .text-xl-left {\n    text-align: right !important; }\n  .text-xl-right {\n    text-align: left !important; }\n  .text-xl-center {\n    text-align: center !important; } }\n\n.text-lowercase {\n  text-transform: lowercase !important; }\n\n.text-uppercase {\n  text-transform: uppercase !important; }\n\n.text-capitalize {\n  text-transform: capitalize !important; }\n\n.font-weight-light {\n  font-weight: 300 !important; }\n\n.font-weight-lighter {\n  font-weight: lighter !important; }\n\n.font-weight-normal {\n  font-weight: 400 !important; }\n\n.font-weight-bold {\n  font-weight: 700 !important; }\n\n.font-weight-bolder {\n  font-weight: bolder !important; }\n\n.font-italic {\n  font-style: italic !important; }\n\n.text-white {\n  color: #fff !important; }\n\n.text-primary {\n  color: #348cd4 !important; }\n\na.text-primary:hover, a.text-primary:focus {\n  color: #21649b !important; }\n\n.text-secondary {\n  color: #6c757d !important; }\n\na.text-secondary:hover, a.text-secondary:focus {\n  color: #494f54 !important; }\n\n.text-success {\n  color: #3ec396 !important; }\n\na.text-success:hover, a.text-success:focus {\n  color: #2b8a6a !important; }\n\n.text-info {\n  color: #4fbde9 !important; }\n\na.text-info:hover, a.text-info:focus {\n  color: #1a9dd1 !important; }\n\n.text-warning {\n  color: #f9bc0b !important; }\n\na.text-warning:hover, a.text-warning:focus {\n  color: #b38604 !important; }\n\n.text-danger {\n  color: #f36270 !important; }\n\na.text-danger:hover, a.text-danger:focus {\n  color: #ee1b2f !important; }\n\n.text-light {\n  color: #f7f7f7 !important; }\n\na.text-light:hover, a.text-light:focus {\n  color: #d1d1d1 !important; }\n\n.text-dark {\n  color: #323a46 !important; }\n\na.text-dark:hover, a.text-dark:focus {\n  color: #121519 !important; }\n\n.text-pink {\n  color: #e061c9 !important; }\n\na.text-pink:hover, a.text-pink:focus {\n  color: #cc28af !important; }\n\n.text-purple {\n  color: #9368f3 !important; }\n\na.text-purple:hover, a.text-purple:focus {\n  color: #6021ed !important; }\n\n.text-body {\n  color: #6c757d !important; }\n\n.text-muted {\n  color: #98a6ad !important; }\n\n.text-black-50 {\n  color: rgba(0, 0, 0, 0.5) !important; }\n\n.text-white-50 {\n  color: rgba(255, 255, 255, 0.5) !important; }\n\n.text-hide {\n  font: 0/0 a;\n  color: transparent;\n  text-shadow: none;\n  background-color: transparent;\n  border: 0; }\n\n.text-decoration-none {\n  text-decoration: none !important; }\n\n.text-break {\n  word-break: break-word !important;\n  overflow-wrap: break-word !important; }\n\n.text-reset {\n  color: inherit !important; }\n\n.logo-box {\n  float: right; }\n\n.navbar-custom {\n  padding: 0 0 0 10px; }\n  .navbar-custom .app-search {\n    margin-left: 20px;\n    margin-right: 0px; }\n    .navbar-custom .app-search .input-group-append {\n      margin-right: 0; }\n    .navbar-custom .app-search .btn {\n      border-radius: 30px 0 0 30px !important; }\n    .navbar-custom .app-search .form-control {\n      padding-right: 20px;\n      padding-left: 0;\n      border-radius: 0 30px 30px 0 !important; }\n  .navbar-custom .topnav-menu > li {\n    float: right; }\n  .navbar-custom .topnav-menu .nav-link {\n    direction: ltr; }\n\n/* Notification */\n.notification-list .noti-icon-badge {\n  left: 12px;\n  right: auto; }\n\n.notification-list .notify-item {\n  padding: 12px 20px; }\n  .notification-list .notify-item .notify-icon {\n    float: right;\n    margin-left: 10px;\n    margin-right: 0; }\n  .notification-list .notify-item .notify-details,\n  .notification-list .notify-item .user-msg {\n    margin-left: 0;\n    margin-right: 45px; }\n\n.notification-list .pro-user-name {\n  margin-left: 0.375rem !important;\n  margin-right: 0 !important; }\n\n.notification-list .profile-dropdown i {\n  margin-left: 5px;\n  margin-right: 0px;\n  float: right; }\n\n.notification-list .profile-dropdown .notify-item {\n  padding: 7px 20px; }\n\n.page-title-box .page-title-right {\n  float: left; }\n\n.content-page {\n  margin-right: 240px;\n  margin-left: 0; }\n\n#sidebar-menu > ul > li > a i {\n  margin: 0 3px 0 10px; }\n\n#sidebar-menu > ul > li > a .drop-arrow {\n  float: left; }\n  #sidebar-menu > ul > li > a .drop-arrow i {\n    margin-left: 0; }\n\n#sidebar-menu > ul > li > ul {\n  padding-right: 40px;\n  padding-left: 0; }\n  #sidebar-menu > ul > li > ul ul {\n    padding-right: 20px;\n    padding-left: 0; }\n\n#sidebar-menu .menu-arrow {\n  left: 20px;\n  right: auto; }\n  #sidebar-menu .menu-arrow:before {\n    content: \"\\F141\"; }\n\n#sidebar-menu li.mm-active > a > span.menu-arrow {\n  -webkit-transform: rotate(-90deg);\n          transform: rotate(-90deg); }\n\n.enlarged .left-side-menu #sidebar-menu > ul > li > a i {\n  margin-left: 20px;\n  margin-right: 5px; }\n\n.enlarged .left-side-menu #sidebar-menu > ul > li > a span {\n  padding-right: 25px;\n  padding-left: 0; }\n\n.enlarged .left-side-menu #sidebar-menu > ul > li:hover > ul {\n  right: 70px;\n  left: auto; }\n\n.enlarged .left-side-menu #sidebar-menu > ul ul li:hover > ul {\n  right: 190px;\n  margin-top: -36px; }\n\n.enlarged .left-side-menu #sidebar-menu > ul ul li > a span.pull-right {\n  left: 20px;\n  right: 0; }\n\n.enlarged .navbar-custom {\n  right: 0px; }\n\n.enlarged .content-page {\n  margin-right: 70px !important;\n  margin-left: 0 !important; }\n\n.enlarged .footer {\n  left: 0 !important;\n  right: 70px !important; }\n\n@media (max-width: 767.98px) {\n  .content-page,\n  .enlarged .content-page {\n    margin-right: 0 !important; } }\n\n.footer {\n  left: 0;\n  right: 240px; }\n\n.footer-alt {\n  right: 0 !important; }\n\n@media (max-width: 767.98px) {\n  .footer {\n    right: 0 !important; } }\n\n.right-bar {\n  float: left !important;\n  left: -270px;\n  right: auto; }\n  .right-bar .user-box .user-img .user-edit {\n    right: 0;\n    left: -5px; }\n\n.right-bar-enabled .right-bar {\n  left: 0;\n  right: auto; }\n\n.legendLabel {\n  padding-right: 5px !important;\n  padding-left: 20px; }\n\n.select2-container .select2-selection--single .select2-selection__rendered {\n  padding-right: 12px; }\n\n.select2-container .select2-selection--single .select2-selection__arrow {\n  left: 3px;\n  right: auto; }\n\n.select2-container .select2-selection--multiple .select2-selection__choice {\n  float: right;\n  margin-left: 5px;\n  margin-right: 0; }\n\n.select2-container .select2-search--inline {\n  float: right; }\n\n.bootstrap-select .dropdown-toggle:before {\n  float: left; }\n\n.bootstrap-select .dropdown-toggle .filter-option {\n  text-align: right; }\n\n.bootstrap-select .dropdown-toggle .filter-option-inner {\n  padding-right: 0;\n  padding-left: inherit; }\n\n.wizard > .steps .number {\n  margin-left: 10px; }\n\n.editable-buttons {\n  margin-left: 0;\n  margin-right: 7px; }\n  .editable-buttons .editable-cancel {\n    margin-left: 0;\n    margin-right: 7px; }\n\n.dataTables_wrapper .dataTables_filter {\n  text-align: left !important; }\n  .dataTables_wrapper .dataTables_filter input {\n    margin-left: 0px !important;\n    margin-right: 0.5em; }\n\n.tablesaw-columntoggle-popup .tablesaw-btn-group > label input {\n  margin-right: 0;\n  margin-left: .8em; }\n\n.tablesaw-bar .tablesaw-bar-section .tablesaw-btn {\n  margin-left: 0;\n  margin-right: .4em; }\n\n.table-rep-plugin .btn-group.pull-right {\n  float: left; }\n\n.table-rep-plugin .checkbox-row label:after {\n  margin-left: -22px;\n  top: -2px; }\n\n.ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg {\n  left: 0;\n  right: auto; }\n\n.ql-editor {\n  direction: rtl;\n  text-align: right; }\n\n.inbox-widget .inbox-item .inbox-item-img {\n  float: right;\n  margin-left: 15px;\n  margin-right: 0; }\n\n.inbox-widget .inbox-item .inbox-item-date {\n  right: auto;\n  left: 5px; }\n\n.comment-list .comment-box-item .commnet-item-date {\n  left: 7px;\n  right: auto; }\n\n.transaction-list .tran-text {\n  padding-right: 25px; }\n\n.transaction-list .tran-price {\n  margin-right: 30px;\n  margin-left: 0px; }\n\n.icons-list-demo i {\n  margin-left: 12px;\n  margin-right: 0; }\n\n.checkbox label {\n  padding-right: 8px;\n  padding-left: 0; }\n  .checkbox label::before {\n    left: auto;\n    right: 0;\n    margin-left: 0;\n    margin-right: -18px; }\n  .checkbox label::after {\n    left: auto;\n    right: 0;\n    margin-right: -18px;\n    margin-left: 0;\n    padding-left: 0;\n    padding-right: 3px; }\n\n.checkbox input[type=\"checkbox\"]:checked + label::after {\n  left: auto;\n  right: 7px;\n  -webkit-transform: rotate(45deg);\n          transform: rotate(45deg); }\n\n.checkbox.checkbox-single label:before {\n  margin-right: 0; }\n\n.checkbox.checkbox-single label:after {\n  margin-right: 0; }\n\n.radio label {\n  padding-left: 0;\n  padding-right: 8px; }\n  .radio label::before {\n    left: auto;\n    right: 0;\n    margin-left: 0;\n    margin-right: -18px; }\n  .radio label::after {\n    left: 0;\n    right: 6px;\n    margin-left: 0;\n    margin-right: -20px; }\n\n@media print {\n  .content-page,\n  .content,\n  body {\n    margin-right: 0; } }\n\n.inbox-leftbar {\n  float: right;\n  padding: 0 10px 20px 20px; }\n\n.inbox-rightbar {\n  margin-right: 250px;\n  margin-left: 0px; }\n\n.message-list {\n  padding-right: 0; }\n  .message-list .col-mail {\n    float: right !important; }\n  .message-list .col-mail-1 .star-toggle,\n  .message-list .col-mail-1 .checkbox-wrapper-mail,\n  .message-list .col-mail-1 .dot {\n    float: right !important; }\n  .message-list .col-mail-1 .dot {\n    margin: 22px 0px 0 26px; }\n  .message-list .col-mail-1 .checkbox-wrapper-mail {\n    margin: 15px 20px 0 10px !important; }\n  .message-list .col-mail-1 .star-toggle {\n    margin-right: 5px; }\n  .message-list .col-mail-1 .title {\n    right: 110px !important;\n    left: 0 !important; }\n  .message-list .col-mail-2 {\n    right: 320px !important;\n    left: 0 !important; }\n    .message-list .col-mail-2 .subject {\n      right: 0 !important;\n      left: 200px !important; }\n    .message-list .col-mail-2 .date {\n      left: 0;\n      padding-right: 80px;\n      padding-left: 0px !important;\n      right: auto !important; }\n\n@media (max-width: 648px) {\n  .inbox-rightbar {\n    margin-right: 0; } }\n\n@media (max-width: 520px) {\n  .message-list li .col-mail-1 .title {\n    right: 80px !important;\n    left: 0px !important; }\n  .message-list li .col-mail-2 {\n    right: 160px !important; }\n    .message-list li .col-mail-2 .date {\n      text-align: left;\n      padding-left: 10px !important;\n      padding-right: 20px; } }\n\n/* =============\r\n   Timeline\r\n============= */\n.timeline .time-show {\n  margin-left: -75px; }\n\n.timeline:before {\n  right: 50%; }\n\n.timeline .timeline-icon {\n  right: -54px; }\n  .timeline .timeline-icon i {\n    right: 4px; }\n\nh3.timeline-title {\n  margin: 0 0 5px; }\n\n.timeline-item .timeline-desk .arrow {\n  border-left: 12px solid rgba(247, 247, 247, 0.3) !important;\n  right: -12px; }\n\n.timeline-item.alt .timeline-desk .arrow-alt {\n  border-right: 12px solid rgba(247, 247, 247, 0.9) !important;\n  right: auto;\n  left: -12px;\n  border-left: none !important; }\n\n.timeline-item.alt .timeline-desk .album {\n  float: left; }\n  .timeline-item.alt .timeline-desk .album a {\n    float: left;\n    margin-right: 5px; }\n\n.timeline-item.alt .timeline-icon {\n  right: auto;\n  left: -56px; }\n\n.timeline-item.alt .panel {\n  margin-right: 0;\n  margin-left: 45px; }\n\n.timeline-item.alt h4 {\n  text-align: left; }\n\n.timeline-item.alt p {\n  text-align: left; }\n\n.timeline-item.alt .timeline-date {\n  text-align: left; }\n\n.timeline-desk .panel {\n  margin-right: 45px;\n  text-align: right;\n  margin-left: 0px; }\n\n.timeline-desk .album a {\n  float: right;\n  margin-left: 5px; }\n\n.home-btn {\n  position: absolute;\n  left: 25px;\n  right: auto; }\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n != null and $n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width. Null for the largest (last) breakpoint.\n// The maximum value is calculated as the minimum of the next one less 0.02px\n// to work around the limitations of `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $next: breakpoint-next($name, $breakpoints);\n  @return if($next, breakpoint-min($next, $breakpoints) - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  $max: breakpoint-max($name, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($name, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "// \r\n// topbar.scss\r\n//\r\n\r\n// Logo\r\n.logo {\r\n    display: block;\r\n    line-height: $topbar-height;\r\n    span.logo-lg {\r\n        display: block;\r\n    }\r\n    span.logo-sm {\r\n        display: none;\r\n    }\r\n    .logo-lg-text-dark {\r\n        color: $dark;\r\n        font-weight: $font-weight-bold;\r\n        font-size: 22px;\r\n        text-transform: uppercase;\r\n    }\r\n    .logo-lg-text-light {\r\n        color: $white;\r\n        font-weight: $font-weight-bold;\r\n        font-size: 22px;\r\n        text-transform: uppercase;\r\n    }\r\n}\r\n\r\n.logo-box {\r\n    height: $topbar-height;\r\n    width: $leftbar-width;\r\n    float: left;\r\n}\r\n\r\n.navbar-custom {\r\n    background: $bg-topbar-gradient;\r\n    box-shadow: $box-shadow;\r\n    padding: 0 10px 0 0;\r\n    position: fixed;\r\n    left: 0;\r\n    right: 0;\r\n    height: $topbar-height;\r\n    z-index: 100;\r\n\r\n    .topnav-menu {\r\n        > li {\r\n            float: left;\r\n        }\r\n        .nav-link {\r\n            padding: 0 15px;\r\n            color: rgba($white,0.6);\r\n            min-width: 32px;\r\n            display: block;\r\n            line-height: $topbar-height;\r\n            text-align: center;\r\n            max-height: $topbar-height;\r\n        }\r\n\r\n    }\r\n\r\n    .dropdown.show {\r\n        .nav-link {\r\n            background-color: rgba($white,0.05);\r\n        }   \r\n    }\r\n\r\n\r\n    /* Search */\r\n    .app-search {\r\n        overflow: hidden;\r\n        height: $topbar-height;\r\n        display: table;\r\n        max-width: 200px;\r\n        margin-right: 20px;\r\n        \r\n        .app-search-box {\r\n            display: table-cell;\r\n            vertical-align: middle;\r\n\r\n            input::-webkit-input-placeholder {\r\n                font-size: 0.8125rem;\r\n                color: rgba($white,0.7);\r\n            }\r\n        }\r\n        .form-control {\r\n            border: none;\r\n            height: 38px;\r\n            padding-left: 20px;\r\n            padding-right: 0;\r\n            color: $white;\r\n            background-color: rgba($white,0.07);\r\n            box-shadow: none;\r\n            border-radius: 30px 0 0 30px;\r\n        }\r\n        .input-group-append {\r\n            margin-left: 0;\r\n            z-index: 4;\r\n        }\r\n\r\n        .btn {\r\n            background-color: rgba($white,0.07);\r\n            color: $white;\r\n            border-color: transparent;;\r\n            border-radius: 0 30px 30px 0;\r\n            box-shadow: none !important;\r\n        }\r\n    }\r\n\r\n    .button-menu-mobile {\r\n        border: none;\r\n        color: $white;\r\n        display: inline-block;\r\n        height: $topbar-height;\r\n        line-height: $topbar-height;\r\n        width: 60px;\r\n        background-color: transparent;\r\n        font-size: 24px;\r\n        cursor: pointer;\r\n    }\r\n    \r\n    .button-menu-mobile.disable-btn {\r\n        display: none;\r\n    }\r\n}\r\n\r\n\r\n/* Notification */\r\n.noti-scroll {\r\n    max-height: 230px;\r\n}\r\n\r\n.notification-list {\r\n    margin-left: 0;\r\n\r\n    .noti-title {\r\n        background-color: $white;\r\n        padding: 15px 20px;\r\n    }\r\n\r\n    .noti-icon {\r\n        font-size: 21px;\r\n        vertical-align: middle;\r\n    }\r\n\r\n    .noti-icon-badge {\r\n        display: inline-block;\r\n        position: absolute;\r\n        top: 16px;\r\n        right: 10px;\r\n    }\r\n\r\n    .notify-item {\r\n        padding: 12px 20px;\r\n\r\n        .notify-icon {\r\n            float: left;\r\n            height: 36px;\r\n            width: 36px;\r\n            font-size: 18px;\r\n            line-height: 36px;\r\n            text-align: center;\r\n            margin-top: 4px;\r\n            margin-right: 10px;\r\n            border-radius: 50%;\r\n            color: $white;\r\n        }\r\n\r\n        .notify-details {\r\n            margin-bottom: 5px;\r\n            overflow: hidden;\r\n            margin-left: 45px;\r\n            text-overflow: ellipsis;\r\n            white-space: nowrap;\r\n            color: $gray-800;\r\n            font-weight: $font-weight-medium;\r\n\r\n            b {\r\n                font-weight: 500;\r\n            }\r\n            small {\r\n                display: block;\r\n            }\r\n            span {\r\n                display: block;\r\n                overflow: hidden;\r\n                text-overflow: ellipsis;\r\n                white-space: nowrap;\r\n                font-size: 13px;\r\n            }\r\n        }\r\n        \r\n        .user-msg {\r\n            margin-left: 45px;\r\n            white-space: normal;\r\n            line-height: 16px;\r\n        }\r\n    }\r\n    .profile-dropdown {\r\n        .notify-item {\r\n            padding: 7px 20px;\r\n        }\r\n    }\r\n}\r\n\r\n.profile-dropdown {\r\n    width: 170px;\r\n    i {\r\n        vertical-align: middle;\r\n        margin-right: 5px;\r\n    }\r\n}\r\n\r\n.nav-user {\r\n    padding: 0 12px !important;\r\n    img {\r\n        height: 32px;\r\n        width: 32px;\r\n    }\r\n}\r\n\r\n", "// \r\n// page-title.scss\r\n//\r\n\r\n.page-title-box {\r\n    padding: 0px 20px;\r\n    margin: 0 -27px 30px -27px;\r\n    background-color: $white;\r\n    box-shadow: $box-shadow;\r\n    .page-title {\r\n        font-size: 18px;\r\n        margin: 0;\r\n        line-height: 50px;\r\n        font-weight: $font-weight-bold;\r\n    }\r\n\r\n    .page-title-right {\r\n        float: right;\r\n    }\r\n\r\n    .breadcrumb {\r\n        margin-bottom: 0;\r\n        padding: 14px 0;\r\n\r\n    }\r\n}\r\n\r\n\r\n@include media-breakpoint-down(sm) {\r\n    .page-title-box {\r\n        .page-title {\r\n            display: block;\r\n            white-space: nowrap;\r\n            text-overflow: ellipsis;\r\n            overflow: hidden;\r\n        }\r\n        .breadcrumb {\r\n            display: none;\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 640px) {\r\n    .page-title-box {\r\n        .page-title-right {\r\n            display: none;\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 419px) {\r\n    .page-title-box .breadcrumb, .nav-user span {\r\n        display: none;\r\n    }\r\n\r\n}", "// \r\n// footer.scss\r\n//\r\n\r\n.footer {\r\n    bottom: 0;\r\n    padding: 19px 15px 20px;\r\n    position: absolute;\r\n    right: 0;\r\n    color: $gray-600;\r\n    left: $leftbar-width;\r\n    border-top: 1px solid $gray-300;\r\n    text-align: center;\r\n}\r\n\r\n@include media-breakpoint-down(sm) {\r\n    .footer {\r\n        left: 0 !important;\r\n        text-align: center;\r\n    }\r\n}", "//\r\n// right-sidebar.scss\r\n//\r\n\r\n.right-bar {\r\n    background-color: $white;\r\n    box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);\r\n    display: block;\r\n    position: fixed;\r\n    transition: all 200ms ease-out;\r\n    width: $rightbar-width;\r\n    z-index: 9999;\r\n    float: right !important;\r\n    right: -($rightbar-width + 10px);\r\n    top: 0;\r\n    bottom: 0;\r\n\r\n    .rightbar-title {\r\n        background: $bg-topbar-gradient;\r\n        padding: 27px 25px;\r\n        color: $white;\r\n    }\r\n    .right-bar-toggle {\r\n        background-color: lighten($dark, 7%);\r\n        height: 24px;\r\n        width: 24px;\r\n        line-height: 24px;\r\n        color: $white;\r\n        text-align: center;\r\n        border-radius: 50%;\r\n        margin-top: -4px;\r\n\r\n        &:hover {\r\n            background-color: lighten($dark, 10%);\r\n        }\r\n    }\r\n    .user-box {\r\n        padding: 25px;\r\n        text-align: center;\r\n        .user-img {\r\n            position: relative;\r\n            height: 64px;\r\n            width: 64px;\r\n            margin: 0 auto 15px auto;\r\n            .user-edit {\r\n                position: absolute;\r\n                right: -5px;\r\n                bottom: 0px;\r\n                height: 24px;\r\n                width: 24px;\r\n                background-color: $white;\r\n                line-height: 24px;\r\n                border-radius: 50%;\r\n                box-shadow: $box-shadow-lg;\r\n            }\r\n        }\r\n        h5 {\r\n            margin-bottom: 2px;\r\n            a {\r\n                color: $dark;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// Rightbar overlay\r\n.rightbar-overlay {\r\n    background-color: rgba($dark, 0.55);\r\n    position: absolute;\r\n    left: 0;\r\n    right: 0;\r\n    top: 0;\r\n    bottom: 0;\r\n    display: none;\r\n    z-index: 9998;\r\n    transition: all .2s ease-out;\r\n}\r\n\r\n.right-bar-enabled {\r\n    .right-bar {\r\n        right: 0;\r\n    }\r\n    .rightbar-overlay {\r\n        display: block;\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(sm) {\r\n    .right-bar {\r\n        overflow: auto;\r\n        .slimscroll-menu {\r\n            height: auto !important;\r\n        }\r\n    }\r\n}\r\n\r\n.activity-widget{\r\n    .activity-list{\r\n        position: relative;\r\n        border-left: 2px dashed $gray-400;\r\n        padding-left: 24px;\r\n        padding-bottom: 2px;\r\n        &::after{\r\n            content: \"\";\r\n            position: absolute;\r\n            left: -7px;\r\n            top: 6px;\r\n            width: 12px;\r\n            height: 12px;\r\n            background-color: $white;\r\n            border: 2px solid $primary;\r\n            border-radius: 50%;\r\n        }\r\n    }\r\n}\r\n\r\n// Inbox-widget(Used Profile)\r\n.inbox-widget {\r\n    .inbox-item {\r\n        overflow: hidden;\r\n        padding: 0.625rem 0;\r\n        position: relative;\r\n\r\n        .inbox-item-img {\r\n            display: block;\r\n            float: left;\r\n            margin-right: 15px;\r\n            margin-top: 4px;\r\n\r\n            img {\r\n                width: 40px;\r\n            }\r\n        }\r\n\r\n        .inbox-item-author {\r\n            display: block;\r\n            margin-bottom: 0px;\r\n            font-weight: $font-weight-semibold;\r\n            a{\r\n                color: $gray-700;\r\n            }\r\n        }\r\n\r\n        .inbox-item-text {\r\n            color: $gray-600;\r\n            display: block;\r\n            margin: 0;\r\n            overflow: hidden;\r\n        }\r\n\r\n        .inbox-item-date {\r\n            color: $gray-600;\r\n            font-size: 0.6875rem;\r\n            position: absolute;\r\n            right: 5px;\r\n            top: 10px;\r\n        }\r\n    }\r\n}\r\n", "//\n// helper.scss\n//\n\n// Minimum width\n\n.width-xs {\n  min-width: 80px;\n}\n\n.width-sm {\n  min-width: 95px;\n}\n\n.width-md {\n  min-width: 110px;\n}\n\n.width-lg {\n  min-width: 140px;\n}\n\n.width-xl {\n  min-width: 160px;\n}\n\n\n// Font Family\n.font-family-secondary {\n  font-family: $font-family-secondary;\n}\n\n// avatar height\n.avatar-xs {\n  height: 1.5rem;\n  width: 1.5rem;\n}\n\n.avatar-sm {\n  height: 2.25rem;\n  width: 2.25rem;\n}\n\n.avatar-md {\n  height: 3.5rem;\n  width: 3.5rem;\n}\n\n.avatar-lg {\n  height: 4.5rem;\n  width: 4.5rem;\n}\n\n.avatar-xl {\n  height: 6rem;\n  width: 6rem;\n}\n\n.avatar-xxl {\n  height: 7.5rem;\n  width: 7.5rem;\n}\n\n.avatar-title {\n  align-items: center;\n  color: $white;\n  display: flex;\n  height: 100%;\n  justify-content: center;\n  width: 100%;\n}\n\n.avatar-group {\n  padding-left: 12px;\n  .avatar-group-item {\n    margin: 0 0 10px -12px;\n    display: inline-block;\n    border: 2px solid $white;\n    border-radius: 50%;\n  }\n}\n\n\n// Font weight help class\n\n.font-weight-medium {\n  font-weight: 500;\n}\n\n.font-weight-semibold {\n  font-weight: 600;\n}\n\n\n// Text specify lines (Only chrome browser support)\n\n.sp-line-1,\n.sp-line-2,\n.sp-line-3,\n.sp-line-4 {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n}\n\n.sp-line-1 {\n  -webkit-line-clamp: 1;\n}\n\n.sp-line-2 {\n  -webkit-line-clamp: 2;\n}\n\n\n.sp-line-3 {\n  -webkit-line-clamp: 3;\n}\n\n.sp-line-4 {\n  -webkit-line-clamp: 4;\n}\n\n// pull in\n\n.pull-in {\n  margin-left: -$card-spacer-x;\n  margin-right: -$card-spacer-x;\n}", "\r\n// \r\n// social.scss\r\n//\r\n\r\n.social-list-item {\r\n    height: 2rem;\r\n    width: 2rem;\r\n    line-height: calc(2rem - 4px);\r\n    display: block;\r\n    border: 2px solid $gray-500;\r\n    border-radius: 50%;\r\n    color: $gray-500;\r\n}  ", "// \r\n// Custom-checkbox.scss\r\n//\r\n\r\n\r\n.checkbox {\r\n    label {\r\n        display: inline-block;\r\n        padding-left: 8px;\r\n        position: relative;\r\n        font-weight: normal;\r\n        &::before {\r\n            -o-transition: 0.3s ease-in-out;\r\n            -webkit-transition: 0.3s ease-in-out;\r\n            background-color: $white;\r\n            border-radius: 3px;\r\n            border: 2px solid $gray-600;\r\n            content: \"\";\r\n            display: inline-block;\r\n            height: 18px;\r\n            left: 0;\r\n            margin-left: -18px;\r\n            position: absolute;\r\n            transition: 0.3s ease-in-out;\r\n            width: 18px;\r\n            outline: none !important;\r\n            top: 2px;\r\n        }\r\n        &::after {\r\n            color: $gray-700;\r\n            display: inline-block;\r\n            font-size: 11px;\r\n            height: 18px;\r\n            left: 0;\r\n            margin-left: -18px;\r\n            padding-left: 3px;\r\n            padding-top: 2px;\r\n            position: absolute;\r\n            top: 0;\r\n            width: 18px;\r\n        }\r\n    }\r\n    input[type=\"checkbox\"] {\r\n        cursor: pointer;\r\n        opacity: 0;\r\n        z-index: 1;\r\n        outline: none !important;\r\n        &:disabled+label {\r\n            opacity: 0.65;\r\n        }\r\n    }\r\n    input[type=\"checkbox\"]:focus+label {\r\n        &::before {\r\n            outline-offset: -2px;\r\n            outline: none;\r\n        }\r\n    }\r\n    input[type=\"checkbox\"]:checked+label {\r\n        &::after {\r\n            content: \"\";\r\n            position: absolute;\r\n            top: 6px;\r\n            left: 7px;\r\n            display: table;\r\n            width: 4px;\r\n            height: 8px;\r\n            border: 2px solid $gray-700;\r\n            border-top-width: 0;\r\n            border-left-width: 0;\r\n            -webkit-transform: rotate(45deg);\r\n            -ms-transform: rotate(45deg);\r\n            -o-transform: rotate(45deg);\r\n            transform: rotate(45deg);\r\n        }\r\n    }\r\n    input[type=\"checkbox\"]:disabled+label {\r\n        &::before {\r\n            background-color: $light;\r\n            cursor: not-allowed;\r\n        }\r\n    }\r\n}\r\n\r\n.checkbox.checkbox-circle {\r\n    label {\r\n        &::before {\r\n            border-radius: 50%;\r\n        }\r\n    }\r\n}\r\n\r\n.checkbox.checkbox-inline {\r\n    margin-top: 0;\r\n}\r\n\r\n.checkbox.checkbox-single {\r\n    input {\r\n        height: 18px;\r\n        width: 18px;\r\n        position: absolute;\r\n    }\r\n    label {\r\n        height: 18px;\r\n        width: 18px;\r\n        &:before {\r\n            margin-left: 0;\r\n        }\r\n        &:after {\r\n            margin-left: 0;\r\n        }\r\n    }\r\n}\r\n\r\n@each $color,\r\n$value in $theme-colors {\r\n    .checkbox-#{$color} {\r\n        input[type=\"checkbox\"]:checked+label {\r\n            &::before {\r\n                background-color: $value;\r\n                border-color: $value;\r\n            }\r\n            &::after {\r\n                border-color: $white;\r\n            }\r\n        }\r\n    }\r\n}", "// \r\n// custom-radio.scss\r\n//\r\n\r\n.radio {\r\n    label {\r\n        display: inline-block;\r\n        padding-left: 8px;\r\n        position: relative;\r\n        font-weight: normal;\r\n        &::before {\r\n            -o-transition: border 0.5s ease-in-out;\r\n            -webkit-transition: border 0.5s ease-in-out;\r\n            background-color: $white;\r\n            border-radius: 50%;\r\n            border: 2px solid $gray-600;\r\n            content: \"\";\r\n            display: inline-block;\r\n            height: 18px;\r\n            left: 0;\r\n            margin-left: -18px;\r\n            position: absolute;\r\n            transition: border 0.5s ease-in-out;\r\n            width: 18px;\r\n            outline: none !important;\r\n        }\r\n        &::after {\r\n            -moz-transition: -moz-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);\r\n            -ms-transform: scale(0, 0);\r\n            -o-transform: scale(0, 0);\r\n            -o-transition: -o-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);\r\n            -webkit-transform: scale(0, 0);\r\n            -webkit-transition: -webkit-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);\r\n            background-color: $gray-700;\r\n            border-radius: 50%;\r\n            content: \" \";\r\n            display: inline-block;\r\n            height: 10px;\r\n            left: 6px;\r\n            margin-left: -20px;\r\n            position: absolute;\r\n            top: 4px;\r\n            transform: scale(0, 0);\r\n            transition: transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);\r\n            width: 10px;\r\n        }\r\n    }\r\n    input[type=\"radio\"] {\r\n        cursor: pointer;\r\n        opacity: 0;\r\n        z-index: 1;\r\n        outline: none !important;\r\n        &:disabled+label {\r\n            opacity: 0.65;\r\n        }\r\n    }\r\n    input[type=\"radio\"]:focus+label {\r\n        &::before {\r\n            outline-offset: -2px;\r\n            outline: 5px auto -webkit-focus-ring-color;\r\n            outline: thin dotted;\r\n        }\r\n    }\r\n    input[type=\"radio\"]:checked+label {\r\n        &::after {\r\n            -ms-transform: scale(1, 1);\r\n            -o-transform: scale(1, 1);\r\n            -webkit-transform: scale(1, 1);\r\n            transform: scale(1, 1);\r\n        }\r\n    }\r\n    input[type=\"radio\"]:disabled+label {\r\n        &::before {\r\n            cursor: not-allowed;\r\n        }\r\n    }\r\n}\r\n\r\n.radio.radio-inline {\r\n    margin-top: 0;\r\n}\r\n\r\n.radio.radio-single {\r\n    label {\r\n        height: 17px;\r\n    }\r\n}\r\n\r\n@each $color,\r\n$value in $theme-colors {\r\n    .radio-#{$color} {\r\n        input[type=\"radio\"]+label {\r\n            &::after {\r\n                background-color: $value;\r\n            }\r\n        }\r\n        input[type=\"radio\"]:checked+label {\r\n            &::before {\r\n                border-color: $value;\r\n            }\r\n            &::after {\r\n                background-color: $value;\r\n            }\r\n        }\r\n    }\r\n}", "// \r\n// print.scss\r\n//\r\n\r\n// Used invoice page\r\n@media print {\r\n    .left-side-menu,\r\n    .right-bar,\r\n    .page-title-box,\r\n    .navbar-custom,\r\n    .footer {\r\n        display: none;\r\n    }\r\n    .card-body,\r\n    .content-page,\r\n    .right-bar,\r\n    .content,\r\n    body {\r\n        padding: 0;\r\n        margin: 0;\r\n    }\r\n}", "/* =============\r\n   Widgets\r\n============= */\r\n\r\n.tilebox-one {\r\n  background: url(\"../images/bg-1.png\");\r\n  background-size: cover;\r\n  border: 4px solid $white;\r\n\r\n  i {\r\n    background: $primary;\r\n    background: $bg-gradient;\r\n    font-size: 24px;\r\n    height: 50px;\r\n    line-height: 50px;\r\n    width: 50px;\r\n    text-align: center;\r\n    color: $white !important;\r\n    border-radius: 50%;\r\n    box-shadow: $box-shadow-lg;\r\n  }\r\n}\r\n\r\n\r\n/* Inbox-widget */\r\n\r\n.inbox-widget {\r\n  .inbox-item {\r\n    border-bottom: 1px solid lighten($gray-300,5%);\r\n    overflow: hidden;\r\n    padding: 10px 0;\r\n    position: relative;\r\n    .inbox-item-img {\r\n      display: block;\r\n      float: left;\r\n      margin-right: 15px;\r\n      width: 40px;\r\n    }\r\n    img {\r\n      width: 40px;\r\n    }\r\n    .inbox-item-author {\r\n      color: $dark;\r\n      display: block;\r\n      margin: 0;\r\n    }\r\n    .inbox-item-text {\r\n      color: $text-muted;\r\n      display: block;\r\n      font-size: 14px;\r\n      margin: 0;\r\n    }\r\n    .inbox-item-date {\r\n      color: $text-muted;\r\n      font-size: 11px;\r\n      position: absolute;\r\n      right: 7px;\r\n      top: 7px;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n/* Comment List */\r\n.comment-list {\r\n  .comment-box-item {\r\n    position: relative;\r\n\r\n    .commnet-item-date {\r\n      color: $text-muted;\r\n      font-size: 11px;\r\n      position: absolute;\r\n      right: 7px;\r\n      top: 2px;\r\n    }\r\n    .commnet-item-msg {\r\n      color: $dark;\r\n      display: block;\r\n      margin: 10px 0;\r\n      font-weight: normal;\r\n      font-size: 15px;\r\n      line-height: 24px;\r\n    }\r\n    .commnet-item-user {\r\n      color: $text-muted;\r\n      display: block;\r\n      font-size: 14px;\r\n      margin: 0;\r\n    }\r\n  }\r\n  a + a {\r\n    margin-top: 15px;\r\n    display: block;\r\n  }\r\n}\r\n\r\n\r\n/* Transaction */\r\n\r\n.transaction-list {\r\n  li{\r\n    padding: 7px 0;\r\n    border-bottom: 1px solid lighten($gray-300,5%);\r\n    clear: both;\r\n    position: relative;\r\n  }\r\n  i{\r\n    width: 20px;\r\n    position: absolute;\r\n    top: 10px;\r\n    font-size: 12px;\r\n  }\r\n  .tran-text {\r\n    padding-left: 25px;\r\n    white-space: nowrap;\r\n    display: inline-block;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    width: 150px;\r\n  }\r\n  .tran-price {\r\n    margin-left: 30px;\r\n  }\r\n}", "/*!\r\n * Waves v0.7.6\r\n * http://fian.my.id/Waves \r\n * \r\n * Copyright 2014-2018 <PERSON><PERSON><PERSON> Si<PERSON> and other contributors \r\n * Released under the MIT license \r\n * https://github.com/fians/Waves/blob/master/LICENSE */\r\n .waves-effect {\r\n  position: relative;\r\n  cursor: pointer;\r\n  display: inline-block;\r\n  overflow: hidden;\r\n  -webkit-user-select: none;\r\n  -moz-user-select: none;\r\n  -ms-user-select: none;\r\n  user-select: none;\r\n  -webkit-tap-highlight-color: transparent;\r\n}\r\n.waves-effect .waves-ripple {\r\n  position: absolute;\r\n  border-radius: 50%;\r\n  width: 100px;\r\n  height: 100px;\r\n  margin-top: -50px;\r\n  margin-left: -50px;\r\n  opacity: 0;\r\n  background: rgba(0, 0, 0, 0.2);\r\n  background: -webkit-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  background: -o-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  background: -moz-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  -webkit-transition: all 0.5s ease-out;\r\n  -moz-transition: all 0.5s ease-out;\r\n  -o-transition: all 0.5s ease-out;\r\n  transition: all 0.5s ease-out;\r\n  -webkit-transition-property: -webkit-transform, opacity;\r\n  -moz-transition-property: -moz-transform, opacity;\r\n  -o-transition-property: -o-transform, opacity;\r\n  transition-property: transform, opacity;\r\n  -webkit-transform: scale(0) translate(0, 0);\r\n  -moz-transform: scale(0) translate(0, 0);\r\n  -ms-transform: scale(0) translate(0, 0);\r\n  -o-transform: scale(0) translate(0, 0);\r\n  transform: scale(0) translate(0, 0);\r\n  pointer-events: none;\r\n}\r\n.waves-effect.waves-light .waves-ripple {\r\n  background: rgba(255, 255, 255, 0.4);\r\n  background: -webkit-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  background: -o-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  background: -moz-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n}\r\n.waves-effect.waves-classic .waves-ripple {\r\n  background: rgba(0, 0, 0, 0.2);\r\n}\r\n.waves-effect.waves-classic.waves-light .waves-ripple {\r\n  background: rgba(255, 255, 255, 0.4);\r\n}\r\n.waves-notransition {\r\n  -webkit-transition: none !important;\r\n  -moz-transition: none !important;\r\n  -o-transition: none !important;\r\n  transition: none !important;\r\n}\r\n.waves-button,\r\n.waves-circle {\r\n  -webkit-transform: translateZ(0);\r\n  -moz-transform: translateZ(0);\r\n  -ms-transform: translateZ(0);\r\n  -o-transform: translateZ(0);\r\n  transform: translateZ(0);\r\n  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);\r\n}\r\n.waves-button,\r\n.waves-button:hover,\r\n.waves-button:visited,\r\n.waves-button-input {\r\n  white-space: nowrap;\r\n  vertical-align: middle;\r\n  cursor: pointer;\r\n  border: none;\r\n  outline: none;\r\n  color: inherit;\r\n  background-color: rgba(0, 0, 0, 0);\r\n  font-size: 1em;\r\n  line-height: 1em;\r\n  text-align: center;\r\n  text-decoration: none;\r\n  z-index: 1;\r\n}\r\n.waves-button {\r\n  padding: 0.85em 1.1em;\r\n  border-radius: 0.2em;\r\n}\r\n.waves-button-input {\r\n  margin: 0;\r\n  padding: 0.85em 1.1em;\r\n}\r\n.waves-input-wrapper {\r\n  border-radius: 0.2em;\r\n  vertical-align: bottom;\r\n}\r\n.waves-input-wrapper.waves-button {\r\n  padding: 0;\r\n}\r\n.waves-input-wrapper .waves-button-input {\r\n  position: relative;\r\n  top: 0;\r\n  left: 0;\r\n  z-index: 1;\r\n}\r\n.waves-circle {\r\n  text-align: center;\r\n  width: 2.5em;\r\n  height: 2.5em;\r\n  line-height: 2.5em;\r\n  border-radius: 50%;\r\n}\r\n.waves-float {\r\n  -webkit-mask-image: none;\r\n  -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\r\n  box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\r\n  -webkit-transition: all 300ms;\r\n  -moz-transition: all 300ms;\r\n  -o-transition: all 300ms;\r\n  transition: all 300ms;\r\n}\r\n.waves-float:active {\r\n  -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\r\n  box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\r\n}\r\n.waves-block {\r\n  display: block;\r\n}\r\n", "//\r\n// slimscroll.scss\r\n//\r\n\r\n.slimScrollDiv {\r\n    height: auto !important;\r\n}", "//\r\n// toastr.scss\r\n//\r\n\r\n.jq-toast-single {\r\n    padding: 15px;\r\n    font-family: $font-family-base;\r\n    background-color: $primary;\r\n    font-size: 13px;\r\n    line-height: 22px;\r\n    h2 {\r\n        font-family: $font-family-base;\r\n    }\r\n    a {\r\n        font-size: $font-size-base;\r\n        &:hover {\r\n            color: $white;\r\n        }\r\n    }\r\n}\r\n\r\n.jq-has-icon {\r\n    padding: 10px 10px 10px 50px;\r\n}\r\n\r\n.close-jq-toast-single {\r\n    position: absolute;\r\n    top: -12px;\r\n    right: -12px;\r\n    font-size: 20px;\r\n    cursor: pointer;\r\n    height: 32px;\r\n    width: 32px;\r\n    background-color: $dark;\r\n    border-radius: 50%;\r\n    text-align: center;\r\n    line-height: 32px;\r\n}\r\n\r\n.jq-toast-loader {\r\n    height: 3px;\r\n    top: 0;\r\n    border-radius: 0;\r\n}\r\n\r\n@each $color,$value in $theme-colors {\r\n    .jq-icon-#{$color} {\r\n        background-color: #{$value};\r\n        color: $white;\r\n        border-color: #{$value};\r\n    }\r\n}\r\n\r\n// For error\r\n.jq-icon-error {\r\n    background-color: $danger;\r\n    color: $white;\r\n    border-color: $danger;\r\n}", "\r\n// \r\n// sweetalert.scss\r\n//\r\n\r\n.swal2-modal {\r\n  font-family: $font-family-base;\r\n  box-shadow: 0 10px 33px rgba(0,0,0,.1);\r\n  border: 2px solid $primary;\r\n\r\n  .swal2-title {\r\n    font-size: 24px;\r\n  }\r\n  .swal2-content {\r\n    font-size: 16px;\r\n  }\r\n  .swal2-spacer {\r\n    margin: 10px 0;\r\n  }\r\n  .swal2-file, .swal2-input, .swal2-textarea {\r\n    border: 2px solid $gray-300;\r\n    font-size: 16px;\r\n    box-shadow: none;\r\n  }\r\n  .swal2-confirm.btn-confirm {\r\n    background-color: $primary !important;\r\n    font-size: $font-size-base;\r\n  }\r\n\r\n  .swal2-cancel.btn-cancel {\r\n    background-color: $danger !important;\r\n    font-size: $font-size-base;\r\n  }\r\n\r\n  .swal2-styled:focus {\r\n    box-shadow: none !important;\r\n  }\r\n}\r\n\r\n.swal2-icon.swal2-question {\r\n  color: $primary;\r\n  border-color: $primary;\r\n}\r\n\r\n.swal2-icon.swal2-success {\r\n  border-color: $success;\r\n\r\n  .line,[class^=swal2-success-line][class$=long],\r\n  [class^=swal2-success-line]{\r\n    background-color: $success;\r\n  }\r\n\r\n  .placeholder,.swal2-success-ring  {\r\n    border-color: $success;\r\n  }\r\n}\r\n\r\n\r\n.swal2-icon.swal2-warning {\r\n  color: $warning;\r\n  border-color: $warning;\r\n}\r\n\r\n.swal2-icon.swal2-error {\r\n  border-color: $danger;\r\n  .line {\r\n    background-color: $danger;\r\n  }\r\n}\r\n.swal2-modal .swal2-file:focus, .swal2-modal .swal2-input:focus, .swal2-modal .swal2-textarea:focus {\r\n  outline: 0;\r\n  border: 2px solid $primary;\r\n}\r\n\r\n.swal2-container.swal2-shown {\r\n  background-color: rgba($white, 0.9);\r\n}\r\n", "//\r\n// flot.scss\r\n//\r\n\r\n.flotTip {\r\n    padding: 8px 12px;\r\n    background-color: $white;\r\n    z-index: 99;\r\n    color: $dark;\r\n    box-shadow: $box-shadow-lg;\r\n    opacity: 1;\r\n    border-radius: 3px;\r\n}\r\n\r\n.legend {\r\n    tr {\r\n        height: 30px;\r\n        font-family: $font-family-secondary;\r\n    }\r\n}\r\n\r\n.legendLabel {\r\n    padding-left: 5px !important;\r\n    line-height: 10px;\r\n    padding-right: 20px;\r\n    font-size: 13px;\r\n    font-weight: $font-weight-medium;\r\n    color: $gray-600;\r\n    text-transform: uppercase;\r\n}\r\n\r\n.legendColorBox {\r\n    div {\r\n        div {\r\n            border-radius: 50%;\r\n        }\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(sm) {\r\n    .legendLabel {\r\n        display: none;\r\n    }\r\n}", "//\r\n// morris.scss\r\n//\r\n\r\n.morris-chart {\r\n    text {\r\n        font-family: $font-family-secondary !important;\r\n    }\r\n}\r\n.morris-hover {\r\n    position: absolute;\r\n    z-index: 10;\r\n\r\n    &.morris-default-style {\r\n        font-size: 12px;\r\n        text-align: center;\r\n        border-radius: 5px;\r\n        padding: 10px 12px;\r\n        background: $white;\r\n        color: $dark;\r\n        box-shadow: $box-shadow-lg;\r\n        font-family: $font-family-base;\r\n\r\n        .morris-hover-row-label {\r\n            font-weight: bold;\r\n            margin: 0.25em 0;\r\n            font-family: $font-family-secondary;\r\n        }\r\n\r\n        .morris-hover-point {\r\n            white-space: nowrap;\r\n            margin: 0.1em 0;\r\n            color: $white;\r\n        }\r\n    }\r\n}", "//\r\n// chartjs.scss\r\n//\r\n\r\n.chartjs-chart {\r\n    margin: auto; \r\n    position: relative; \r\n    width: 100%;\r\n}", "//\r\n// sparkline.scss\r\n//\r\n\r\n.jqstooltip {\r\n  box-sizing: content-box;\r\n  width: auto !important;\r\n  height: auto !important;\r\n  background-color: $white !important;\r\n  box-shadow: $box-shadow-lg;\r\n  padding: 5px 10px !important;\r\n  border-radius: 3px;\r\n  border-color: $white !important;\r\n}\r\n\r\n.jqsfield {\r\n  color: $dark !important;\r\n  font-size: 12px !important;\r\n  line-height: 18px !important;\r\n  font-family: $font-family-base !important;\r\n  font-weight: $font-weight-bold !important;\r\n}", "/* =============\r\n   Maps\r\n============= */\r\n.gmaps,\r\n.gmaps-panaroma {\r\n  height: 300px;\r\n  background: #eeeeee;\r\n  border-radius: 3px;\r\n}\r\n.gmaps-overlay {\r\n  display: block;\r\n  text-align: center;\r\n  color: $white;\r\n  font-size: 16px;\r\n  line-height: 40px;\r\n  background: $primary;\r\n  border-radius: 4px;\r\n  padding: 10px 20px;\r\n}\r\n.gmaps-overlay_arrow {\r\n  left: 50%;\r\n  margin-left: -16px;\r\n  width: 0;\r\n  height: 0;\r\n  position: absolute;\r\n}\r\n.gmaps-overlay_arrow.above {\r\n  bottom: -15px;\r\n  border-left: 16px solid transparent;\r\n  border-right: 16px solid transparent;\r\n  border-top: 16px solid $primary;\r\n}\r\n.gmaps-overlay_arrow.below {\r\n  top: -15px;\r\n  border-left: 16px solid transparent;\r\n  border-right: 16px solid transparent;\r\n  border-bottom: 16px solid $primary;\r\n}\r\n.gmaps-full {\r\n  z-index: 99;\r\n  margin: 0 -20px -10px -20px;\r\n\r\n  .gmaps-full1 {\r\n    height: 80vh;\r\n    width: 100%;\r\n  }\r\n}\r\n", "\r\n//\r\n// vector-maps.scss\r\n//\r\n\r\n.jvectormap-label {\r\n  border: none;\r\n  background: $dark;\r\n  color: $white;\r\n  font-family: $font-family-secondary;\r\n  font-size: $font-size-base;\r\n  padding: 5px 8px;\r\n}", "\r\n/* Mapael Map */\r\n\r\n//\r\n// mapeal-maps.scss\r\n//\r\n\r\n.mapael {\r\n  .map {\r\n      position: relative;\r\n\r\n      .zoomIn {\r\n          top: 25px;\r\n      }\r\n      \r\n      .zoomOut {\r\n          top: 50px;\r\n      }\r\n  }\r\n  .mapTooltip {\r\n      position: absolute;\r\n      background-color: $primary;\r\n      opacity: 0.95;\r\n      border-radius: 3px;\r\n      padding: 2px 10px;\r\n      z-index: 1000;\r\n      max-width: 200px;\r\n      display: none;\r\n      color: $white;\r\n      font-family: $font-family-secondary;\r\n  }\r\n  .zoomIn,\r\n  .zoomOut,\r\n  .zoomReset {\r\n      display: inline-block;\r\n      text-align: center;\r\n      vertical-align: middle;\r\n      border-radius: 2px;\r\n      font-weight: 500;\r\n      cursor: pointer;\r\n      background-color: $primary;\r\n      text-decoration: none;\r\n      color: $white;\r\n      font-size: 14px;\r\n      position: absolute;\r\n      top: 0;\r\n      left: 10px;\r\n      width: 24px;\r\n      height: 24px;\r\n      line-height: 24px;\r\n  }\r\n\r\n  .plotLegend {\r\n      text {\r\n          font-family: $font-family-base !important;\r\n      }\r\n  }\r\n}", "// \r\n// calendar.scss\r\n//\r\n\r\n.calendar {\r\n  float: left;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.fc-view {\r\n  margin-top: 30px;\r\n}\r\n\r\n.none-border {\r\n  .modal-footer {\r\n      border-top: none;\r\n  }\r\n}\r\n\r\n.fc-toolbar {\r\n  margin: 15px 0 5px 0;\r\n  h2 {\r\n      font-size: 1.25rem;\r\n      line-height: 1.875rem;\r\n      text-transform: uppercase;\r\n  }\r\n}\r\n\r\n.fc-day-grid-event {\r\n  .fc-time {\r\n      font-weight: $font-weight-bold;\r\n  }\r\n}\r\n\r\n.fc-day {\r\n  background: $white;\r\n}\r\n\r\n.fc-toolbar {\r\n  .fc-state-active,\r\n  .ui-state-active,\r\n  button:focus,\r\n  button:hover,\r\n  .ui-state-hover {\r\n      z-index: 0;\r\n  }\r\n}\r\n\r\n.fc {\r\n  th.fc-widget-header {\r\n      background: $gray-100;\r\n      font-size: 13px;\r\n      line-height: 20px;\r\n      padding: 10px 0;\r\n      text-transform: uppercase;\r\n  }\r\n}\r\n\r\n.fc-unthemed {\r\n  th,\r\n  td,\r\n  thead,\r\n  tbody,\r\n  .fc-divider,\r\n  .fc-row,\r\n  .fc-popover {\r\n      border-color: $gray-300;\r\n  }\r\n}\r\n\r\n.fc-ltr {\r\n  .fc-basic-view {\r\n      .fc-day-top {\r\n          .fc-day-number {\r\n              float: right;\r\n              margin: 5px;\r\n              font-family: $font-family-secondary;\r\n              font-size: 12px;\r\n          }\r\n      }\r\n  }\r\n}\r\n\r\n.fc-button {\r\n  background: $gray-100;\r\n  border: none;\r\n  color: $gray-700;\r\n  text-transform: capitalize;\r\n  box-shadow: none;\r\n  border-radius: 3px;\r\n  margin: 0 3px;\r\n  padding: 6px 12px;\r\n  height: auto;\r\n}\r\n\r\n.fc-text-arrow {\r\n  font-family: inherit;\r\n  font-size: 1rem;\r\n}\r\n\r\n.fc-state-hover {\r\n  background: $gray-100;\r\n}\r\n\r\n.fc-state-highlight {\r\n  background: $gray-300;\r\n}\r\n\r\n.fc-state-down,\r\n.fc-state-active,\r\n.fc-state-disabled {\r\n  background: $bg-gradient;\r\n  color: $white;\r\n  text-shadow: none;\r\n}\r\n\r\n.fc-cell-overlay {\r\n  background: $gray-300;\r\n}\r\n\r\n.fc-unthemed {\r\n  .fc-today {\r\n      background: $white;\r\n  }\r\n}\r\n\r\n.fc-event {\r\n  border-radius: 2px;\r\n  border: none;\r\n  cursor: move;\r\n  font-size: 0.8125rem;\r\n  margin: 5px 7px;\r\n  padding: 5px 5px;\r\n  text-align: center;\r\n}\r\n\r\n.external-event {\r\n  cursor: move;\r\n  margin: 10px 0;\r\n  padding: 8px 10px;\r\n  color: $white;\r\n}\r\n\r\n.fc-basic-view {\r\n  td.fc-week-number {\r\n      span {\r\n          padding-right: 8px;\r\n      }\r\n  }\r\n  td.fc-day-number {\r\n      padding-right: 8px;\r\n  }\r\n  .fc-content {\r\n      color: $white;\r\n  }\r\n}\r\n\r\n.fc-time-grid-event {\r\n  .fc-content {\r\n      color: $white;\r\n  }\r\n}\r\n\r\n@include media-breakpoint-down(sm) {\r\n  .fc-toolbar {\r\n      .fc-left,.fc-right,.fc-center {\r\n          float: none;\r\n          display: block;\r\n          clear: both;\r\n          margin: 10px 0;\r\n      }\r\n  }\r\n  .fc {\r\n      .fc-toolbar{\r\n          >* {\r\n              >* {\r\n                  float: none;\r\n              }\r\n          }\r\n      }\r\n  }\r\n  .fc-today-button {\r\n      display: none;\r\n  }\r\n}", "/* =============\r\n   Summernote\r\n============= */\r\n\r\n\r\n\r\n\r\n@font-face {\r\n  font-family: \"summernote\";\r\n  font-style: normal;\r\n  font-weight: normal;\r\n  src: url(\"../fonts/summernote.eot\");\r\n  src: url(\"../fonts/summernote.eot?#iefix\") format(\"embedded-opentype\"), url(\"../fonts/summernote.woff?\") format(\"woff\"), \r\n    url(\"../fonts/summernote.ttf?\") format(\"truetype\")\r\n}\r\n\r\n.note-editor{\r\n  &.note-frame {\r\n    border: 1px solid $input-border-color;\r\n    box-shadow: none;\r\n    margin: 0;\r\n\r\n    .note-statusbar {\r\n      background-color: lighten($gray-200,2%);\r\n      border-top: 1px solid $gray-200;\r\n    }\r\n\r\n    .note-editable {\r\n        border: none;\r\n    }\r\n  }\r\n}\r\n\r\n.note-status-output {\r\n  display: none;\r\n}\r\n\r\n.note-editable {\r\n  border: none;\r\n  border-radius: $input-border-radius;\r\n  padding: $input-padding-y $input-padding-x;\r\n\r\n  p {\r\n    &:last-of-type {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.note-popover .popover-content .note-color .dropdown-menu,\r\n.card-header.note-toolbar .note-color .dropdown-menu {\r\n    min-width: 344px;\r\n}\r\n\r\n.note-toolbar {\r\n   z-index: 1;\r\n}", "//\r\n// Select2.scss\r\n//\r\n\r\n.select2-container {\r\n    .select2-selection--single {\r\n        border: 1px solid $input-border-color;\r\n        height: 38px;\r\n        outline: none;\r\n        .select2-selection__rendered {\r\n            line-height: 36px;\r\n            padding-left: 12px;\r\n        }\r\n        .select2-selection__arrow {\r\n            height: 34px;\r\n            width: 34px;\r\n            right: 3px;\r\n            b {\r\n                border-color: darken($light, 15%) transparent transparent transparent;\r\n                border-width: 6px 6px 0 6px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.select2-container--open {\r\n    .select2-selection--single {\r\n        .select2-selection__arrow {\r\n            b {\r\n                border-color: transparent transparent darken($light, 15%) transparent !important;\r\n                border-width: 0 6px 6px 6px !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.select2-results__option {\r\n    padding: 6px 12px;\r\n}\r\n\r\n.select2-dropdown {\r\n    border: 1px solid darken($light, 5%);\r\n    box-shadow: $box-shadow;\r\n}\r\n\r\n.select2-container--default {\r\n    .select2-search--dropdown {\r\n        padding: 10px;\r\n        background-color: lighten($light, 5%);\r\n        .select2-search__field {\r\n            border: 1px solid darken($light, 5%);\r\n            outline: none;\r\n        }\r\n    }\r\n    .select2-results__option--highlighted[aria-selected] {\r\n        background: $bg-gradient;;\r\n    }\r\n    .select2-results__option[aria-selected=true] {\r\n        background-color: $light;\r\n        color: $dark;\r\n        &:hover {\r\n            background: $bg-gradient;;\r\n            color: $white;\r\n        }\r\n    }\r\n}\r\n\r\n.select2-container {\r\n    .select2-selection--multiple {\r\n        min-height: 38px;\r\n        border: 1px solid $input-border-color !important;\r\n        .select2-selection__rendered {\r\n            padding: 1px 10px;\r\n        }\r\n        .select2-search__field {\r\n            border: 0;\r\n        }\r\n        .select2-selection__choice {\r\n            background: $bg-gradient;\r\n            border: none;\r\n            color: $white;\r\n            border-radius: 3px;\r\n            padding: 0 7px;\r\n            margin-top: 7px;\r\n        }\r\n        .select2-selection__choice__remove {\r\n            color: $white;\r\n            margin-right: 5px;\r\n            &:hover {\r\n                color: $white;\r\n            }\r\n        }\r\n    }\r\n}", "//\r\n// autocomplete.scss\r\n//\r\n \r\n.autocomplete-suggestions {\r\n    border: 1px solid #f9f9f9;\r\n    background: $white;\r\n    cursor: default;\r\n    overflow: auto;\r\n    max-height: 200px !important;\r\n    box-shadow: 0 2px 2px rgba(0, 0, 0, 0.15);\r\n    strong {\r\n      font-weight: bold;\r\n      color: $dark;\r\n    }\r\n  }\r\n  \r\n  .autocomplete-suggestion {\r\n    padding: 5px 10px;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n  }\r\n  \r\n  .autocomplete-no-suggestion {\r\n    padding: 5px;\r\n  }\r\n  \r\n  .autocomplete-selected {\r\n    background: $gray-200;\r\n    cursor: pointer;\r\n  }\r\n  \r\n\r\n  .autocomplete-group {\r\n    padding: 5px;\r\n    font-weight: $font-weight-medium;\r\n    font-family: $font-family-secondary;\r\n    strong {\r\n      font-weight: bold;\r\n      font-size: 16px;\r\n      color: $dark;\r\n      display: block;\r\n    }\r\n    \r\n  }\r\n", "\r\n//\r\n// bootstrap-taginput.scss\r\n//\r\n\r\n/* Bootstrap tagsinput */\r\n.bootstrap-tagsinput {\r\n    box-shadow: none;\r\n    padding: 4px 7px 4px;\r\n    border: 1px solid darken($light,3%);\r\n    width: 100%;\r\n  \r\n    .label-info {\r\n      background: $bg-gradient;\r\n      display: inline-block;\r\n      font-size: 11px;\r\n      margin: 3px 1px;\r\n      padding: 0 5px;\r\n      border-radius: 3px;\r\n    }\r\n}", "//\r\n// bootstrap-select.scss\r\n//\r\n \r\n.bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {\r\n    width: 100% !important;\r\n}\r\n\r\n.bootstrap-select {\r\n    .dropdown-menu{\r\n        .dropdown-menu {\r\n            li{\r\n                a{\r\n                    display: block;\r\n                    width: 100%;\r\n                    clear: both;\r\n                    font-weight: 400;\r\n                    color: $dark;\r\n                    text-align: inherit;\r\n                    white-space: nowrap;\r\n                    background: 0 0;\r\n                    border: 0;\r\n                    &:hover {\r\n                      background: $bg-gradient;\r\n                      color: $white;\r\n                    }\r\n                  }\r\n            }\r\n        }\r\n    }\r\n    .dropdown-toggle{\r\n        &:after {\r\n            content: \"\\F140\";\r\n            display: inline-block;\r\n            font-family: \"Material Design Icons\";\r\n        }\r\n        &:focus {\r\n            outline: none !important;\r\n            outline-offset: 0;\r\n        }\r\n    }\r\n    a {\r\n        outline: none !important;\r\n    }\r\n    .inner {\r\n        overflow-y: inherit !important;\r\n    }\r\n    >.btn-pink {\r\n        &.bs-placeholder{\r\n            color: $white !important;\r\n        }\r\n    }\r\n}\r\n\r\n", "//\r\n// parsley.scss\r\n//\r\n \r\n.parsley-errors-list {\r\n  margin: 0;\r\n  padding: 0;\r\n\r\n  > li {\r\n    list-style: none;\r\n    color: $danger;\r\n    margin-top: 5px;\r\n    padding-left: 20px;\r\n    position: relative;\r\n  \r\n    &:before {\r\n      content: \"\\F159\";\r\n      font-family: \"Material Design Icons\";\r\n      position: absolute;\r\n      left: 2px;\r\n      top: -1px;\r\n    }\r\n  }\r\n}\r\n.parsley-error {\r\n  border-color: $danger;\r\n}\r\n\r\n.parsley-success {\r\n  border-color: $success;\r\n}\r\n\r\n", "\r\n// \r\n// timepicker.scss\r\n//\r\n\r\n.bootstrap-timepicker-widget {\r\n    table {\r\n        td {\r\n            input {\r\n                width: 35px;\r\n                border: 0px;\r\n    \r\n            }\r\n            \r\n            a{\r\n                &:hover {\r\n                    background-color: transparent;\r\n                    border: 1px solid transparent;\r\n                }\r\n            }\r\n\r\n        }     \r\n    }\r\n}", "//\r\n// Daterange\r\n//\r\n\r\n/* Daterange Picker */\r\n.daterangepicker td.active, .daterangepicker td.active:hover {\r\n    background: $primary;\r\n    background: $bg-gradient;\r\n  }\r\n  .daterangepicker .input-mini.active {\r\n    border: 1px solid rgba($dark,0.3);\r\n  }\r\n  .daterangepicker .ranges li {\r\n    border-radius: 2px;\r\n    color: $dark;\r\n    font-weight: 600;\r\n    font-size: 12px;\r\n  }\r\n  .daterangepicker select.hourselect, .daterangepicker select.minuteselect,\r\n  .daterangepicker select.secondselect, .daterangepicker select.ampmselect{\r\n    border: 1px solid rgba($dark,0.3);\r\n    padding: 2px;\r\n    width: 60px;\r\n  }\r\n  .daterangepicker .ranges li.active, .daterangepicker .ranges li:hover {\r\n    background: $primary;\r\n    background: $bg-gradient;\r\n    border: 1px solid $primary;\r\n    color: $white;\r\n  }\r\n  .daterangepicker select.monthselect, .daterangepicker select.yearselect {\r\n    border-color: $text-muted;\r\n  }\r\n  \r\n\r\n", "//\r\n// clockpicker.scss\r\n//\r\n\r\n\r\n\r\n/* Clock picker */\r\n.clockpicker-canvas line {\r\n    stroke: lighten($primary,15%);\r\n  }\r\n  .clockpicker-canvas-bearing, .clockpicker-canvas-fg,.clockpicker-canvas-bg {\r\n    fill: lighten($primary,15%);\r\n  }\r\n\r\n  .clockpicker-popover {\r\n    .btn-default {\r\n        background-color: $primary;\r\n        color: $white;\r\n    }\r\n}", "//\r\n// form-wizard.scss\r\n//\r\n\r\n\r\n.wizard{\r\n  // steps\r\n\r\n  >.steps {\r\n      \r\n      position: relative;\r\n      display: block;\r\n      width: 100%;\r\n      >ul{\r\n          >li {\r\n              width: 25%;\r\n          }\r\n      }\r\n\r\n      a{\r\n        font-size: 16px;\r\n        margin: 0 0.5em 0.5em;\r\n      }\r\n      .number {\r\n        border-radius: 50%;\r\n        background-color: rgba(255,255,255,0.3);\r\n        display: inline-block;\r\n        line-height: 30px;\r\n        margin-right: 10px;\r\n        width: 30px;\r\n        text-align: center;\r\n        font-size: 1.429em;\r\n        \r\n      }\r\n\r\n      a, a:hover, a:active{\r\n          display: block;\r\n          width: auto;\r\n          padding: 1em 1em;\r\n          text-decoration: none;\r\n          border-radius: 2px;\r\n      }\r\n\r\n      .disabled{\r\n          a{\r\n            border: 1px solid darken($gray-200, 4%);\r\n            color: $dark;\r\n              cursor: default;\r\n            &:hover, &:active{\r\n              \r\n          }\r\n        }\r\n      }\r\n\r\n      .current{\r\n          a, a:hover, a:active{\r\n              background: $bg-gradient;\r\n              color: $white;\r\n              cursor: default;\r\n              .number {\r\n                color: $white;\r\n              }\r\n          }\r\n      }\r\n\r\n      .done{\r\n          a, a:hover, a:active{\r\n            background: lighten($primary,20%);\r\n            color: $white;\r\n          }\r\n      }\r\n\r\n      .error{\r\n          a, a:hover, a:active{\r\n              background: lighten($danger, 32%);\r\n              color: $white;\r\n              border-color: lighten($danger, 28%);\r\n          }\r\n      }\r\n  }\r\n\r\n  >.steps, >.actions{\r\n      >ul{\r\n          >li {\r\n              float: left;\r\n              position: relative;\r\n          }\r\n      }\r\n  }\r\n\r\n  // content\r\n\r\n  >.content {\r\n      display: block;\r\n      min-height: 240px;\r\n      overflow: hidden;\r\n      position: relative;\r\n      width: auto;\r\n      padding: 20px;\r\n      >.body {\r\n          padding: 0;\r\n          position: relative;\r\n          width: 95%;\r\n\r\n          ul {\r\n              list-style: disc !important;\r\n              >li {\r\n                  display: block;\r\n                  line-height: 30px;\r\n              }\r\n          }\r\n\r\n          >iframe {\r\n              border: 0 none;\r\n              width: 100%;\r\n              height: 100%;\r\n          }\r\n\r\n          input {\r\n              display: block;\r\n              border-color: $gray-300;\r\n\r\n              &:focus {\r\n                  border-color: $gray-300;\r\n              }\r\n              &[type=\"checkbox\"] {\r\n                  display: inline-block;\r\n              }\r\n\r\n              &.error {\r\n                  background: lighten($danger, 42%);\r\n                  border: 1px solid lighten($danger, 32%);\r\n                  color: $danger;\r\n              }\r\n          }\r\n\r\n          label {\r\n              display: inline-block;\r\n              margin-bottom: 0.5em;\r\n              margin-top: 10px;\r\n              &.error {\r\n                  color: $danger;\r\n                  font-size: 12px;\r\n              }\r\n          }\r\n      }\r\n  }\r\n\r\n  // actions\r\n\r\n  >.actions {\r\n      position: relative;\r\n      display: block;\r\n      text-align: right;\r\n      width: 100%;\r\n      margin-top: 15px;\r\n      >ul {\r\n          display: inline-block;\r\n          text-align: right;\r\n          >li {\r\n              margin: 0 0.5em;\r\n          }\r\n      }\r\n\r\n      a, a:hover, a:active{\r\n          background: $bg-gradient;\r\n          color: $white;\r\n          display: block;\r\n          padding: 0.5em 1em;\r\n          text-decoration: none;\r\n          border-radius: 2px;\r\n      }\r\n\r\n      .disabled{\r\n          a, a:hover, a:active{\r\n            background: $white;\r\n            color: $dark;\r\n            cursor: default;\r\n            border: 1px solid $light;\r\n          }\r\n      }\r\n  }\r\n\r\n  // vertical wizard\r\n\r\n  &.vertical{\r\n      >.steps {\r\n          display: inline;\r\n          float: left;\r\n          width: 30%;\r\n          >ul{\r\n              >li {\r\n                  float: none;\r\n                  width: 100%;\r\n              }\r\n          }\r\n      }\r\n\r\n      > .content{\r\n        width: 65%;\r\n        margin: 0 2.5% 0.5em;\r\n        display: inline;\r\n        float: left;\r\n      }\r\n\r\n      >.actions {\r\n          display: inline;\r\n          float: right;\r\n          width: 95%;\r\n          margin: 0 2.5%;\r\n          margin-top: 15px !important;\r\n          >ul{\r\n              >li {\r\n                  margin: 0 0 0 1em;\r\n              }\r\n          }\r\n      }\r\n  }  \r\n}\r\n\r\n\r\n\r\n/*\r\n  Common \r\n*/\r\n\r\n.wizard, .tabcontrol {\r\n  display: block;\r\n  width: 100%;\r\n  overflow: hidden;\r\n  a{\r\n      outline: 0;\r\n  }\r\n\r\n  ul {\r\n      list-style: none !important;\r\n      padding: 0;\r\n      margin: 0;\r\n\r\n      >li {\r\n          display: block;\r\n          padding: 0;\r\n      }\r\n  }\r\n\r\n  /* Accessibility */\r\n\r\n  >.steps {\r\n      .current-info {\r\n          position: absolute;\r\n          left: -999em;\r\n      }\r\n  }\r\n\r\n  >.content{\r\n      >.title {\r\n          position: absolute;\r\n          left: -999em;\r\n      }\r\n  }\r\n}\r\n\r\n@include media-breakpoint-down(sm) { \r\n  .wizard > .steps > ul > li,.wizard.vertical > .steps,.wizard.vertical > .content {\r\n      width: 100%;\r\n  }\r\n}", "//\r\n// x-editable.scss\r\n//\r\n\r\n.editable-clear-x {\r\n    background: url(\"../images/clear.png\") center center no-repeat;\r\n}\r\n\r\n.editableform-loading {\r\n    background: url('../images/loading.gif') center center no-repeat;\r\n}\r\n\r\n.editable-checklist label {\r\n    display: block;\r\n}", "// \r\n// dropzone.scss\r\n//\r\n\r\n.dropzone {\r\n  border: 2px dashed rgba($dark, 0.3);\r\n  background:$white;\r\n  border-radius: 6px;\r\n}", "//\r\n// datatable.scss\r\n//\r\n\r\n.dataTables_wrapper.container-fluid {\r\n    padding: 0;\r\n}\r\n\r\ntable.dataTable {\r\n    border-collapse: collapse !important;\r\n    margin-bottom: 15px !important;\r\n\r\n    tbody {\r\n        // Multi select table\r\n\r\n        > tr.selected, >tr>.selected {\r\n            background-color: $primary;\r\n            \r\n            td {\r\n                border-color: $primary;\r\n            }\r\n        }\r\n        td {\r\n            &:focus {\r\n                outline: none !important;\r\n            }\r\n        }\r\n        // Key Tables\r\n        th.focus,td.focus{\r\n            outline: 2px solid $primary !important;\r\n            outline-offset: -1px;\r\n            color: $primary;\r\n            background-color: rgba($primary, 0.15);\r\n        }\r\n    }\r\n}\r\n\r\n.dataTables_info {\r\n    font-weight: $font-weight-semibold;\r\n}\r\n\r\n\r\n// Responsive data table\r\ntable.dataTable.dtr-inline.collapsed {\r\n    > tbody {\r\n        >tr[role=row] {\r\n            > td, > th {\r\n                &:first-child{\r\n                    &:before{\r\n                        box-shadow: $box-shadow-lg;\r\n                        background-color: $primary;\r\n                        top: $table-cell-padding;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        >tr.parent {\r\n            > td, > th {\r\n                &:first-child{\r\n                    &:before{\r\n                        background-color: $danger;\r\n                        top: $table-cell-padding;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n\r\n// Data Table copy button\r\ndiv.dt-button-info {\r\n    background-color: $primary;\r\n    border: none;\r\n    color: $white;\r\n    box-shadow: none;\r\n    border-radius: 3px;\r\n    text-align: center;\r\n    z-index: 21;\r\n\r\n    h2 {\r\n        border-bottom: none;\r\n        background-color: rgba($white, 0.2);\r\n        color: $white;\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(sm) {\r\n    li.paginate_button.previous,li.paginate_button.next {\r\n        display: inline-block;\r\n    }\r\n \r\n    li.paginate_button {\r\n        display: none;\r\n    }\r\n    .dataTables_paginate {\r\n        ul {\r\n            text-align: center;\r\n            display: block;\r\n            margin: $spacer 0 0 !important;\r\n        }\r\n    }\r\n    div.dt-buttons {\r\n        display: inline-table;\r\n        margin-bottom: $spacer;\r\n    }\r\n}\r\n\r\n\r\n.activate-select {\r\n    .sorting_1 {\r\n        background-color: $gray-100;\r\n    }\r\n}", "// \r\n// responsive-table.scss\r\n//\r\n\r\n.table-rep-plugin {\r\n    .dropdown-menu li.checkbox-row {\r\n      padding: 7px 15px;\r\n    }\r\n  \r\n    .table-responsive {\r\n      border: none;\r\n    }\r\n    tbody {\r\n      th {\r\n        font-size: 14px;\r\n        font-weight: normal;\r\n      }\r\n    }\r\n    .checkbox-row {\r\n      padding-left: 40px;\r\n  \r\n      label {\r\n        display: inline-block;\r\n        padding-left: 5px;\r\n        position: relative;\r\n        margin-bottom: 0;\r\n        &::before {\r\n          -o-transition: 0.3s ease-in-out;\r\n          -webkit-transition: 0.3s ease-in-out;\r\n          background-color: $white;\r\n          border-radius: 3px;\r\n          border: 1px solid $text-muted;\r\n          content: \"\";\r\n          display: inline-block;\r\n          height: 17px;\r\n          left: 0;\r\n          margin-left: -20px;\r\n          position: absolute;\r\n          transition: 0.3s ease-in-out;\r\n          width: 17px;\r\n          outline: none;\r\n        }\r\n        &::after {\r\n          color: $gray-300;\r\n          display: inline-block;\r\n          font-size: 11px;\r\n          height: 16px;\r\n          left: 0;\r\n          margin-left: -20px;\r\n          padding-left: 3px;\r\n          padding-top: 1px;\r\n          position: absolute;\r\n          top: -1px;\r\n          width: 16px;\r\n        }\r\n      }\r\n      input[type=\"checkbox\"] {\r\n        cursor: pointer;\r\n        opacity: 0;\r\n        z-index: 1;\r\n        outline: none;\r\n  \r\n        &:disabled + label {\r\n          opacity: 0.65;\r\n        }\r\n      }\r\n      input[type=\"checkbox\"]:focus + label {\r\n        &::before {\r\n          outline-offset: -2px;\r\n          outline: none;\r\n        }\r\n      }\r\n      input[type=\"checkbox\"]:checked + label {\r\n        &::after {\r\n             content: \"\\f00c\";\r\n    font-family: 'Font Awesome 5 Free';\r\n    font-weight: 900;\r\n        }\r\n      }\r\n      input[type=\"checkbox\"]:disabled + label {\r\n        &::before {\r\n          background-color: $light;\r\n          cursor: not-allowed;\r\n        }\r\n      }\r\n      input[type=\"checkbox\"]:checked + label {\r\n        &::before {\r\n          background-color: $white;\r\n          border-color: $primary;\r\n        }\r\n        &::after {\r\n          color: $primary;\r\n        }\r\n      }\r\n    }\r\n    table.focus-on tbody tr.focused th, table.focus-on tbody tr.focused td,\r\n    .sticky-table-header{\r\n      background: $primary;\r\n      background: $bg-gradient;\r\n      color: $white;\r\n      border-color: $primary;\r\n      table{\r\n          color: $white;\r\n      }\r\n    }\r\n    .fixed-solution {\r\n        .sticky-table-header {\r\n            top: $topbar-height !important;\r\n        }\r\n    }\r\n    .btn-default {\r\n      background-color: $white;\r\n      border: 1px solid rgba($dark, 0.3);\r\n      color: $dark;\r\n    }\r\n    .btn-primary {\r\n        background: $bg-gradient;\r\n        border-color: $primary;\r\n        color: $white;\r\n    }\r\n    .btn-group.pull-right {\r\n      float: right;\r\n      .dropdown-menu {\r\n        left: auto;\r\n        right: 0;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .table-rep-plugin .btn-toolbar {\r\n    display: block;\r\n  }\r\n  ", "// \r\n// tablesaw.scss\r\n//\r\n\r\n.tablesaw {\r\n    thead {\r\n        background: $gray-100;\r\n        background-image: none;\r\n        border: none;\r\n        th {\r\n            text-shadow: none;\r\n        }\r\n        tr:first-child th {\r\n            border: none;\r\n            font-weight: 500;\r\n            font-family: $font-family-secondary;\r\n        }\r\n    }\r\n    td {\r\n        border-top: 1px solid $gray-100 !important;\r\n    }\r\n}\r\n\r\n.tablesaw td,\r\n.tablesaw tbody th {\r\n    font-size: inherit;\r\n    line-height: inherit;\r\n    padding: 10px !important;\r\n}\r\n\r\n.tablesaw-stack tbody tr,\r\n.tablesaw tbody tr {\r\n    border-bottom: none;\r\n}\r\n\r\n.tablesaw-bar .btn-select.btn-small:after,\r\n.tablesaw-bar .btn-select.btn-micro:after {\r\n    font-size: 8px;\r\n    padding-right: 10px;\r\n}\r\n\r\n.tablesaw-swipe .tablesaw-cell-persist {\r\n    box-shadow: none;\r\n    border-color: $gray-100;\r\n}\r\n\r\n.tablesaw-enhanced .tablesaw-bar .btn {\r\n    text-shadow: none;\r\n    background-image: none;\r\n    text-transform: none;\r\n    border: 1px solid $gray-300;\r\n    padding: 3px 10px;\r\n    color: $dark;\r\n\r\n    &:after {\r\n        display: none;\r\n    }\r\n}\r\n\r\n.tablesaw-enhanced .tablesaw-bar .btn.btn-select {\r\n    &:hover {\r\n        background: $white;\r\n    }\r\n}\r\n\r\n.tablesaw-enhanced .tablesaw-bar .btn:hover,\r\n.tablesaw-enhanced .tablesaw-bar .btn:focus,\r\n.tablesaw-enhanced .tablesaw-bar .btn:active {\r\n    color: $primary !important;\r\n    background-color: $gray-100;\r\n    outline: none !important;\r\n    box-shadow: none !important;\r\n    background-image: none;\r\n}\r\n\r\n.tablesaw-columntoggle-popup {\r\n    .btn-group {\r\n        display: block;\r\n    }\r\n}\r\n\r\n.tablesaw-swipe .tablesaw-swipe-cellpersist {\r\n    border-right: 2px solid $gray-100;\r\n}\r\n\r\n.tablesaw-sortable-btn {\r\n    cursor: pointer;\r\n}\r\n\r\n.tablesaw-swipe-cellpersist{\r\n    width: auto !important;\r\n}", "// \r\n// components-demo.scss\r\n//\r\n\r\n// Demo Only\r\n.button-list {\r\n    margin-left: -8px;\r\n    margin-bottom: -12px;\r\n    \r\n    .btn {\r\n        margin-bottom: 12px;\r\n        margin-left: 8px;\r\n    }\r\n}\r\n\r\n\r\n// Icon demo ( Demo only )\r\n.icons-list-demo {\r\n    div {\r\n        cursor: pointer;\r\n        line-height: 45px;\r\n        white-space: nowrap;\r\n        text-overflow: ellipsis;\r\n        display: block;\r\n        overflow: hidden;\r\n        p {\r\n            margin-bottom: 0;\r\n            line-height: inherit;\r\n        }\r\n    }\r\n    i {\r\n        text-align: center;\r\n        vertical-align: middle;\r\n        font-size: 22px;\r\n        width: 50px;\r\n        height: 50px;\r\n        line-height: 50px;\r\n        margin-right: 12px;\r\n        color: rgba($dark, 0.7);\r\n        border-radius: 3px;\r\n        display: inline-block;\r\n        transition: all 0.2s;\r\n    }\r\n    .col-lg-4 {\r\n        background-clip: padding-box;\r\n        margin-top: 10px;\r\n        &:hover i {\r\n            color: $white;\r\n            background: $bg-gradient;\r\n        \r\n        }\r\n    }\r\n}\r\n\r\n\r\n\r\n// Grid\r\n\r\n.grid-structure {\r\n    .grid-container {\r\n        background-color: $gray-100;\r\n        margin-top: 10px;\r\n        font-size: .8rem;\r\n        font-weight: $font-weight-medium;\r\n        padding: 10px 20px;\r\n    }\r\n}\r\n", "// \r\n// authentication.scss\r\n//\r\n\r\n// authentication home icon\r\n.home-btn {\r\n    position: absolute;\r\n    top: 15px;\r\n    right: 25px;\r\n}\r\n\r\n\r\n.checkmark {\r\n    width: 100px;\r\n    margin: 0 auto;\r\n    padding: 20px 0;\r\n  }\r\n  \r\n  .path {\r\n    stroke-dasharray: 1000;\r\n    stroke-dashoffset: 0;\r\n    animation: dash 2s ease-in-out;\r\n    -webkit-animation: dash 2s ease-in-out;\r\n  }\r\n  \r\n  .spin {\r\n    animation: spin 2s;\r\n    -webkit-animation: spin 2s;\r\n    transform-origin: 50% 50%;\r\n    -webkit-transform-origin: 50% 50%;\r\n  }\r\n  \r\n  \r\n@keyframes dash {\r\n0% {\r\n    stroke-dashoffset: 1000;\r\n}\r\n100% {\r\n    stroke-dashoffset: 0;\r\n}\r\n}\r\n\r\n@keyframes spin {\r\n0% {\r\n    -webkit-transform: rotate(0deg);\r\n}\r\n100% {\r\n    -webkit-transform: rotate(360deg);\r\n}\r\n}", "/* =============\r\n   Email\r\n============= */\r\n\r\n.inbox-leftbar {\r\n  width: 240px;\r\n  float: left;\r\n  padding: 0 20px 20px 10px;\r\n}\r\n.inbox-rightbar {\r\n  margin-left: 250px;\r\n}\r\n\r\n.message-list {\r\n  display: block;\r\n  padding-left: 0;\r\n\r\n  li {\r\n    position: relative;\r\n    display: block;\r\n    height: 50px;\r\n    line-height: 50px;\r\n    cursor: default;\r\n    transition-duration: .3s;\r\n\r\n    a{\r\n      color: $gray-700;\r\n    }\r\n\r\n    &:hover {\r\n      background: rgba($text-muted, 0.15);\r\n      transition-duration: .05s;\r\n    }\r\n\r\n    .col-mail {\r\n      float: left;\r\n      position: relative;\r\n    }\r\n\r\n    .col-mail-1 {\r\n      width: 320px;\r\n\r\n      .star-toggle,\r\n      .checkbox-wrapper-mail,\r\n      .dot {\r\n        display: block;\r\n        float: left;\r\n      }\r\n\r\n      .dot {\r\n        border: 4px solid transparent;\r\n        border-radius: 100px;\r\n        margin: 22px 26px 0;\r\n        height: 0;\r\n        width: 0;\r\n        line-height: 0;\r\n        font-size: 0;\r\n      }\r\n\r\n      .checkbox-wrapper-mail {\r\n        margin: 15px 10px 0 20px;\r\n      }\r\n\r\n      .star-toggle {\r\n        margin-top: 18px;\r\n        margin-left: 5px;\r\n      }\r\n\r\n      .title {\r\n        position: absolute;\r\n        top: 15px;\r\n        left: 110px;\r\n        right: 0;\r\n        text-overflow: ellipsis;\r\n        overflow: hidden;\r\n        white-space: nowrap;\r\n      }\r\n    }\r\n\r\n    .col-mail-2 {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 320px;\r\n      right: 0;\r\n      bottom: 0;\r\n\r\n      .subject,\r\n      .date {\r\n        position: absolute;\r\n        top: 0;\r\n      }\r\n\r\n      .subject {\r\n        left: 0;\r\n        right: 200px;\r\n        text-overflow: ellipsis;\r\n        overflow: hidden;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .date {\r\n        right: 0;\r\n        width: 170px;\r\n        padding-left: 80px;\r\n      }\r\n    }\r\n\r\n  }\r\n  li.active,li.selected {\r\n    background: rgba($text-muted, 0.15);\r\n    transition-duration: .05s;\r\n  }\r\n\r\n  li.active,\r\n  li.active:hover {\r\n    box-shadow: inset 3px 0 0 $primary;\r\n  }\r\n\r\n  li.unread a{\r\n    font-weight: 600;\r\n    color: darken($dark,5%);\r\n  }\r\n\r\n  li.blue-dot .col-mail-1 .dot {\r\n    border-color: $primary;\r\n  }\r\n\r\n  li.orange-dot .col-mail-1 .dot {\r\n    border-color: $warning;\r\n  }\r\n\r\n  li.green-dot .col-mail-1 .dot {\r\n    border-color: $success;\r\n  }\r\n\r\n  .checkbox-wrapper-mail {\r\n    cursor: pointer;\r\n    height: 20px;\r\n    width: 20px;\r\n    position: relative;\r\n    display: inline-block;\r\n    box-shadow: inset 0 0 0 1px $text-muted;\r\n    border-radius: 1px;\r\n\r\n    input {\r\n      opacity: 0;\r\n      cursor: pointer;\r\n    }\r\n    input:checked ~ label {\r\n      opacity: 1;\r\n    }\r\n\r\n    label {\r\n      position: absolute;\r\n      top: 3px;\r\n      left: 3px;\r\n      right: 3px;\r\n      bottom: 3px;\r\n      cursor: pointer;\r\n      background: $text-muted;\r\n      opacity: 0;\r\n      margin-bottom: 0 !important;\r\n      transition-duration: .05s;\r\n    }\r\n\r\n    label:active {\r\n      background: #87949b;\r\n    }\r\n  }\r\n\r\n}\r\n\r\n.mail-list {\r\n  a {\r\n    font-family: $font-family-secondary;\r\n    vertical-align: middle;\r\n    color: $gray-700;\r\n    padding: 10px 12px;\r\n    display: block;\r\n  }\r\n}\r\n\r\n\r\n@media (max-width: 648px) {\r\n  .inbox-leftbar {\r\n    width: 100%;\r\n  }\r\n  .inbox-rightbar {\r\n    margin-left: 0;\r\n  }\r\n}\r\n\r\n@media (max-width: 520px) {\r\n  .message-list li {\r\n    .col-mail-1 {\r\n      width: 150px;\r\n\r\n      .title {\r\n        left: 80px;\r\n      }\r\n    }\r\n    .col-mail-2 {\r\n      left: 160px;\r\n      .date {\r\n        text-align: right;\r\n        padding-right: 10px;\r\n        padding-left: 20px;\r\n      }\r\n    }\r\n  }\r\n}", "/* =============\r\n   Timeline\r\n============= */\r\n.timeline {\r\n  border-collapse: collapse;\r\n  border-spacing: 0;\r\n  display: table;\r\n  margin-bottom: 50px;\r\n  position: relative;\r\n  table-layout: fixed;\r\n  width: 100%;\r\n\r\n  .time-show {\r\n    margin-bottom: 30px;\r\n    margin-right: -75px;\r\n    margin-top: 30px;\r\n    position: relative;\r\n    a {\r\n      color: $white;\r\n    }\r\n  }\r\n  &:before {\r\n    background-color: rgba($gray-800, 0.3);\r\n    bottom: 0;\r\n    content: \"\";\r\n    left: 50%;\r\n    position: absolute;\r\n    top: 30px;\r\n    width: 2px;\r\n    z-index: 0;\r\n  }\r\n  .timeline-icon {\r\n    -webkit-border-radius: 50%;\r\n    background: $gray-800;\r\n    border-radius: 50%;\r\n    color: $white;\r\n    display: block;\r\n    height: 20px;\r\n    left: -54px;\r\n    margin-top: -10px;\r\n    position: absolute;\r\n    text-align: center;\r\n    top: 50%;\r\n    width: 20px;\r\n    i {\r\n      color: $white;\r\n      font-size: 13px;\r\n      margin-top: 1px;\r\n      position: absolute;\r\n      left: 4px;\r\n    }\r\n  }\r\n  .time-icon {\r\n    &:before {\r\n      font-size: 16px;\r\n      margin-top: 5px;\r\n    }\r\n  }\r\n\r\n}\r\n\r\nh3.timeline-title {\r\n  color: $gray-800;\r\n  font-size: 20px;\r\n  font-weight: 400;\r\n  margin: 0 0 5px;\r\n  text-transform: uppercase;\r\n}\r\n\r\n.timeline-item {\r\n  display: table-row;\r\n  &:before {\r\n    content: \"\";\r\n    display: block;\r\n    width: 50%;\r\n  }\r\n  .timeline-desk {\r\n    .arrow {\r\n      border-bottom: 12px solid transparent;\r\n      border-right: 12px solid rgba($light,0.3) !important;\r\n      border-top: 12px solid transparent;\r\n      display: block;\r\n      height: 0;\r\n      left: -12px;\r\n      margin-top: -12px;\r\n      position: absolute;\r\n      top: 50%;\r\n      width: 0;\r\n    }\r\n    .timeline-box {\r\n      padding: 20px;\r\n    }\r\n  }\r\n  .timeline-date {\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n\r\n.timeline-item.alt {\r\n  &:after {\r\n    content: \"\";\r\n    display: block;\r\n    width: 50%;\r\n  }\r\n  .timeline-desk {\r\n    .arrow-alt {\r\n      border-bottom: 12px solid transparent;\r\n      border-left: 12px solid rgba($light,0.9) !important;\r\n      border-top: 12px solid transparent;\r\n      display: block;\r\n      height: 0;\r\n      left: auto;\r\n      margin-top: -12px;\r\n      position: absolute;\r\n      right: -12px;\r\n      top: 50%;\r\n      width: 0;\r\n    }\r\n    .album {\r\n      float: right;\r\n      margin-top: 20px;\r\n      a {\r\n        float: right;\r\n        margin-left: 5px;\r\n      }\r\n    }\r\n  }\r\n  .timeline-icon {\r\n    left: auto;\r\n    right: -56px;\r\n  }\r\n  &:before {\r\n    display: none;\r\n  }\r\n  .panel {\r\n    margin-left: 0;\r\n    margin-right: 45px;\r\n  }\r\n  h4 {\r\n    text-align: right;\r\n  }\r\n  p {\r\n    text-align: right;\r\n  }\r\n  .timeline-date {\r\n    text-align: right;\r\n  }\r\n}\r\n\r\n\r\n.timeline-desk {\r\n  display: table-cell;\r\n  vertical-align: top;\r\n  width: 50%;\r\n  h4 {\r\n    font-size: 16px;\r\n    margin: 0;\r\n  }\r\n  .panel {\r\n    background: rgba($light,0.9);\r\n    display: block;\r\n    margin-bottom: 5px;\r\n    margin-left: 45px;\r\n    position: relative;\r\n    text-align: left;\r\n    border: 0;\r\n  }\r\n  h5 {\r\n    span {\r\n      color: $gray-800;\r\n      display: block;\r\n      font-size: 12px;\r\n      margin-bottom: 4px;\r\n    }\r\n  }\r\n  p {\r\n    color: #999999;\r\n    font-size: 14px;\r\n    margin-bottom: 0;\r\n  }\r\n  .album {\r\n    margin-top: 12px;\r\n    a {\r\n      float: left;\r\n      margin-right: 5px;\r\n    }\r\n\r\n    img {\r\n      height: 36px;\r\n      width: auto;\r\n      border-radius: 3px;\r\n    }\r\n  }\r\n  .notification {\r\n    background: none repeat scroll 0 0 $white;\r\n    margin-top: 20px;\r\n    padding: 8px;\r\n  }\r\n}\r\n", "/* =============\r\n   Account Pages\r\n============= */\r\n.home-wrapper {\r\n  margin: 10% 0;\r\n}\r\n\r\n.bg-accpunt-pages {\r\n  background-color: $primary;\r\n  background: $bg-gradient;\r\n  padding-bottom: 0;\r\n  min-height: 100px;\r\n}\r\n\r\n.wrapper-page {\r\n  display: table;\r\n  height: 100vh;\r\n  width: 100%;\r\n}\r\n\r\n.account-box {\r\n  position: relative;\r\n  max-width: 460px;\r\n  margin: 20px auto;\r\n  background-color: $white;\r\n  border-radius: 5px;\r\n\r\n  .account-content {\r\n    padding: 30px;\r\n  }\r\n\r\n  .account-btn {\r\n    position: absolute;\r\n    left: 0;\r\n    right: 0;\r\n  }\r\n}\r\n\r\n.account-logo-box {\r\n  padding: 30px 30px 0 30px;\r\n}\r\n\r\n.text-error {\r\n  color: $primary;\r\n  text-shadow: rgba($primary,0.3) 5px 1px, rgba($primary,0.2) 10px 3px;\r\n  font-size: 84px;\r\n  font-weight: 700;\r\n  line-height: 90px;\r\n}\r\n\r\n\r\n.checkmark {\r\n  width: 100px;\r\n  margin: 0 auto;\r\n  padding: 20px 0;\r\n}\r\n\r\n.path {\r\n  stroke-dasharray: 1000;\r\n  stroke-dashoffset: 0;\r\n  animation: dash 2s ease-in-out;\r\n  -webkit-animation: dash 2s ease-in-out;\r\n}\r\n\r\n.spin {\r\n  animation: spin 2s;\r\n  -webkit-animation: spin 2s;\r\n  transform-origin: 50% 50%;\r\n  -webkit-transform-origin: 50% 50%;\r\n}\r\n\r\n@-webkit-keyframes dash {\r\n  0% {\r\n    stroke-dashoffset: 1000;\r\n  }\r\n  100% {\r\n    stroke-dashoffset: 0;\r\n  }\r\n}\r\n\r\n@keyframes dash {\r\n  0% {\r\n    stroke-dashoffset: 1000;\r\n  }\r\n  100% {\r\n    stroke-dashoffset: 0;\r\n  }\r\n}\r\n\r\n@-webkit-keyframes spin {\r\n  0% {\r\n    -webkit-transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    -webkit-transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n@keyframes spin {\r\n  0% {\r\n    -webkit-transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    -webkit-transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n@-webkit-keyframes text {\r\n  0% {\r\n    opacity: 0;\r\n  }\r\n  100% {\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n@keyframes text {\r\n  0% {\r\n    opacity: 0; }\r\n  100% {\r\n    opacity: 1;\r\n  }\r\n}", "/* =============\r\n   Pricing\r\n============= */\r\n\r\n\r\n.pricing-column{\r\n  .ribbon-pricing {\r\n    width: 160px;\r\n    margin: -15px auto -10px;\r\n    padding-bottom: 2px;\r\n    line-height: 22px;\r\n    text-align: center;\r\n    z-index: 1;\r\n    position: relative;\r\n  }\r\n  .plan-title {\r\n    font-family: $font-family-secondary;\r\n    letter-spacing: 1px;\r\n  }\r\n  .plan-price {\r\n    font-size: 48px;\r\n    font-family: $font-family-secondary;\r\n  }\r\n  .plan-duration {\r\n    font-size: 15px;\r\n    color: rgba($white,0.7);\r\n  }\r\n\r\n  .plan-stats {\r\n    padding: 30px 20px 15px;\r\n    li {\r\n      margin-bottom: 15px;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n}\r\n\r\n", "// \r\n// general-rtl.scss\r\n//\r\n\r\nhtml {\r\n    direction: rtl;\r\n}\r\n\r\nbody {\r\n    text-align: right;\r\n}", "// \r\n// bootstrap-rtl.scss\r\n//\r\n\r\n// Dropdowns\r\n\r\n.dropdown-menu {\r\n    &.show {\r\n        text-align: right;\r\n        left: auto !important;\r\n        right: 0;\r\n        bottom: auto;\r\n    }\r\n}\r\n\r\n.dropdown-menu-right {\r\n    right: auto !important;\r\n    left: 0 !important;\r\n    &.show {\r\n        left: 0 !important;\r\n    }\r\n}\r\n\r\n// Buttons\r\n\r\n.btn-group, .btn-group-vertical {\r\n    direction: ltr;\r\n}\r\n\r\n\r\n// List\r\n\r\nul {\r\n    padding-right: 0;\r\n}\r\n\r\n\r\n// pagination\r\n\r\n.pagination{\r\n    .page-item {\r\n        &:first-child {\r\n            .page-link {\r\n                margin-right: 0;//rtl\r\n                border-top-left-radius: 0px;\r\n                border-bottom-left-radius: 0px;\r\n                @include border-right-radius($border-radius);//rtl\r\n            }\r\n        }\r\n        &:last-child {\r\n            .page-link {\r\n                border-top-right-radius: 0px;\r\n                border-bottom-right-radius: 0px;\r\n                @include border-left-radius($border-radius);//rtl\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// blockquote\r\n\r\n.blockquote-reverse{\r\n    text-align: left !important;\r\n}\r\n\r\n// dl\r\n\r\ndd {\r\n    margin-right: 0;\r\n}\r\n\r\n// Modal\r\n\r\n.modal-header {\r\n    .close {\r\n        margin: (-$modal-header-padding-y) auto (-$modal-header-padding-x) (-$modal-header-padding-y);\r\n        left: 0px;\r\n    }\r\n}\r\n\r\n.modal-footer {\r\n    > :not(:first-child) {\r\n        margin-right: .25rem;\r\n        margin-left: 0;\r\n    }\r\n\r\n    > :not(:last-child) {\r\n        margin-left: .25rem;\r\n        margin-right: 0;\r\n    }\r\n}\r\n\r\n\r\n// Alerts\r\n\r\n.alert-dismissible {\r\n    padding-left: $close-font-size + $alert-padding-x * 2;\r\n    padding-right: $alert-padding-x;\r\n\r\n    .close {\r\n        left: 0;\r\n        right: auto;\r\n    }\r\n}\r\n\r\n\r\n// Breadcrumb item arrow\r\n\r\n.breadcrumb-item {\r\n    +.breadcrumb-item {\r\n        padding-right: $breadcrumb-item-padding;\r\n        padding-left: 0px;\r\n        &::before {\r\n            padding-left: $breadcrumb-item-padding;\r\n            content: \"\\F141\";\r\n            padding-right: 0px;\r\n        }\r\n    }\r\n}\r\n\r\n// Custom Checkbox-Radio \r\n\r\n.form-check-inline{\r\n    margin-left: .75rem;\r\n    margin-right: 0;\r\n}\r\n\r\n.custom-control {\r\n    padding-right: $custom-control-gutter + $custom-control-indicator-size;\r\n    padding-left: 0;\r\n}\r\n\r\n.custom-control-label {\r\n    &::before {\r\n        left: auto;\r\n        right: -($custom-control-gutter + $custom-control-indicator-size);\r\n    }\r\n\r\n    // Foreground (icon)\r\n    &::after {\r\n        left: auto;\r\n        right: -($custom-control-gutter + $custom-control-indicator-size);\r\n    }\r\n}\r\n\r\n.custom-switch {\r\n    padding-right: $custom-switch-width + $custom-control-gutter;\r\n    padding-left: 0;\r\n\r\n    .custom-control-label {\r\n        &::before {\r\n            right: -($custom-switch-width + $custom-control-gutter);\r\n            left: auto;\r\n        }\r\n\r\n        &::after {\r\n            right: calc(#{-($custom-switch-width + $custom-control-gutter)} + #{$custom-control-indicator-border-width * 2});\r\n            left: auto;\r\n        }\r\n    }\r\n\r\n    .custom-control-input:checked~.custom-control-label {\r\n        &::after {\r\n            transform: translateX(#{-($custom-switch-width - $custom-control-indicator-size)});\r\n        }\r\n    }\r\n}\r\n\r\n.custom-file-label {\r\n    &::after {\r\n        right: auto;\r\n        left: 0;\r\n        border-right: inherit;\r\n    }\r\n}\r\n\r\n\r\n\r\n// Input Group\r\n\r\n.input-group-prepend {\r\n    margin-left: -1px;\r\n    margin-right: 0;\r\n}\r\n\r\n.input-group-append {\r\n    margin-right: -1px;\r\n    margin-left: 0;\r\n}\r\n\r\n.input-group>.input-group-prepend>.btn,\r\n.input-group>.input-group-prepend>.input-group-text,\r\n.input-group>.input-group-append:not(:last-child)>.btn,\r\n.input-group>.input-group-append:not(:last-child)>.input-group-text,\r\n.input-group>.input-group-append:last-child>.btn:not(:last-child):not(.dropdown-toggle),\r\n.input-group>.input-group-append:last-child>.input-group-text:not(:last-child),\r\n.input-group>.custom-select:not(:last-child),\r\n.input-group>.form-control:not(:last-child) {\r\n    border-top-right-radius: $input-border-radius;\r\n    border-bottom-right-radius: $input-border-radius;\r\n    border-top-left-radius: 0;\r\n    border-bottom-left-radius: 0;\r\n}\r\n\r\n.input-group>.input-group-append>.btn,\r\n.input-group>.input-group-append>.input-group-text,\r\n.input-group>.input-group-prepend:not(:first-child)>.btn,\r\n.input-group>.input-group-prepend:not(:first-child)>.input-group-text,\r\n.input-group>.input-group-prepend:first-child>.btn:not(:first-child),\r\n.input-group>.input-group-prepend:first-child>.input-group-text:not(:first-child),\r\n.input-group>.custom-select:not(:first-child),\r\n.input-group>.form-control:not(:first-child) {\r\n    border-top-left-radius: $input-border-radius;\r\n    border-bottom-left-radius: $input-border-radius;\r\n    border-top-right-radius: 0;\r\n    border-bottom-right-radius: 0;\r\n}", "// stylelint-disable property-blacklist\n// Single side border-radius\n\n@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {\n  @if $enable-rounded {\n    border-radius: $radius;\n  }\n  @else if $fallback-border-radius != false {\n    border-radius: $fallback-border-radius;\n  }\n}\n\n@mixin border-top-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: $radius;\n    border-top-right-radius: $radius;\n  }\n}\n\n@mixin border-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: $radius;\n    border-bottom-right-radius: $radius;\n  }\n}\n\n@mixin border-bottom-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: $radius;\n    border-bottom-left-radius: $radius;\n  }\n}\n\n@mixin border-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: $radius;\n    border-bottom-left-radius: $radius;\n  }\n}\n\n@mixin border-top-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: $radius;\n  }\n}\n\n@mixin border-top-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: $radius;\n  }\n}\n\n@mixin border-bottom-right-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: $radius;\n  }\n}\n\n@mixin border-bottom-left-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-left-radius: $radius;\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n// <PERSON><PERSON> and Padding\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    @each $prop, $abbrev in (margin: m, padding: p) {\n      @each $size, $length in $spacers {\n        .#{$abbrev}#{$infix}-#{$size} { #{$prop}: $length !important; }\n        .#{$abbrev}t#{$infix}-#{$size},\n        .#{$abbrev}y#{$infix}-#{$size} {\n          #{$prop}-top: $length !important;\n        }\n        .#{$abbrev}r#{$infix}-#{$size},\n        .#{$abbrev}x#{$infix}-#{$size} {\n          #{$prop}-left: $length !important;\n          #{$prop}-right: 0 !important;\n        }\n        .#{$abbrev}b#{$infix}-#{$size},\n        .#{$abbrev}y#{$infix}-#{$size} {\n          #{$prop}-bottom: $length !important;\n        }\n        .#{$abbrev}l#{$infix}-#{$size},\n        .#{$abbrev}x#{$infix}-#{$size} {\n          #{$prop}-right: $length !important;\n          #{$prop}-left: 0 !important;\n        }\n      }\n    }\n\n    // Negative margins (e.g., where `.mb-n1` is negative version of `.mb-1`)\n    @each $size, $length in $spacers {\n      @if $size != 0 {\n        .m#{$infix}-n#{$size} { margin: -$length !important; }\n        .mt#{$infix}-n#{$size},\n        .my#{$infix}-n#{$size} {\n          margin-top: -$length !important;\n        }\n        .mr#{$infix}-n#{$size},\n        .mx#{$infix}-n#{$size} {\n          margin-right: -$length !important;\n        }\n        .mb#{$infix}-n#{$size},\n        .my#{$infix}-n#{$size} {\n          margin-bottom: -$length !important;\n        }\n        .ml#{$infix}-n#{$size},\n        .mx#{$infix}-n#{$size} {\n          margin-left: -$length !important;\n        }\n      }\n    }\n\n    // Some special margin utils\n    .m#{$infix}-auto { margin: auto !important; }\n    .mt#{$infix}-auto,\n    .my#{$infix}-auto {\n      margin-top: auto !important;\n    }\n    .mr#{$infix}-auto,\n    .mx#{$infix}-auto {\n      margin-left: auto !important;\n      margin-right: inherit !important;\n    }\n    .mb#{$infix}-auto,\n    .my#{$infix}-auto {\n      margin-bottom: auto !important;\n    }\n    .ml#{$infix}-auto,\n    .mx#{$infix}-auto {\n      margin-right: auto !important;\n      margin-left: auto !important;\n    }\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .float#{$infix}-left  { float: right !important; }\n    .float#{$infix}-right { float: left !important; }\n    .float#{$infix}-none  { float: none !important; }\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n//\n// Text\n//\n\n.text-monospace { font-family: $font-family-monospace !important; }\n\n// Alignment\n\n.text-justify  { text-align: justify !important; }\n.text-wrap     { white-space: normal !important; }\n.text-nowrap   { white-space: nowrap !important; }\n.text-truncate { @include text-truncate; }\n\n// Responsive alignment\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .text#{$infix}-left   { text-align: right !important; }\n    .text#{$infix}-right  { text-align: left !important; }\n    .text#{$infix}-center { text-align: center !important; }\n  }\n}\n\n// Transformation\n\n.text-lowercase  { text-transform: lowercase !important; }\n.text-uppercase  { text-transform: uppercase !important; }\n.text-capitalize { text-transform: capitalize !important; }\n\n// Weight and italics\n\n.font-weight-light   { font-weight: $font-weight-light !important; }\n.font-weight-lighter { font-weight: $font-weight-lighter !important; }\n.font-weight-normal  { font-weight: $font-weight-normal !important; }\n.font-weight-bold    { font-weight: $font-weight-bold !important; }\n.font-weight-bolder  { font-weight: $font-weight-bolder !important; }\n.font-italic         { font-style: italic !important; }\n\n// Contextual colors\n\n.text-white { color: $white !important; }\n\n@each $color, $value in $theme-colors {\n  @include text-emphasis-variant(\".text-#{$color}\", $value);\n}\n\n.text-body { color: $body-color !important; }\n.text-muted { color: $text-muted !important; }\n\n.text-black-50 { color: rgba($black, .5) !important; }\n.text-white-50 { color: rgba($white, .5) !important; }\n\n// Misc\n\n.text-hide {\n  @include text-hide($ignore-warning: true);\n}\n\n.text-decoration-none { text-decoration: none !important; }\n\n.text-break {\n  word-break: break-word !important; // IE & < Edge 18\n  overflow-wrap: break-word !important;\n}\n\n// Reset\n\n.text-reset { color: inherit !important; }\n", "// Text truncate\n// Requires inline-block or block for proper styling\n\n@mixin text-truncate() {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n", "// stylelint-disable declaration-no-important\n\n// Typography\n\n@mixin text-emphasis-variant($parent, $color) {\n  #{$parent} {\n    color: $color !important;\n  }\n  @if $emphasized-link-hover-darken-percentage != 0 {\n    a#{$parent} {\n      @include hover-focus {\n        color: darken($color, $emphasized-link-hover-darken-percentage) !important;\n      }\n    }\n  }\n}\n", "// Hover mixin and `$enable-hover-media-query` are deprecated.\n//\n// Originally added during our alphas and maintained during betas, this mixin was\n// designed to prevent `:hover` stickiness on iOS-an issue where hover styles\n// would persist after initial touch.\n//\n// For backward compatibility, we've kept these mixins and updated them to\n// always return their regular pseudo-classes instead of a shimmed media query.\n//\n// Issue: https://github.com/twbs/bootstrap/issues/25195\n\n@mixin hover {\n  &:hover { @content; }\n}\n\n@mixin hover-focus {\n  &:hover,\n  &:focus {\n    @content;\n  }\n}\n\n@mixin plain-hover-focus {\n  &,\n  &:hover,\n  &:focus {\n    @content;\n  }\n}\n\n@mixin hover-focus-active {\n  &:hover,\n  &:focus,\n  &:active {\n    @content;\n  }\n}\n", "// CSS image replacement\n@mixin text-hide($ignore-warning: false) {\n  // stylelint-disable-next-line font-family-no-missing-generic-family-keyword\n  font: 0/0 a;\n  color: transparent;\n  text-shadow: none;\n  background-color: transparent;\n  border: 0;\n\n  @include deprecate(\"`text-hide()`\", \"v4.1.0\", \"v5\", $ignore-warning);\n}\n", "// \r\n// structure-rtl.scss\r\n//\r\n\r\n// topbar.scss\r\n\r\n\r\n.logo-box {\r\n    float: right;\r\n}\r\n\r\n\r\n\r\n.navbar-custom {\r\n    padding: 0 0 0 10px;\r\n    .app-search{\r\n        margin-left: 20px;\r\n        margin-right: 0px;\r\n        .input-group-append {\r\n            margin-right: 0;\r\n        }\r\n        .btn {\r\n            border-radius: 30px 0 0 30px!important;\r\n        }\r\n        .form-control{\r\n            padding-right: 20px;\r\n            padding-left: 0;\r\n            border-radius: 0 30px 30px 0!important;\r\n        }\r\n    }\r\n    .topnav-menu {\r\n        >li {\r\n            float: right;\r\n        }\r\n\r\n        .nav-link {\r\n            direction: ltr;\r\n        }\r\n    }\r\n\r\n}\r\n\r\n\r\n\r\n\r\n/* Notification */\r\n.notification-list {\r\n\r\n    .noti-icon-badge {\r\n        left: 12px;\r\n        right: auto;\r\n    }\r\n\r\n    .notify-item {\r\n        padding: 12px 20px;\r\n\r\n        .notify-icon {\r\n            float: right;\r\n            margin-left: 10px;\r\n            margin-right: 0;\r\n        }\r\n\r\n        .notify-details,\r\n        .user-msg {\r\n            margin-left: 0;\r\n            margin-right: 45px;\r\n        }\r\n    }\r\n\r\n    .pro-user-name{\r\n        margin-left: .375rem!important;\r\n        margin-right: 0 !important;\r\n    }\r\n\r\n    .profile-dropdown {\r\n        i {\r\n            margin-left: 5px;\r\n            margin-right: 0px;\r\n            float: right;\r\n        }\r\n        .notify-item {\r\n            padding: 7px 20px;\r\n        }\r\n    }\r\n}\r\n\r\n// page-title\r\n\r\n.page-title-box {\r\n    .page-title-right {\r\n        float: left;\r\n    }\r\n}\r\n\r\n\r\n// Left-sidebar\r\n\r\n.content-page {\r\n    margin-right: $leftbar-width;\r\n    margin-left: 0;\r\n}\r\n\r\n// Sidebar\r\n#sidebar-menu {\r\n    >ul {\r\n        >li {\r\n            >a {\r\n                i {\r\n                    margin: 0 3px 0 10px;\r\n                }\r\n\r\n                .drop-arrow {\r\n                    float: left;\r\n\r\n                    i {\r\n                        margin-left: 0;\r\n                    }\r\n                }\r\n            }\r\n\r\n            >ul {\r\n                padding-right: 40px;\r\n                padding-left: 0;\r\n\r\n                ul {\r\n                    padding-right: 20px;\r\n                    padding-left: 0;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .menu-arrow {\r\n        left: 20px;\r\n        right: auto;\r\n\r\n        &:before {\r\n            content: \"\\F141\";\r\n        }\r\n    }\r\n\r\n    li.mm-active {\r\n        >a {\r\n            >span.menu-arrow {\r\n                transform: rotate(-90deg);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// Enlarge menu\r\n.enlarged {\r\n\r\n    // Side menu\r\n    .left-side-menu {\r\n\r\n        // Sidebar Menu\r\n        #sidebar-menu {\r\n\r\n            >ul {\r\n                >li {\r\n\r\n                    >a {\r\n                        i {\r\n                            margin-left: 20px;\r\n                            margin-right: 5px;\r\n                        }\r\n\r\n                        span {\r\n                            padding-right: 25px;\r\n                            padding-left: 0;\r\n                        }\r\n                    }\r\n\r\n                    &:hover {\r\n\r\n                        >ul {\r\n                            right: $leftbar-width-collapsed;\r\n                            left: auto;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                ul {\r\n\r\n                    li {\r\n                        &:hover {\r\n                            >ul {\r\n                                right: 190px;\r\n                                margin-top: -36px;\r\n                            }\r\n                        }\r\n\r\n                        >a {\r\n                            span.pull-right {\r\n                                left: 20px;\r\n                                right: 0;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // Navbar\r\n\r\n    .navbar-custom{\r\n        right: 0px;\r\n    }\r\n\r\n    // Content Page\r\n    .content-page {\r\n        margin-right: $leftbar-width-collapsed !important;\r\n        margin-left: 0 !important;\r\n    }\r\n\r\n    //Footer\r\n    .footer {\r\n        left: 0 !important;\r\n        right: $leftbar-width-collapsed !important;\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(sm) {\r\n\r\n    .content-page,\r\n    .enlarged .content-page {\r\n        margin-right: 0 !important;\r\n    }\r\n}\r\n\r\n\r\n// footer.scss\r\n\r\n.footer {\r\n    left: 0;\r\n    right: $leftbar-width;\r\n}\r\n\r\n.footer-alt {\r\n    right: 0 !important;\r\n}\r\n\r\n@include media-breakpoint-down(sm) {\r\n    .footer {\r\n        right: 0 !important;\r\n    }\r\n}\r\n\r\n// right-sidebar.scss\r\n//\r\n\r\n.right-bar {\r\n    float: left !important;\r\n    left: -($rightbar-width + 10px);\r\n    right: auto;\r\n\r\n    .user-box {\r\n        .user-img {\r\n            .user-edit {\r\n                right: 0;\r\n                left: -5px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.right-bar-enabled {\r\n    .right-bar {\r\n        left: 0;\r\n        right: auto;\r\n    }\r\n}", "// \r\n// plugins-rtl.scss\r\n//\r\n\r\n\r\n\r\n\r\n// FLOT CHART\r\n\r\n\r\n.legendLabel {\r\n    padding-right: 5px!important;\r\n    padding-left: 20px;\r\n}\r\n\r\n// Select 2\r\n\r\n.select2-container {\r\n    .select2-selection--single {\r\n        .select2-selection__rendered {\r\n            padding-right: 12px;\r\n        }\r\n\r\n        .select2-selection__arrow {\r\n            left: 3px;\r\n            right: auto;\r\n        }\r\n    }\r\n\r\n    .select2-selection--multiple {\r\n        .select2-selection__choice {\r\n            float: right;\r\n            margin-left: 5px;\r\n            margin-right: 0;\r\n        }\r\n    }\r\n\r\n    .select2-search--inline {\r\n        float: right;\r\n    }\r\n}\r\n\r\n\r\n// Bootstrap select\r\n\r\n.bootstrap-select {\r\n    .dropdown-toggle {\r\n        &:before {\r\n            float: left;\r\n        }\r\n\r\n        .filter-option {\r\n            text-align: right;\r\n        }\r\n\r\n        .filter-option-inner {\r\n            padding-right: 0;\r\n            padding-left: inherit;\r\n        }\r\n    }\r\n}\r\n\r\n// wizard\r\n\r\n.wizard{\r\n    >.steps {\r\n        .number {\r\n            margin-left: 10px;\r\n        }\r\n    }\r\n}\r\n\r\n// X-ediatable \r\n\r\n.editable-buttons {\r\n    margin-left: 0;\r\n    margin-right: 7px;\r\n\r\n    .editable-cancel {\r\n        margin-left: 0;\r\n        margin-right: 7px;\r\n    }\r\n}\r\n\r\n// datatable\r\n\r\n.dataTables_wrapper {\r\n    .dataTables_filter{\r\n        text-align: left !important;\r\n        input{\r\n            margin-left: 0px !important;\r\n            margin-right: 0.5em;\r\n        }\r\n    }\r\n}\r\n\r\n// tablesaw\r\n\r\n.tablesaw-columntoggle-popup {\r\n    .tablesaw-btn-group {\r\n        > label {\r\n            input{\r\n                margin-right: 0;\r\n                margin-left: .8em;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.tablesaw-bar {\r\n    .tablesaw-bar-section {\r\n        .tablesaw-btn{\r\n            margin-left: 0;\r\n            margin-right: .4em;\r\n        }\r\n    }\r\n}\r\n\r\n// Responsive Table\r\n\r\n.table-rep-plugin {\r\n    .btn-group.pull-right {\r\n        float: left;\r\n    }\r\n    .checkbox-row {\r\n        label{\r\n            &:after{\r\n                margin-left: -22px;\r\n                top: -2px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// Quilljs\r\n\r\n.ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg {\r\n    left: 0;\r\n    right: auto;\r\n}\r\n\r\n.ql-editor {\r\n    direction: rtl;\r\n    text-align: right;\r\n}\r\n\r\n\r\n", "//\n// components-rtl.scss\n//\n\n\n// Inbox-widget\n\n.inbox-widget {\n  .inbox-item {\n    .inbox-item-img {\n      float: right;\n      margin-left: 15px;\n      margin-right: 0;\n    }\n\n    .inbox-item-date {\n      right: auto;\n      left: 5px;\n    }\n  }\n}\n\n// Dashboard-widgets\n\n// LATEST COMMENTS\n\n.comment-list {\n  .comment-box-item {\n    .commnet-item-date {\n      left: 7px;\n      right: auto;\n    }\n  }\n}\n\n\n// LAST TRANSACTIONS\n\n.transaction-list{\n  .tran-text {\n    padding-right: 25px;\n   \n  }\n  .tran-price {\n    margin-right: 30px;\n    margin-left: 0px;\n  }\n\n  \n}\n\n\n// icons\n\n.icons-list-demo{\n  i{\n    margin-left: 12px;\n    margin-right: 0;\n  }\n}\n\n// Custom-radio\n\n.checkbox {\n  label {\n    padding-right: 8px;\n    padding-left: 0;\n\n    &::before {\n      left: auto;\n      right: 0;\n      margin-left: 0;\n      margin-right: -18px;\n    }\n\n    &::after {\n      left: auto;\n      right: 0;\n      margin-right: -18px;\n      margin-left: 0;\n      padding-left: 0;\n      padding-right: 3px;\n    }\n  }\n\n  input[type=\"checkbox\"]:checked+label {\n    &::after {\n      left: auto;\n      right: 7px;\n      transform: rotate(45deg);\n    }\n  }\n}\n\n.checkbox.checkbox-single {\n  label {\n    &:before {\n      margin-right: 0;\n    }\n\n    &:after {\n      margin-right: 0;\n    }\n  }\n}\n\n\n// custom-radio\n\n.radio {\n  label {\n    padding-left: 0;\n    padding-right: 8px;\n\n    &::before {\n      left: auto;\n      right: 0;\n      margin-left: 0;\n      margin-right: -18px;\n    }\n\n    &::after {\n      left: 0;\n      right: 6px;\n      margin-left: 0;\n      margin-right: -20px;\n    }\n  }\n}\n\n// Invoice\n\n@media print {\n  .content-page,\n  .content,\n  body {\n      margin-right: 0;\n  }\n}\n\n", "// \r\n// pages-rtl.scss\r\n//\r\n// Email\r\n.inbox-leftbar {\r\n    float: right;\r\n    padding: 0 10px 20px 20px;\r\n}\r\n\r\n.inbox-rightbar {\r\n    margin-right: 250px;\r\n    margin-left: 0px;\r\n}\r\n\r\n.message-list {\r\n    padding-right: 0;\r\n    .col-mail {\r\n        float: right !important;\r\n    }\r\n    .col-mail-1 {\r\n        .star-toggle,\r\n        .checkbox-wrapper-mail,\r\n        .dot {\r\n            float: right !important;\r\n        }\r\n        .dot {\r\n            margin: 22px 0px 0 26px;\r\n        }\r\n        .checkbox-wrapper-mail {\r\n            margin: 15px 20px 0 10px !important;\r\n        }\r\n        .star-toggle {\r\n            margin-right: 5px;\r\n        }\r\n        .title {\r\n            right: 110px !important;\r\n            left: 0 !important;\r\n        }\r\n    }\r\n    .col-mail-2 {\r\n        right: 320px !important;\r\n        left: 0 !important;\r\n        .subject {\r\n            right: 0 !important;\r\n            left: 200px !important;\r\n        }\r\n        .date {\r\n            left: 0;\r\n            padding-right: 80px;\r\n            padding-left: 0px !important;\r\n            right: auto !important;\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 648px) {\r\n    .inbox-rightbar {\r\n        margin-right: 0;\r\n    }\r\n}\r\n\r\n@media (max-width: 520px) {\r\n    .message-list li {\r\n        .col-mail-1 {\r\n            .title {\r\n                right: 80px !important;\r\n                left: 0px !important;\r\n            }\r\n        }\r\n        .col-mail-2 {\r\n            right: 160px !important;\r\n            .date {\r\n                text-align: left;\r\n                padding-left: 10px !important;\r\n                padding-right: 20px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// Timelime\r\n\r\n/* =============\r\n   Timeline\r\n============= */\r\n\r\n.timeline {\r\n    .time-show {\r\n        margin-left: -75px;\r\n    }\r\n    &:before {\r\n        right: 50%;\r\n    }\r\n    .timeline-icon {\r\n        right: -54px;\r\n        i {\r\n            right: 4px;\r\n        }\r\n    }\r\n}\r\n\r\nh3.timeline-title {\r\n    margin: 0 0 5px;\r\n}\r\n\r\n.timeline-item {\r\n    .timeline-desk {\r\n        .arrow {\r\n            border-left: 12px solid rgba($light, 0.3) !important;\r\n            right: -12px;\r\n        }\r\n    }\r\n}\r\n\r\n.timeline-item.alt {\r\n    .timeline-desk {\r\n        .arrow-alt {\r\n            border-right: 12px solid rgba($light, 0.9) !important;\r\n            right: auto;\r\n            left: -12px;\r\n            border-left: none !important;\r\n        }\r\n        .album {\r\n            float: left;\r\n            a {\r\n                float: left;\r\n                margin-right: 5px;\r\n            }\r\n        }\r\n    }\r\n    .timeline-icon {\r\n        right: auto;\r\n        left: -56px;\r\n    }\r\n    .panel {\r\n        margin-right: 0;\r\n        margin-left: 45px;\r\n    }\r\n    h4 {\r\n        text-align: left;\r\n    }\r\n    p {\r\n        text-align: left;\r\n    }\r\n    .timeline-date {\r\n        text-align: left;\r\n    }\r\n}\r\n\r\n.timeline-desk {\r\n    .panel {\r\n        margin-right: 45px;\r\n        text-align: right;\r\n        margin-left: 0px;\r\n    }\r\n    .album {\r\n        a {\r\n            float: right;\r\n            margin-left: 5px;\r\n        }\r\n    }\r\n}\r\n\r\n// authentication\r\n.home-btn {\r\n    position: absolute;\r\n    left: 25px;\r\n    right: auto;\r\n}"]}