@extends('layouts.master')

@section('content')
    <div class="row">
        <div class="col-lg-8">
            <div class="card-box">
                <h4 class="header-title">Edit Book</h4>
                <p class="sub-header">Update book information and PDF file.</p>

                <form method="POST" class="parsley-examples" action="{{ route('books.update', $book->id) }}"
                    enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    <div class="form-group">
                        <label for="title">Book Title<span class="text-danger">*</span></label>
                        <input type="text" name="title" parsley-trigger="change" required
                            placeholder="Enter book title" class="form-control" id="title" value="{{ $book->title }}">
                    </div>

                    <div class="form-group">
                        <label for="author">Author</label>
                        <input type="text" name="author" parsley-trigger="change" placeholder="Enter author name"
                            class="form-control" id="author" value="{{ $book->author }}">
                    </div>

                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea name="description" class="form-control" id="description" rows="4" placeholder="Enter book description">{{ $book->description }}</textarea>
                    </div>

                    <div class="form-group">
                        <label for="pdf_file">PDF File</label>
                        <input type="file" name="pdf_file" class="form-control filestyle" id="pdf_file"
                            data-btnClass="btn-primary" accept=".pdf">
                        <small class="text-muted">Current file: {{ basename($book->file_path) }}</small>
                    </div>

                    <div class="form-group">
                        <div class="checkbox checkbox-purple">
                            <input id="isactive" name="isactive" type="checkbox" value="1"
                                {{ $book->isactive ? 'checked' : '' }}>
                            <label for="isactive">
                                Is Active
                            </label>
                        </div>
                    </div>

                    <div class="form-group text-right mb-0">
                        <button class="btn btn-primary waves-effect waves-light" type="submit">
                            <i class="fas fa-save mr-1"></i> Update Book
                        </button>
                        <button type="reset" onclick="window.history.go(-1);" class="btn btn-light waves-effect ml-1">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            $("#pdf_file").filestyle({
                htmlIcon: '<span class="fas fa-folder-open mr-1"></span>'
            });
        });
    </script>
@endsection
