<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Relations\HasMany;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    /**
     * Get all audits performed by this user
     */
    public function audits(): HasMany
    {
        return $this->hasMany(Audit::class)->orderBy('created_at', 'desc');
    }

    /**
     * Get all activities performed by this user
     */
    public function activities(): HasMany
    {
        return $this->hasMany(UserActivity::class)->orderBy('created_at', 'desc');
    }

    /**
     * Get recent activities for this user
     */
    public function recentActivities(int $limit = 10): HasMany
    {
        return $this->activities()->limit($limit);
    }

    /**
     * Get the user's full name for audit identifier
     */
    public function getAuditIdentifier(): string
    {
        return $this->name;
    }
}
