<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'username',
        'first_name',
        'last_name',
        'email',
        'phone',
        'address',
        'avatar',
        'timezone',
        'language',
        'password',
        'is_active',
        'is_verified',
        'last_login_at',
        'last_login_ip',
        'password_changed_at',
        'force_password_change',
        'preferences',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'is_active' => 'boolean',
        'is_verified' => 'boolean',
        'last_login_at' => 'datetime',
        'password_changed_at' => 'datetime',
        'force_password_change' => 'boolean',
        'preferences' => 'array',
    ];

    /**
     * Get all audits performed by this user
     */
    public function audits(): HasMany
    {
        return $this->hasMany(Audit::class)->orderBy('created_at', 'desc');
    }

    /**
     * Get all activities performed by this user
     */
    public function activities(): HasMany
    {
        return $this->hasMany(UserActivity::class)->orderBy('created_at', 'desc');
    }

    /**
     * Get recent activities for this user
     */
    public function recentActivities(int $limit = 10): HasMany
    {
        return $this->activities()->limit($limit);
    }

    /**
     * Get the user's full name for audit identifier
     */
    public function getAuditIdentifier(): string
    {
        return $this->name;
    }

    /**
     * Get the roles for the user
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'user_roles')
            ->withPivot(['assigned_at', 'assigned_by', 'expires_at', 'is_active'])
            ->withTimestamps();
    }

    /**
     * Get active roles for the user
     */
    public function activeRoles(): BelongsToMany
    {
        return $this->roles()->wherePivot('is_active', true)
            ->where(function ($query) {
                $query->whereNull('user_roles.expires_at')
                    ->orWhere('user_roles.expires_at', '>', now());
            });
    }

    /**
     * Get user role assignments
     */
    public function userRoles(): HasMany
    {
        return $this->hasMany(UserRole::class);
    }

    /**
     * Get the user who created this user
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this user
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get users created by this user
     */
    public function createdUsers(): HasMany
    {
        return $this->hasMany(User::class, 'created_by');
    }

    /**
     * Get users updated by this user
     */
    public function updatedUsers(): HasMany
    {
        return $this->hasMany(User::class, 'updated_by');
    }

    /**
     * Check if user has a specific role
     */
    public function hasRole(string $role): bool
    {
        return $this->activeRoles()->where('name', $role)->exists();
    }

    /**
     * Check if user has any of the given roles
     */
    public function hasAnyRole(array $roles): bool
    {
        return $this->activeRoles()->whereIn('name', $roles)->exists();
    }

    /**
     * Check if user has all of the given roles
     */
    public function hasAllRoles(array $roles): bool
    {
        $userRoles = $this->activeRoles()->pluck('name')->toArray();
        return count(array_intersect($roles, $userRoles)) === count($roles);
    }

    /**
     * Check if user has a specific permission
     */
    public function hasPermission(string $permission): bool
    {
        return $this->activeRoles()
            ->whereHas('permissions', function ($query) use ($permission) {
                $query->where('name', $permission)->where('is_active', true);
            })->exists();
    }

    /**
     * Check if user has any of the given permissions
     */
    public function hasAnyPermission(array $permissions): bool
    {
        return $this->activeRoles()
            ->whereHas('permissions', function ($query) use ($permissions) {
                $query->whereIn('name', $permissions)->where('is_active', true);
            })->exists();
    }

    /**
     * Check if user has all of the given permissions
     */
    public function hasAllPermissions(array $permissions): bool
    {
        foreach ($permissions as $permission) {
            if (!$this->hasPermission($permission)) {
                return false;
            }
        }
        return true;
    }

    /**
     * Get all permissions for the user through roles
     */
    public function getAllPermissions()
    {
        return Permission::whereHas('roles.users', function ($query) {
            $query->where('users.id', $this->id)
                ->where('user_roles.is_active', true)
                ->where(function ($q) {
                    $q->whereNull('user_roles.expires_at')
                        ->orWhere('user_roles.expires_at', '>', now());
                });
        })->where('is_active', true)->get();
    }

    /**
     * Assign role to user
     */
    public function assignRole(Role|string $role, ?User $assignedBy = null, ?\DateTime $expiresAt = null): self
    {
        if (is_string($role)) {
            $role = Role::where('name', $role)->firstOrFail();
        }

        if (!$this->hasRole($role->name)) {
            $this->roles()->attach($role, [
                'assigned_at' => now(),
                'assigned_by' => $assignedBy?->id,
                'expires_at' => $expiresAt,
                'is_active' => true,
            ]);
        }

        return $this;
    }

    /**
     * Remove role from user
     */
    public function removeRole(Role|string $role): self
    {
        if (is_string($role)) {
            $role = Role::where('name', $role)->firstOrFail();
        }

        $this->roles()->detach($role);

        return $this;
    }

    /**
     * Sync roles for user
     */
    public function syncRoles(array $roles, ?User $assignedBy = null): self
    {
        $roleData = [];

        foreach ($roles as $role) {
            if (is_string($role)) {
                $role = Role::where('name', $role)->firstOrFail();
            }

            $roleData[$role->id] = [
                'assigned_at' => now(),
                'assigned_by' => $assignedBy?->id,
                'is_active' => true,
            ];
        }

        $this->roles()->sync($roleData);

        return $this;
    }

    /**
     * Get user's full name
     */
    public function getFullNameAttribute(): string
    {
        if ($this->first_name && $this->last_name) {
            return "{$this->first_name} {$this->last_name}";
        }

        return $this->name;
    }

    /**
     * Get user's initials
     */
    public function getInitialsAttribute(): string
    {
        $name = $this->full_name;
        $words = explode(' ', $name);

        if (count($words) >= 2) {
            return strtoupper(substr($words[0], 0, 1) . substr($words[1], 0, 1));
        }

        return strtoupper(substr($name, 0, 2));
    }

    /**
     * Get user status badge
     */
    public function getStatusBadgeAttribute(): string
    {
        if (!$this->is_active) {
            return "<span class='badge badge-danger'>Inactive</span>";
        }

        if (!$this->is_verified) {
            return "<span class='badge badge-warning'>Unverified</span>";
        }

        return "<span class='badge badge-success'>Active</span>";
    }

    /**
     * Get user avatar URL
     */
    public function getAvatarUrlAttribute(): string
    {
        if ($this->avatar) {
            return asset('storage/' . $this->avatar);
        }

        // Generate avatar using initials
        return "https://ui-avatars.com/api/?name=" . urlencode($this->full_name) . "&background=007bff&color=fff&size=128";
    }

    /**
     * Check if user needs to change password
     */
    public function needsPasswordChange(): bool
    {
        return $this->force_password_change;
    }

    /**
     * Update last login information
     */
    public function updateLastLogin(?string $ipAddress = null): void
    {
        $this->update([
            'last_login_at' => now(),
            'last_login_ip' => $ipAddress ?? request()->ip(),
        ]);
    }

    /**
     * Scope for active users
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for verified users
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope for users with specific role
     */
    public function scopeWithRole($query, string $role)
    {
        return $query->whereHas('activeRoles', function ($q) use ($role) {
            $q->where('name', $role);
        });
    }

    /**
     * Scope for users with specific permission
     */
    public function scopeWithPermission($query, string $permission)
    {
        return $query->whereHas('activeRoles.permissions', function ($q) use ($permission) {
            $q->where('name', $permission)->where('is_active', true);
        });
    }
}
