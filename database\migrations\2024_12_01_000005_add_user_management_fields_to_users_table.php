<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('username')->unique()->nullable()->after('name');
            $table->string('first_name')->nullable()->after('username');
            $table->string('last_name')->nullable()->after('first_name');
            $table->string('phone')->nullable()->after('email');
            $table->text('address')->nullable()->after('phone');
            $table->string('avatar')->nullable()->after('address');
            $table->string('timezone')->default('UTC')->after('avatar');
            $table->string('language')->default('en')->after('timezone');
            $table->boolean('is_active')->default(true)->after('language');
            $table->boolean('is_verified')->default(false)->after('is_active');
            $table->timestamp('last_login_at')->nullable()->after('is_verified');
            $table->string('last_login_ip')->nullable()->after('last_login_at');
            $table->timestamp('password_changed_at')->nullable()->after('last_login_ip');
            $table->boolean('force_password_change')->default(false)->after('password_changed_at');
            $table->json('preferences')->nullable()->after('force_password_change');
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null')->after('preferences');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null')->after('created_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['created_by']);
            $table->dropForeign(['updated_by']);
            $table->dropColumn([
                'username',
                'first_name',
                'last_name',
                'phone',
                'address',
                'avatar',
                'timezone',
                'language',
                'is_active',
                'is_verified',
                'last_login_at',
                'last_login_ip',
                'password_changed_at',
                'force_password_change',
                'preferences',
                'created_by',
                'updated_by'
            ]);
        });
    }
};
