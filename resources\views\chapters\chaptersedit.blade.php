@extends('layouts.master')

@section('content')
    <!-- Row -->

<div class="row">
    <div class="col-lg-6">

        <div class="card-box">
            <h4 class="header-title">Edit Quran Chapter Detail</h4>
            <p class="sub-header">
                Modify Quran chapter options in the system.
            </p>

            <form method="POST" class="parsley-examples" action="{{route('chapts.update',$chapt->id)}}">
                @method('PUT')
                @csrf
                <div class="form-group">
                    <label for="chaptname">Chapter Name: <span class="text-danger">*</span></label>
                    <input type="text" name="chaptname" parsley-trigger="change" required
                           placeholder="Enter Chapter Name" class="form-control" id="chaptname" value="{{$chapt->chaptername}}">
                </div>
                <div class="form-group">
                    <div class="checkbox checkbox-purple">
                        <input id="isactive" name="isactive" type="checkbox" value="1" {{$chapt->isactive ? "checked":""}}>
                        <label for="isactive">
                            Is Active
                        </label>
                    </div>

                </div>

                <div class="form-group text-right mb-0">
                    <button class="btn btn-primary waves-effect waves-light" type="submit"><i class="fas fa-save mr-1"></i>
                        Update Lanaguage
                    </button>
                    <button type="reset" onclick="window.history.go(-1)"; class="btn btn-light waves-effect ml-1">
                        Cancel
                    </button>
                </div>

            </form>
        </div> <!-- end card-box -->
    </div>
</div>
@endsection
