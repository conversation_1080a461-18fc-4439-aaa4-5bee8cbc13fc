@extends('layouts.master')

@section('content')
    <div class="row">
        <div class="col-lg-8">
            <div class="card-box">
                <h4 class="header-title">Edit Library Item</h4>
                <p class="sub-header">Update the details of this library item.</p>

                <form method="POST" class="parsley-examples" action="{{ route('library.update', $library->id) }}"
                    enctype="multipart/form-data">
                    @csrf
                    @method('PUT')

                    <div class="form-group">
                        <label for="title">Title<span class="text-danger">*</span></label>
                        <input type="text" name="title" parsley-trigger="change" required placeholder="Enter title"
                            class="form-control" id="title" value="{{ $library->title }}">
                    </div>

                    <div class="form-group">
                        <label for="author">Author/Creator</label>
                        <input type="text" name="author" parsley-trigger="change"
                            placeholder="Enter author/creator name" class="form-control" id="author"
                            value="{{ $library->author }}">
                    </div>

                    <div class="form-group">
                        <label for="description">Description<span class="text-danger">*</span></label>
                        <textarea name="description" class="form-control" id="description" rows="4" placeholder="Enter description"
                            required>{{ $library->description }}</textarea>
                    </div>

                    <div class="form-group">
                        <label for="media_type">Media Type<span class="text-danger">*</span></label>
                        <select name="media_type" id="media_type" class="form-control" required>
                            <option value="pdf" {{ $library->media_type == 'pdf' ? 'selected' : '' }}>PDF Book</option>
                            <option value="video" {{ $library->media_type == 'video' ? 'selected' : '' }}>Video</option>
                            <option value="audio" {{ $library->media_type == 'audio' ? 'selected' : '' }}>Audio</option>
                            <option value="other" {{ $library->media_type == 'other' ? 'selected' : '' }}>Other</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="thumbnail">Thumbnail Image</label>
                        @if ($library->thumbnail_path)
                            <div class="mb-2">
                                <img src="{{ asset('storage/' . $library->thumbnail_path) }}" alt="Current thumbnail"
                                    class="img-thumbnail" style="max-height: 100px;">
                                <p class="text-muted small">Current thumbnail</p>
                            </div>
                        @endif
                        <input type="file" name="thumbnail" class="form-control filestyle" id="thumbnail"
                            data-btnClass="btn-primary" accept="image/*">
                        <small class="text-muted">Leave empty to keep current thumbnail. Recommended size: 400x300px. Max
                            file size: 2MB</small>
                    </div>

                    <div class="form-group file-upload-group">
                        <label for="file">File Upload</label>
                        @if ($library->file_path)
                            <div class="mb-2">
                                <p class="text-muted small">
                                    Current file: {{ basename($library->file_path) }}
                                    ({{ $library->getFormattedFileSizeAttribute() }})
                                </p>
                            </div>
                        @endif
                        <input type="file" name="file" class="form-control filestyle" id="file"
                            data-btnClass="btn-primary">
                        <small class="text-muted">Leave empty to keep current file. Max file size: 20MB</small>
                    </div>

                    <div class="form-group external-url-group">
                        <label for="external_url">External URL</label>
                        <input type="url" name="external_url" class="form-control" id="external_url"
                            placeholder="https://example.com/video.mp4 or YouTube/Vimeo URL"
                            value="{{ $library->external_url }}">
                        <small class="text-muted">For videos or audio hosted elsewhere (YouTube, Vimeo, etc.)</small>
                    </div>

                    <div class="form-group">
                        <label for="duration">Duration</label>
                        <input type="text" name="duration" class="form-control" id="duration"
                            placeholder="e.g., 1:30:45 or 45 minutes" value="{{ $library->duration }}">
                        <small class="text-muted">For videos and audio, specify the length</small>
                    </div>

                    <div class="form-group">
                        <label for="language">Language</label>
                        <input type="text" name="language" class="form-control" id="language"
                            placeholder="e.g., English, Arabic, etc." value="{{ $library->language }}">
                    </div>

                    <div class="form-group">
                        <label for="category">Category</label>
                        <input type="text" name="category" class="form-control" id="category"
                            placeholder="e.g., Quran, Hadith, Fiqh, etc." value="{{ $library->category }}">
                    </div>

                    <div class="form-group">
                        <label for="keywords">Keywords</label>
                        <input type="text" name="keywords" class="form-control" id="keywords"
                            placeholder="Comma-separated keywords" value="{{ $library->keywords }}">
                        <small class="text-muted">Separate keywords with commas (e.g., quran, tafsir, recitation)</small>
                    </div>

                    <div class="form-group">
                        <div class="checkbox checkbox-primary">
                            <input id="isactive" name="isactive" type="checkbox" value="1"
                                {{ $library->isactive ? 'checked' : '' }}>
                            <label for="isactive">
                                Active (visible to users)
                            </label>
                        </div>
                    </div>

                    <div class="form-group text-right mb-0">
                        <button class="btn btn-primary waves-effect waves-light" type="submit">
                            <i class="fas fa-save mr-1"></i> Update Item
                        </button>
                        <button type="reset" onclick="window.history.go(-1);" class="btn btn-light waves-effect ml-1">
                            Cancel
                        </button>
                    </div>
                </form>
            </div> <!-- end card-box -->
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Show/hide fields based on media type
            $('#media_type').change(function() {
                const mediaType = $(this).val();

                // Reset all fields
                $('.file-upload-group, .external-url-group').hide();

                // Show appropriate fields based on media type
                if (mediaType === 'pdf' || mediaType === 'other') {
                    $('.file-upload-group').show();
                    $('.external-url-group').hide();
                } else if (mediaType === 'video' || mediaType === 'audio') {
                    $('.file-upload-group').show();
                    $('.external-url-group').show();
                }
            });

            // Trigger change on page load
            $('#media_type').trigger('change');
        });
    </script>
@endsection
