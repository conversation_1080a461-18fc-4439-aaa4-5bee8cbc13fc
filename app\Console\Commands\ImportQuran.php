<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Quran;
use Illuminate\Support\Facades\Http;

class ImportQuran extends Command
{
    protected $signature = 'import:quran';
    protected $description = 'Import Quran Arabic and English translation into the database';

    public function handle()
    {
        $this->info('Fetching Quran Arabic and English JSON data...');

        // Fetch Arabic Quran
        $quranData = Http::get('https://cdn.jsdelivr.net/npm/quran-json@3.1.2/dist/quran.json')->json();
        // Fetch English translation
        $quranEnglish = Http::get('https://cdn.jsdelivr.net/npm/quran-json@3.1.2/dist/quran_en.json')->json();

        if (!$quranData || !$quranEnglish) {
            $this->error('Failed to fetch Quran data.');
            return;
        }

        $this->info('Saving data into the database...');

        foreach ($quranData as $index => $surah) {
            foreach ($surah['verses'] as $ayah) {
                Quran::updateOrCreate(
                    [
                        'surah_number' => $surah['id'],
                        'ayah_number'  => $ayah['id'],
                    ],
                    [
                        'surah_name'   => $surah['name'],
                        'ayah_text'    => $ayah['text'],
                        'ayah_translation' => $quranEnglish[$index]['verses'][$ayah['id'] - 1]['translation'] ?? null,
                    ]
                );
            }
        }

        $this->info('Quran Arabic and English data imported successfully!');
    }
}
