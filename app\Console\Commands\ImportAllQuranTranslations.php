<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class ImportAllQuranTranslations extends Command
{
    protected $signature = 'import:all-quran-translations {--directory=json-files}';
    protected $description = 'Import all supported Quran translations from JSON files';

    public function handle()
    {
        $languages = ['bn', 'zh', 'en', 'es', 'fr', 'id', 'ru', 'sv', 'tr', 'ur'];
        $directory = $this->option('directory');
        
        $this->info('Starting import of all Quran translations...');
        
        foreach ($languages as $language) {
            $this->info("Importing $language translation...");
            $this->call('import:quran-translation', [
                'language' => $language,
                '--directory' => $directory,
            ]);
        }
        
        $this->info('All translations imported successfully!');
        return 0;
    }
}