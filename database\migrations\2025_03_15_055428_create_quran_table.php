<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('quran', function (Blueprint $table) {
            $table->id();
            $table->integer('surah_number');
            $table->string('surah_name');
            $table->integer('ayah_number');
            $table->text('ayah_text');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('quran');
    }
};
