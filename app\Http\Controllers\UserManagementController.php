<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rules;
use App\Services\ActivityLogger;

class UserManagementController extends Controller
{
    /**
     * Display a listing of users
     */
    public function index(Request $request)
    {
        $query = User::with(['activeRoles', 'creator', 'updater']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('username', 'like', "%{$search}%")
                  ->orWhere('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            } elseif ($request->status === 'verified') {
                $query->verified();
            } elseif ($request->status === 'unverified') {
                $query->where('is_verified', false);
            }
        }

        // Filter by role
        if ($request->filled('role')) {
            $query->withRole($request->role);
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(15);
        $roles = Role::active()->ordered()->get();

        return view('users.index', compact('users', 'roles'));
    }

    /**
     * Show the form for creating a new user
     */
    public function create()
    {
        $roles = Role::active()->ordered()->get();
        $timezones = $this->getTimezones();
        $languages = $this->getLanguages();

        return view('users.create', compact('roles', 'timezones', 'languages'));
    }

    /**
     * Store a newly created user
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'username' => ['nullable', 'string', 'max:255', 'unique:users'],
            'first_name' => ['nullable', 'string', 'max:255'],
            'last_name' => ['nullable', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'phone' => ['nullable', 'string', 'max:20'],
            'address' => ['nullable', 'string', 'max:500'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'roles' => ['array'],
            'roles.*' => ['exists:roles,id'],
            'avatar' => ['nullable', 'image', 'max:2048'],
            'timezone' => ['required', 'string'],
            'language' => ['required', 'string'],
            'is_active' => ['boolean'],
            'is_verified' => ['boolean'],
            'force_password_change' => ['boolean'],
        ]);

        $userData = $request->except(['password', 'password_confirmation', 'roles', 'avatar']);
        $userData['password'] = Hash::make($request->password);
        $userData['created_by'] = auth()->id();

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            $userData['avatar'] = $request->file('avatar')->store('avatars', 'public');
        }

        $user = User::create($userData);

        // Assign roles
        if ($request->filled('roles')) {
            $user->syncRoles(
                Role::whereIn('id', $request->roles)->get(),
                auth()->user()
            );
        }

        // Log activity
        ActivityLogger::log('user_create', "Created user: {$user->name}", $user, [
            'user_id' => $user->id,
            'roles_assigned' => $request->roles ?? [],
        ]);

        return redirect()->route('users.index')
                        ->with('success', 'User created successfully.');
    }

    /**
     * Display the specified user
     */
    public function show(User $user)
    {
        $user->load(['activeRoles.permissions', 'creator', 'updater', 'recentActivities']);
        
        return view('users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified user
     */
    public function edit(User $user)
    {
        $user->load('activeRoles');
        $roles = Role::active()->ordered()->get();
        $timezones = $this->getTimezones();
        $languages = $this->getLanguages();

        return view('users.edit', compact('user', 'roles', 'timezones', 'languages'));
    }

    /**
     * Update the specified user
     */
    public function update(Request $request, User $user)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'username' => ['nullable', 'string', 'max:255', 'unique:users,username,' . $user->id],
            'first_name' => ['nullable', 'string', 'max:255'],
            'last_name' => ['nullable', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'phone' => ['nullable', 'string', 'max:20'],
            'address' => ['nullable', 'string', 'max:500'],
            'password' => ['nullable', 'confirmed', Rules\Password::defaults()],
            'roles' => ['array'],
            'roles.*' => ['exists:roles,id'],
            'avatar' => ['nullable', 'image', 'max:2048'],
            'timezone' => ['required', 'string'],
            'language' => ['required', 'string'],
            'is_active' => ['boolean'],
            'is_verified' => ['boolean'],
            'force_password_change' => ['boolean'],
        ]);

        $userData = $request->except(['password', 'password_confirmation', 'roles', 'avatar']);
        $userData['updated_by'] = auth()->id();

        // Handle password update
        if ($request->filled('password')) {
            $userData['password'] = Hash::make($request->password);
            $userData['password_changed_at'] = now();
        }

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            // Delete old avatar
            if ($user->avatar) {
                Storage::disk('public')->delete($user->avatar);
            }
            $userData['avatar'] = $request->file('avatar')->store('avatars', 'public');
        }

        $user->update($userData);

        // Update roles
        if ($request->has('roles')) {
            $user->syncRoles(
                Role::whereIn('id', $request->roles ?? [])->get(),
                auth()->user()
            );
        }

        // Log activity
        ActivityLogger::log('user_update', "Updated user: {$user->name}", $user, [
            'user_id' => $user->id,
            'roles_assigned' => $request->roles ?? [],
            'password_changed' => $request->filled('password'),
        ]);

        return redirect()->route('users.index')
                        ->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified user
     */
    public function destroy(User $user)
    {
        // Prevent deletion of current user
        if ($user->id === auth()->id()) {
            return redirect()->route('users.index')
                            ->with('error', 'You cannot delete your own account.');
        }

        // Delete avatar
        if ($user->avatar) {
            Storage::disk('public')->delete($user->avatar);
        }

        // Log activity before deletion
        ActivityLogger::log('user_delete', "Deleted user: {$user->name}", $user, [
            'user_id' => $user->id,
            'user_email' => $user->email,
        ]);

        $user->delete();

        return redirect()->route('users.index')
                        ->with('success', 'User deleted successfully.');
    }

    /**
     * Toggle user status
     */
    public function toggleStatus(User $user)
    {
        // Prevent deactivating current user
        if ($user->id === auth()->id()) {
            return response()->json(['error' => 'You cannot deactivate your own account.'], 400);
        }

        $user->update([
            'is_active' => !$user->is_active,
            'updated_by' => auth()->id(),
        ]);

        $status = $user->is_active ? 'activated' : 'deactivated';
        
        // Log activity
        ActivityLogger::log('user_status_change', "User {$status}: {$user->name}", $user, [
            'user_id' => $user->id,
            'new_status' => $user->is_active,
        ]);

        return response()->json([
            'success' => true,
            'message' => "User {$status} successfully.",
            'status' => $user->is_active,
        ]);
    }

    /**
     * Get available timezones
     */
    private function getTimezones(): array
    {
        return [
            'UTC' => 'UTC',
            'America/New_York' => 'Eastern Time',
            'America/Chicago' => 'Central Time',
            'America/Denver' => 'Mountain Time',
            'America/Los_Angeles' => 'Pacific Time',
            'Europe/London' => 'London',
            'Europe/Paris' => 'Paris',
            'Asia/Tokyo' => 'Tokyo',
            'Asia/Shanghai' => 'Shanghai',
            'Asia/Karachi' => 'Karachi',
            'Asia/Dubai' => 'Dubai',
        ];
    }

    /**
     * Get available languages
     */
    private function getLanguages(): array
    {
        return [
            'en' => 'English',
            'ur' => 'Urdu',
            'ar' => 'Arabic',
            'tr' => 'Turkish',
            'fr' => 'French',
            'es' => 'Spanish',
            'de' => 'German',
            'it' => 'Italian',
            'pt' => 'Portuguese',
            'ru' => 'Russian',
        ];
    }
}
