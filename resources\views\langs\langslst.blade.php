@extends('layouts.master')

@section('content')
    <!-- Row -->

    <div class="row">
        <div class="col-12">
            <div class="card-box table-responsive">
                <a href="{{ route('langs.create') }}" class="btn btn-primary float-right">Add Language</a>
                <h4 class="header-title"><b>Languages List</b></h4>
                <p class="sub-header">
                    List of Languages of Quran for Translation.
                </p>

                <table id="datatable-buttons" class="table table-striped table-bordered dt-responsive nowrap"
                    style="border-collapse: collapse; border-spacing: 0; width: 100%;">
                    <thead>
                        <tr>
                            <th class="border-bottom-0">ID</th>
                            <th class="border-bottom-0">Language</th>
                            <th class="border-bottom-0">Code</th>
                            <th class="border-bottom-0">Status</th>
                            <th class="border-bottom-0">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($langs as $lang)
                            <tr>
                                <td>{{ $lang->id }}</td>
                                <td>{{ $lang->name }}</td>
                                <td>{{ $lang->code }}</td>
                                <td><span
                                        class="badge badge-{{ $lang->isactive == 1 ? 'primary' : 'danger' }}">{{ $lang->isactive == 1 ? 'Active' : 'Disable' }}</span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('langs.edit', $lang->id) }}" class="btn btn-sm btn-purple"><i
                                                class="far fa-edit"></i></a>
                                        <form action="{{ route('langs.destroy', $lang) }}" method="POST"
                                            style="display: inline-block">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit"
                                                onclick="return confirm('Are you sure to delete this record?')"
                                                class="btn btn-sm btn-danger"><i class="fas fa-trash-alt"></i></button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach

                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <!-- End Row -->
@endsection
