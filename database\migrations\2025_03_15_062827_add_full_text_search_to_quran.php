<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    public function up(): void
    {
        // Add FULLTEXT Index (Only for MySQL)
        DB::statement('ALTER TABLE quran ADD FULLTEXT(ayah_text, ayah_translation)');
    }

    public function down(): void
    {
        DB::statement('ALTER TABLE quran DROP INDEX ayah_text');
    }
};
