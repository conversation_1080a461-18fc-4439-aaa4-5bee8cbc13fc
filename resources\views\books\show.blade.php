@extends('layouts.master')

@section('content')
    <div class="row">
        <div class="col-lg-8">
            <div class="card-box">
                <div class="d-flex justify-content-between mb-3">
                    <h4 class="header-title">{{ $book->title }}</h4>
                    <div>
                        <a href="{{ route('books.edit', $book->id) }}" class="btn btn-warning btn-sm">Edit</a>
                        <a href="{{ route('books.index') }}" class="btn btn-light btn-sm ml-1">Back to List</a>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered mb-4">
                        <tr>
                            <th style="width: 150px;">Title</th>
                            <td>{{ $book->title }}</td>
                        </tr>
                        <tr>
                            <th>Author</th>
                            <td>{{ $book->author ?? 'N/A' }}</td>
                        </tr>
                        <tr>
                            <th>Description</th>
                            <td>{{ $book->description ?? 'N/A' }}</td>
                        </tr>
                        <tr>
                            <th>Status</th>
                            <td>
                                @if ($book->isactive)
                                    <span class="badge badge-success">Active</span>
                                @else
                                    <span class="badge badge-danger">Inactive</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>Created By</th>
                            <td>{{ $book->created_by }}</td>
                        </tr>
                        <tr>
                            <th>Created At</th>
                            <td>{{ $book->created_at->format('Y-m-d H:i:s') }}</td>
                        </tr>
                    </table>
                </div>

                <div class="text-center">
                    <a href="{{ asset('storage/' . $book->file_path) }}" class="btn btn-primary" target="_blank">
                        <i class="fas fa-file-pdf mr-1"></i> View PDF
                    </a>
                    <a href="{{ asset('storage/' . $book->file_path) }}" class="btn btn-success ml-2" download>
                        <i class="fas fa-download mr-1"></i> Download PDF
                    </a>
                </div>
            </div>
        </div>
    </div>
@endsection
