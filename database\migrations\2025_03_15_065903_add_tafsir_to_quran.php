<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('quran', function (Blueprint $table) {
            $table->text('tafsir')->nullable()->after('ayah_translation');
        });
    }

    public function down(): void
    {
        Schema::table('quran', function (Blueprint $table) {
            $table->dropColumn('tafsir');
        });
    }
};
