@extends('layouts.master')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('quran.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('users.index') }}">Users</a></li>
                    <li class="breadcrumb-item active">Create</li>
                </ol>
            </div>
            <h4 class="page-title">Create User</h4>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title">
                    <i class="mdi mdi-account-plus text-primary"></i> Create New User
                </h4>

                <form method="POST" action="{{ route('users.store') }}" enctype="multipart/form-data">
                    @csrf

                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-8">
                            <h5><i class="mdi mdi-account"></i> Basic Information</h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="first_name" class="form-label">First Name</label>
                                        <input type="text" class="form-control @error('first_name') is-invalid @enderror" 
                                               id="first_name" name="first_name" value="{{ old('first_name') }}">
                                        @error('first_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="last_name" class="form-label">Last Name</label>
                                        <input type="text" class="form-control @error('last_name') is-invalid @enderror" 
                                               id="last_name" name="last_name" value="{{ old('last_name') }}">
                                        @error('last_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="name" class="form-label">Display Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                               id="name" name="name" value="{{ old('name') }}" required>
                                        @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="form-text text-muted">This will be auto-generated from first and last name</small>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="username" class="form-label">Username</label>
                                        <input type="text" class="form-control @error('username') is-invalid @enderror" 
                                               id="username" name="username" value="{{ old('username') }}">
                                        @error('username')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="form-text text-muted">Optional unique identifier</small>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                               id="email" name="email" value="{{ old('email') }}" required>
                                        @error('email')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">Phone</label>
                                        <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                                               id="phone" name="phone" value="{{ old('phone') }}">
                                        @error('phone')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="address" class="form-label">Address</label>
                                <textarea class="form-control @error('address') is-invalid @enderror" 
                                          id="address" name="address" rows="2">{{ old('address') }}</textarea>
                                @error('address')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Password Section -->
                            <h5 class="mt-4"><i class="mdi mdi-lock"></i> Password</h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                                        <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                               id="password" name="password" required>
                                        @error('password')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="password_confirmation" class="form-label">Confirm Password <span class="text-danger">*</span></label>
                                        <input type="password" class="form-control" 
                                               id="password_confirmation" name="password_confirmation" required>
                                    </div>
                                </div>
                            </div>

                            <!-- Preferences -->
                            <h5 class="mt-4"><i class="mdi mdi-cog"></i> Preferences</h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="timezone" class="form-label">Timezone <span class="text-danger">*</span></label>
                                        <select class="form-control @error('timezone') is-invalid @enderror" id="timezone" name="timezone" required>
                                            @foreach($timezones as $key => $value)
                                                <option value="{{ $key }}" {{ old('timezone', 'UTC') == $key ? 'selected' : '' }}>
                                                    {{ $value }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('timezone')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="language" class="form-label">Language <span class="text-danger">*</span></label>
                                        <select class="form-control @error('language') is-invalid @enderror" id="language" name="language" required>
                                            @foreach($languages as $key => $value)
                                                <option value="{{ $key }}" {{ old('language', 'en') == $key ? 'selected' : '' }}>
                                                    {{ $value }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('language')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Status Options -->
                            <h5 class="mt-4"><i class="mdi mdi-account-check"></i> Status & Options</h5>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1" 
                                               {{ old('is_active', true) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            Active
                                        </label>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="is_verified" name="is_verified" value="1" 
                                               {{ old('is_verified', false) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_verified">
                                            Verified
                                        </label>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="force_password_change" name="force_password_change" value="1" 
                                               {{ old('force_password_change', true) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="force_password_change">
                                            Force Password Change
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Avatar and Roles -->
                        <div class="col-md-4">
                            <!-- Avatar -->
                            <h5><i class="mdi mdi-image"></i> Avatar</h5>
                            <div class="text-center mb-3">
                                <img src="https://ui-avatars.com/api/?name=New+User&background=007bff&color=fff&size=120" 
                                     alt="Avatar Preview" class="rounded-circle mb-2" width="120" height="120" id="avatar-preview">
                                <div>
                                    <input type="file" class="form-control @error('avatar') is-invalid @enderror" 
                                           id="avatar" name="avatar" accept="image/*" onchange="previewAvatar(this)">
                                    @error('avatar')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Max 2MB, JPG/PNG only</small>
                                </div>
                            </div>

                            <!-- Role Assignment -->
                            <h5><i class="mdi mdi-account-group"></i> Assign Roles</h5>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-2">
                                    <small class="text-muted">Select roles for this user:</small>
                                    <div>
                                        <button type="button" class="btn btn-xs btn-outline-primary" onclick="selectAllRoles()">All</button>
                                        <button type="button" class="btn btn-xs btn-outline-secondary" onclick="selectNoRoles()">None</button>
                                    </div>
                                </div>
                                
                                @foreach($roles as $role)
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input role-checkbox" 
                                               id="role_{{ $role->id }}" name="roles[]" value="{{ $role->id }}"
                                               {{ in_array($role->id, old('roles', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="role_{{ $role->id }}">
                                            <span class="badge me-1" style="background-color: {{ $role->color }}; color: white;">
                                                {{ $role->display_name }}
                                            </span>
                                            @if($role->is_system)
                                                <span class="badge badge-danger">System</span>
                                            @endif
                                            <br><small class="text-muted">{{ $role->description }}</small>
                                        </label>
                                    </div>
                                @endforeach
                            </div>

                            <!-- Quick Role Suggestions -->
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <h6>Quick Assign:</h6>
                                    <div class="d-grid gap-1">
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="assignRole('viewer')">
                                            Basic User (Viewer)
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-success" onclick="assignRole('editor')">
                                            Content Creator (Editor)
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-warning" onclick="assignRole('moderator')">
                                            Content Moderator
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-end mt-4">
                        <a href="{{ route('users.index') }}" class="btn btn-secondary me-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">Create User</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function previewAvatar(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('avatar-preview').src = e.target.result;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

function selectAllRoles() {
    const checkboxes = document.querySelectorAll('.role-checkbox');
    checkboxes.forEach(cb => cb.checked = true);
}

function selectNoRoles() {
    const checkboxes = document.querySelectorAll('.role-checkbox');
    checkboxes.forEach(cb => cb.checked = false);
}

function assignRole(roleName) {
    // First uncheck all
    selectNoRoles();
    
    // Then check the specific role
    const roleMap = {
        'viewer': 'viewer',
        'editor': 'editor', 
        'moderator': 'moderator'
    };
    
    const targetRole = roleMap[roleName];
    if (targetRole) {
        const checkboxes = document.querySelectorAll('.role-checkbox');
        checkboxes.forEach(cb => {
            const label = cb.nextElementSibling;
            if (label && label.textContent.toLowerCase().includes(targetRole)) {
                cb.checked = true;
            }
        });
    }
}

// Auto-generate display name and username from first and last name
document.addEventListener('DOMContentLoaded', function() {
    const firstNameInput = document.getElementById('first_name');
    const lastNameInput = document.getElementById('last_name');
    const nameInput = document.getElementById('name');
    const usernameInput = document.getElementById('username');

    function updateGeneratedFields() {
        const firstName = firstNameInput.value.trim();
        const lastName = lastNameInput.value.trim();
        
        // Update display name
        if (firstName || lastName) {
            const fullName = [firstName, lastName].filter(n => n).join(' ');
            if (nameInput.dataset.autoGenerated !== 'false') {
                nameInput.value = fullName;
                nameInput.dataset.autoGenerated = 'true';
            }
            
            // Update avatar preview
            const avatarPreview = document.getElementById('avatar-preview');
            if (!document.getElementById('avatar').files.length) {
                avatarPreview.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(fullName)}&background=007bff&color=fff&size=120`;
            }
        }
        
        // Auto-generate username suggestion
        if (firstName && usernameInput.dataset.autoGenerated !== 'false') {
            let username = firstName.toLowerCase();
            if (lastName) {
                username += '.' + lastName.toLowerCase();
            }
            username = username.replace(/[^a-z0-9.]/g, '');
            usernameInput.value = username;
            usernameInput.dataset.autoGenerated = 'true';
        }
    }

    firstNameInput.addEventListener('input', updateGeneratedFields);
    lastNameInput.addEventListener('input', updateGeneratedFields);
    
    nameInput.addEventListener('input', function() {
        this.dataset.autoGenerated = 'false';
    });
    
    usernameInput.addEventListener('input', function() {
        this.dataset.autoGenerated = 'false';
    });
});
</script>
@endsection
