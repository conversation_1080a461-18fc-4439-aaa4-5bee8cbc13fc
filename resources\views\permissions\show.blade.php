@extends('layouts.master')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('quran.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('permissions.index') }}">Permissions</a></li>
                    <li class="breadcrumb-item active">{{ $permission->display_name }}</li>
                </ol>
            </div>
            <h4 class="page-title">Permission Details</h4>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 class="header-title">
                        <i class="mdi mdi-key text-primary"></i> {{ $permission->display_name }}
                    </h4>
                    <div>
                        @if(auth()->user()->hasPermission('permissions.update'))
                            <a href="{{ route('permissions.edit', $permission) }}" class="btn btn-warning">
                                <i class="mdi mdi-pencil"></i> Edit
                            </a>
                        @endif
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">Name:</th>
                                <td><code>{{ $permission->name }}</code></td>
                            </tr>
                            <tr>
                                <th>Display Name:</th>
                                <td>{{ $permission->display_name }}</td>
                            </tr>
                            <tr>
                                <th>Module:</th>
                                <td>{!! $permission->module_badge !!}</td>
                            </tr>
                            <tr>
                                <th>Action:</th>
                                <td>{!! $permission->action_badge !!}</td>
                            </tr>
                            @if($permission->resource)
                                <tr>
                                    <th>Resource:</th>
                                    <td><span class="badge badge-secondary">{{ $permission->resource }}</span></td>
                                </tr>
                            @endif
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">Status:</th>
                                <td>{!! $permission->badge !!}</td>
                            </tr>
                            <tr>
                                <th>Type:</th>
                                <td>
                                    @if($permission->is_system)
                                        <span class="badge badge-danger">System</span>
                                    @else
                                        <span class="badge badge-info">Custom</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <th>Sort Order:</th>
                                <td>{{ $permission->sort_order }}</td>
                            </tr>
                            <tr>
                                <th>Created:</th>
                                <td>{{ $permission->created_at->format('M j, Y g:i A') }}</td>
                            </tr>
                            <tr>
                                <th>Updated:</th>
                                <td>{{ $permission->updated_at->format('M j, Y g:i A') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                @if($permission->description)
                    <div class="mt-3">
                        <h6>Description:</h6>
                        <p class="text-muted">{{ $permission->description }}</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="mdi mdi-account-group"></i> Assigned Roles
                    <span class="badge badge-primary ms-2">{{ $permission->roles->count() }}</span>
                </h5>

                @if($permission->roles->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($permission->roles as $role)
                            <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <div>
                                    <span class="badge me-2" style="background-color: {{ $role->color }}; color: white;">
                                        {{ $role->display_name }}
                                    </span>
                                    @if($role->is_system)
                                        <span class="badge badge-danger">System</span>
                                    @endif
                                </div>
                                <div>
                                    @if(auth()->user()->hasPermission('roles.read'))
                                        <a href="{{ route('roles.show', $role) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="mdi mdi-eye"></i>
                                        </a>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-3">
                        <i class="mdi mdi-account-group-outline h2 text-muted"></i>
                        <p class="text-muted">No roles assigned</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Users with this permission -->
        <div class="card mt-3">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="mdi mdi-account-multiple"></i> Users with Permission
                </h5>

                @php
                    $usersWithPermission = $permission->users()->take(10)->get();
                @endphp

                @if($usersWithPermission->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($usersWithPermission as $user)
                            <div class="list-group-item d-flex align-items-center px-0">
                                <img src="{{ $user->avatar_url }}" alt="{{ $user->name }}" 
                                     class="rounded-circle me-2" width="32" height="32">
                                <div class="flex-grow-1">
                                    <div class="fw-bold">{{ $user->full_name }}</div>
                                    <small class="text-muted">{{ $user->email }}</small>
                                </div>
                                @if(auth()->user()->hasPermission('users.read'))
                                    <a href="{{ route('users.show', $user) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="mdi mdi-eye"></i>
                                    </a>
                                @endif
                            </div>
                        @endforeach
                    </div>

                    @if($permission->users()->count() > 10)
                        <div class="text-center mt-2">
                            <small class="text-muted">
                                +{{ $permission->users()->count() - 10 }} more users
                            </small>
                        </div>
                    @endif
                @else
                    <div class="text-center py-3">
                        <i class="mdi mdi-account-outline h2 text-muted"></i>
                        <p class="text-muted">No users have this permission</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
