<?php

namespace App\Http\Controllers;

use App\Models\Audit;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AuditController extends Controller
{
    /**
     * Display a listing of audit logs
     */
    public function index(Request $request)
    {
        $query = Audit::with(['user', 'auditable']);

        // Filter by user
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Filter by event
        if ($request->filled('event')) {
            $query->where('event', $request->event);
        }

        // Filter by model type
        if ($request->filled('model_type')) {
            $query->where('auditable_type', $request->model_type);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search in URL or IP address
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('url', 'like', "%{$search}%")
                  ->orWhere('ip_address', 'like', "%{$search}%")
                  ->orWhere('user_agent', 'like', "%{$search}%");
            });
        }

        $audits = $query->orderBy('created_at', 'desc')->paginate(20);

        // Get filter options
        $users = User::select('id', 'name')->orderBy('name')->get();
        $events = Audit::select('event')->distinct()->orderBy('event')->pluck('event');
        $modelTypes = Audit::select('auditable_type')->distinct()->orderBy('auditable_type')->pluck('auditable_type');

        // Get statistics
        $stats = $this->getAuditStatistics();

        return view('audits.index', compact('audits', 'users', 'events', 'modelTypes', 'stats'));
    }

    /**
     * Show the specified audit log
     */
    public function show(Audit $audit)
    {
        $audit->load(['user', 'auditable']);
        return view('audits.show', compact('audit'));
    }

    /**
     * Get audit statistics
     */
    private function getAuditStatistics()
    {
        $today = Carbon::today();
        $thisWeek = Carbon::now()->startOfWeek();
        $thisMonth = Carbon::now()->startOfMonth();

        return [
            'total' => Audit::count(),
            'today' => Audit::whereDate('created_at', $today)->count(),
            'this_week' => Audit::where('created_at', '>=', $thisWeek)->count(),
            'this_month' => Audit::where('created_at', '>=', $thisMonth)->count(),
            'by_event' => Audit::select('event', DB::raw('count(*) as count'))
                ->groupBy('event')
                ->orderBy('count', 'desc')
                ->limit(5)
                ->get(),
            'by_model' => Audit::select('auditable_type', DB::raw('count(*) as count'))
                ->groupBy('auditable_type')
                ->orderBy('count', 'desc')
                ->limit(5)
                ->get(),
            'top_users' => Audit::select('user_id', DB::raw('count(*) as count'))
                ->whereNotNull('user_id')
                ->with('user:id,name')
                ->groupBy('user_id')
                ->orderBy('count', 'desc')
                ->limit(5)
                ->get(),
        ];
    }

    /**
     * Export audit logs
     */
    public function export(Request $request)
    {
        $query = Audit::with(['user', 'auditable']);

        // Apply same filters as index
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('event')) {
            $query->where('event', $request->event);
        }

        if ($request->filled('model_type')) {
            $query->where('auditable_type', $request->model_type);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $audits = $query->orderBy('created_at', 'desc')->get();

        $filename = 'audit_logs_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($audits) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'ID',
                'Date/Time',
                'User',
                'Event',
                'Model Type',
                'Model ID',
                'URL',
                'IP Address',
                'User Agent'
            ]);

            foreach ($audits as $audit) {
                fputcsv($file, [
                    $audit->id,
                    $audit->created_at->format('Y-m-d H:i:s'),
                    $audit->user ? $audit->user->name : 'System',
                    $audit->event,
                    class_basename($audit->auditable_type),
                    $audit->auditable_id,
                    $audit->url,
                    $audit->ip_address,
                    $audit->user_agent
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Clean old audit logs
     */
    public function cleanup(Request $request)
    {
        $request->validate([
            'days' => 'required|integer|min:1|max:365'
        ]);

        $cutoffDate = Carbon::now()->subDays($request->days);
        $deletedCount = Audit::where('created_at', '<', $cutoffDate)->delete();

        return redirect()->route('audits.index')
            ->with('success', "Deleted {$deletedCount} audit records older than {$request->days} days.");
    }
}
