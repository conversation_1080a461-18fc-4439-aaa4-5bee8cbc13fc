@extends('layouts.master')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('quran.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item active">Roles</li>
                </ol>
            </div>
            <h4 class="page-title">Role Management</h4>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <h4 class="header-title">
                            <i class="mdi mdi-account-group text-primary"></i> Roles
                        </h4>
                    </div>
                    <div class="col-md-6 text-end">
                        @if(auth()->user()->hasPermission('roles.create'))
                            <a href="{{ route('roles.create') }}" class="btn btn-primary">
                                <i class="mdi mdi-plus"></i> Create Role
                            </a>
                        @endif
                    </div>
                </div>

                <!-- Filters -->
                <div class="row mb-3">
                    <div class="col-12">
                        <form method="GET" action="{{ route('roles.index') }}" class="row g-3">
                            <div class="col-md-4">
                                <input type="text" name="search" class="form-control" placeholder="Search roles..." value="{{ request('search') }}">
                            </div>
                            <div class="col-md-3">
                                <select name="status" class="form-control">
                                    <option value="">All Status</option>
                                    <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                                    <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                    <option value="system" {{ request('status') == 'system' ? 'selected' : '' }}>System</option>
                                    <option value="custom" {{ request('status') == 'custom' ? 'selected' : '' }}>Custom</option>
                                </select>
                            </div>
                            <div class="col-md-5">
                                <button type="submit" class="btn btn-secondary">
                                    <i class="mdi mdi-filter"></i> Filter
                                </button>
                                <a href="{{ route('roles.index') }}" class="btn btn-light">
                                    <i class="mdi mdi-refresh"></i> Reset
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Roles Table -->
                <div class="table-responsive">
                    <table class="table table-striped table-bordered">
                        <thead>
                            <tr>
                                <th>Role</th>
                                <th>Description</th>
                                <th>Permissions</th>
                                <th>Users</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($roles as $role)
                                <tr>
                                    <td>
                                        {!! $role->color_badge !!}
                                        @if($role->is_system)
                                            <span class="badge badge-danger ms-1">System</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ $role->display_name }}</strong>
                                            @if($role->description)
                                                <br><small class="text-muted">{{ $role->description }}</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-info">{{ $role->permissions->count() }} permissions</span>
                                    </td>
                                    <td>
                                        <span class="badge badge-success">{{ $role->activeUsers->count() }} users</span>
                                    </td>
                                    <td>{!! $role->badge !!}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            @if(auth()->user()->hasPermission('roles.read'))
                                                <a href="{{ route('roles.show', $role) }}" class="btn btn-sm btn-info">
                                                    <i class="mdi mdi-eye"></i>
                                                </a>
                                            @endif
                                            
                                            @if(auth()->user()->hasPermission('roles.update'))
                                                <a href="{{ route('roles.edit', $role) }}" class="btn btn-sm btn-warning">
                                                    <i class="mdi mdi-pencil"></i>
                                                </a>
                                            @endif

                                            @if(auth()->user()->hasPermission('roles.create'))
                                                <a href="{{ route('roles.clone', $role) }}" class="btn btn-sm btn-secondary">
                                                    <i class="mdi mdi-content-copy"></i>
                                                </a>
                                            @endif

                                            @if(auth()->user()->hasPermission('roles.update') && (!$role->is_system || !$role->is_active))
                                                <button type="button" class="btn btn-sm btn-{{ $role->is_active ? 'secondary' : 'success' }}" 
                                                        onclick="toggleStatus({{ $role->id }})">
                                                    <i class="mdi mdi-{{ $role->is_active ? 'pause' : 'play' }}"></i>
                                                </button>
                                            @endif

                                            @if(auth()->user()->hasPermission('roles.delete') && !$role->is_system)
                                                <form method="POST" action="{{ route('roles.destroy', $role) }}" style="display: inline;">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger" 
                                                            onclick="return confirm('Are you sure you want to delete this role?')">
                                                        <i class="mdi mdi-delete"></i>
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <i class="mdi mdi-account-group-outline h1 text-muted"></i>
                                        <h4 class="text-muted">No roles found</h4>
                                        <p class="text-muted">No roles match your current filters.</p>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <p class="text-muted">
                            Showing {{ $roles->firstItem() ?? 0 }} to {{ $roles->lastItem() ?? 0 }} 
                            of {{ $roles->total() }} roles
                        </p>
                    </div>
                    <div>
                        {{ $roles->appends(request()->query())->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function toggleStatus(roleId) {
    if (confirm('Are you sure you want to change the status of this role?')) {
        fetch(`/roles/${roleId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.error || 'An error occurred');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the role status');
        });
    }
}
</script>
@endsection
