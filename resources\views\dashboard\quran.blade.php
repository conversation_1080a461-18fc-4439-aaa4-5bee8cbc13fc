@extends('layouts.master')

@section('content')
    <!-- Debug output -->
    <!-- Surah Count: {{ $surahCount }} -->

    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="javascript: void(0);">Home</a></li>
                            <li class="breadcrumb-item active">Quran & Library Dashboard</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Quran & Library Dashboard</h4>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <!-- Stats Boxes -->
        <div class="row">
            <div class="col-md-6 col-xl-3">
                <div class="card-box tilebox-one">
                    <i class="fe-book float-right"></i>
                    <h5 class="text-muted text-uppercase mb-3 mt-0">Total Surahs</h5>
                    <h3 class="mb-3">{{ $surahCount }}</h3>
                    <span class="badge badge-primary"> 100% </span> <span
                        class="text-muted ml-2 vertical-middle">Complete</span>
                </div>
            </div>

            <div class="col-md-6 col-xl-3">
                <div class="card-box tilebox-one">
                    <i class="mdi mdi-book-open-page-variant float-right"></i>
                    <h5 class="text-muted text-uppercase mb-3 mt-0">Total Chapters</h5>
                    <h3 class="mb-3">{{ $chapterCount }}</h3>
                    <span class="badge badge-primary"> +{{ $newChaptersCount }} </span> <span
                        class="text-muted ml-2 vertical-middle">New this month</span>
                </div>
            </div>

            <div class="col-md-6 col-xl-3">
                <div class="card-box tilebox-one">
                    <i class="mdi mdi-translate float-right"></i>
                    <h5 class="text-muted text-uppercase mb-3 mt-0">Languages</h5>
                    <h3 class="mb-3">{{ $languageCount }}</h3>
                    <span class="badge badge-primary"> +{{ $newLanguagesCount }} </span> <span
                        class="text-muted ml-2 vertical-middle">New this month</span>
                </div>
            </div>

            <div class="col-md-6 col-xl-3">
                <div class="card-box tilebox-one">
                    <i class="fe-tag float-right"></i>
                    <h5 class="text-muted text-uppercase mb-3 mt-0">Topics</h5>
                    <h3 class="mb-3">{{ $topicCount }}</h3>
                    <span class="badge badge-primary"> +{{ $newTopicsCount }} </span> <span
                        class="text-muted ml-2 vertical-middle">New this month</span>
                </div>
            </div>
        </div>

        <!-- Charts -->
        <div class="row">
            <div class="col-xl-6">
                <div class="card-box">
                    <h4 class="header-title">Topics Distribution</h4>
                    <p class="sub-header">Distribution of topics across different categories</p>
                    <div class="mt-3 chartjs-chart">
                        <canvas id="topics-chart" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-xl-6">
                <div class="card-box">
                    <h4 class="header-title">Language Distribution</h4>
                    <p class="sub-header">Distribution of content across different languages</p>
                    <div class="mt-3 chartjs-chart">
                        <canvas id="language-chart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Topics and Library -->
        <div class="row">
            <div class="col-xl-6">
                <div class="card-box">
                    <h4 class="header-title mb-3">Recent Topics</h4>
                    <div class="table-responsive">
                        <table class="table table-hover table-centered mb-0">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Topic Name</th>
                                    <th>Category</th>
                                    <th>Added Date</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (isset($recentTopics) && count($recentTopics) > 0)
                                    @foreach ($recentTopics as $topic)
                                        <tr>
                                            <td>{{ $topic->id }}</td>
                                            <td>{{ $topic->topicname }}</td>
                                            <td>{{ $topic->category }}</td>
                                            <td>{{ $topic->created_at ? $topic->created_at->format('d M Y') : 'N/A' }}</td>
                                            <td>
                                                <a href="{{ route('topics.show', $topic->id) }}"
                                                    class="btn btn-sm btn-info">
                                                    <i class="fa fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                @else
                                    <tr>
                                        <td colspan="5" class="text-center">No recent topics found</td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="col-xl-6">
                <div class="card-box">
                    <h4 class="header-title mb-3">Recent Chapters</h4>
                    <div class="table-responsive">
                        <table class="table table-hover table-centered mb-0">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Chapter Name</th>
                                    <th>Language</th>
                                    <th>Added Date</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (isset($recentChapters) && count($recentChapters) > 0)
                                    @foreach ($recentChapters as $chapter)
                                        <tr>
                                            <td>{{ $chapter->id }}</td>
                                            <td>{{ $chapter->chaptername }}</td>
                                            <td>{{ $chapter->language }}</td>
                                            <td>{{ $chapter->created_at ? $chapter->created_at->format('d M Y') : 'N/A' }}
                                            </td>
                                            <td>
                                                <a href="{{ route('chapts.show', $chapter->id) }}"
                                                    class="btn btn-sm btn-info">
                                                    <i class="fa fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                @else
                                    <tr>
                                        <td colspan="5" class="text-center">No recent chapters found</td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Popular Content -->
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <h4 class="header-title mb-3">Most Viewed Content</h4>

                    <ul class="nav nav-tabs">
                        <li class="nav-item">
                            <a href="#surahs-tab" data-toggle="tab" aria-expanded="false" class="nav-link active">
                                Surahs
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#topics-tab" data-toggle="tab" aria-expanded="true" class="nav-link">
                                Topics
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#chapters-tab" data-toggle="tab" aria-expanded="false" class="nav-link">
                                Chapters
                            </a>
                        </li>
                    </ul>
                    <div class="tab-content">
                        <div class="tab-pane show active" id="surahs-tab">
                            <div class="table-responsive">
                                <table class="table table-hover table-centered mb-0">
                                    <thead>
                                        <tr>
                                            <th>Surah Name</th>
                                            <th>Arabic Name</th>
                                            <th>Total Verses</th>
                                            <th>Views</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if (isset($popularSurahs) && count($popularSurahs) > 0)
                                            @foreach ($popularSurahs as $surah)
                                                <tr>
                                                    <td>{{ $surah->surahname }}</td>
                                                    <td>{{ $surah->arabic_name }}</td>
                                                    <td>{{ $surah->verses_count }}</td>
                                                    <td>{{ $surah->views }}</td>
                                                    <td>
                                                        <a href="{{ route('surahs.show', $surah->id) }}"
                                                            class="btn btn-sm btn-info">
                                                            <i class="fa fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        @else
                                            <tr>
                                                <td colspan="5" class="text-center">No data available</td>
                                            </tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="tab-pane" id="topics-tab">
                            <div class="table-responsive">
                                <table class="table table-hover table-centered mb-0">
                                    <thead>
                                        <tr>
                                            <th>Topic Name</th>
                                            <th>Category</th>
                                            <th>Details Count</th>
                                            <th>Views</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if (isset($popularTopics) && count($popularTopics) > 0)
                                            @foreach ($popularTopics as $topic)
                                                <tr>
                                                    <td>{{ $topic->topicname }}</td>
                                                    <td>{{ $topic->category }}</td>
                                                    <td>{{ $topic->details_count }}</td>
                                                    <td>{{ $topic->views }}</td>
                                                    <td>
                                                        <a href="{{ route('topics.show', $topic->id) }}"
                                                            class="btn btn-sm btn-info">
                                                            <i class="fa fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        @else
                                            <tr>
                                                <td colspan="5" class="text-center">No data available</td>
                                            </tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="tab-pane" id="chapters-tab">
                            <div class="table-responsive">
                                <table class="table table-hover table-centered mb-0">
                                    <thead>
                                        <tr>
                                            <th>Chapter Name</th>
                                            <th>Language</th>
                                            <th>Content Size</th>
                                            <th>Views</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if (isset($popularChapters) && count($popularChapters) > 0)
                                            @foreach ($popularChapters as $chapter)
                                                <tr>
                                                    <td>{{ $chapter->chaptername }}</td>
                                                    <td>{{ $chapter->language }}</td>
                                                    <td>{{ $chapter->content_size }}</td>
                                                    <td>{{ $chapter->views }}</td>
                                                    <td>
                                                        <a href="{{ route('chapts.show', $chapter->id) }}"
                                                            class="btn btn-sm btn-info">
                                                            <i class="fa fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        @else
                                            <tr>
                                                <td colspan="5" class="text-center">No data available</td>
                                            </tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div> <!-- end container-fluid -->
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Topics Distribution Chart
            var topicsCtx = document.getElementById('topics-chart').getContext('2d');
            var topicsChart = new Chart(topicsCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Faith', 'Worship', 'Ethics', 'History', 'Guidance', 'Others'],
                    datasets: [{
                        data: [30, 25, 20, 15, 7, 3],
                        backgroundColor: ['#5d6dc3', '#3ec396', '#f9bc0b', '#4fbde9', '#313a46',
                            '#9368f3'
                        ],
                        borderColor: '#fff',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    legend: {
                        position: 'bottom'
                    },
                    cutoutPercentage: 70
                }
            });

            // Language Distribution Chart
            var langCtx = document.getElementById('language-chart').getContext('2d');
            var langChart = new Chart(langCtx, {
                type: 'bar',
                data: {
                    labels: ['Arabic', 'English', 'Urdu', 'French', 'Spanish', 'Others'],
                    datasets: [{
                        label: 'Content Count',
                        data: [114, 95, 80, 45, 30, 20],
                        backgroundColor: '#3ec396',
                        borderColor: '#3ec396',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        yAxes: [{
                            ticks: {
                                beginAtZero: true
                            }
                        }]
                    }
                }
            });
        });
    </script>
@endsection
