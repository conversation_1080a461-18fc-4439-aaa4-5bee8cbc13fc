<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('quran.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item active">API Documentation</li>
                    </ol>
                </div>
                <h4 class="page-title">API Documentation</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="header-title">
                            <i class="mdi mdi-api text-primary"></i> Al-Quran API Documentation
                        </h4>
                        <div>
                            <a href="<?php echo e(route('api.test')); ?>" class="btn btn-primary" target="_blank">
                                <i class="mdi mdi-test-tube"></i> API Tester
                            </a>
                            <a href="<?php echo e(route('api.postman.collection')); ?>" class="btn btn-info">
                                <i class="mdi mdi-download"></i> Postman Collection
                            </a>
                            <a href="<?php echo e(route('api.quick.reference')); ?>" class="btn btn-success">
                                <i class="mdi mdi-file-document"></i> Quick Reference
                            </a>
                            <a href="<?php echo e(route('api.status')); ?>" class="btn btn-warning" target="_blank">
                                <i class="mdi mdi-heart-pulse"></i> API Status
                            </a>
                        </div>
                    </div>

                    <!-- API Status -->
                    <div class="alert alert-success" role="alert">
                        <h5 class="alert-heading">
                            <i class="mdi mdi-check-circle"></i> API Status: Operational
                        </h5>
                        <p class="mb-0">All API endpoints are working correctly. Last tested:
                            <?php echo e(now()->format('Y-m-d H:i:s')); ?></p>
                    </div>

                    <!-- Quick Stats -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <h3 class="text-primary">14</h3>
                                    <p class="text-muted mb-0">Total Endpoints</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-info">
                                <div class="card-body text-center">
                                    <h3 class="text-info">10</h3>
                                    <p class="text-muted mb-0">Quran Endpoints</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <h3 class="text-success">10</h3>
                                    <p class="text-muted mb-0">Languages Supported</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <h3 class="text-warning">4</h3>
                                    <p class="text-muted mb-0">Search Endpoints</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Access -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="mdi mdi-rocket"></i> Quick Start
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p>Get started with the API in seconds:</p>
                                    <div class="bg-dark text-light p-3 rounded">
                                        <code>
                                            // Get first ayah of Al-Fatiha<br>
                                            fetch('<?php echo e(url('/api/quran/surah/1/ayah/1')); ?>')<br>
                                            &nbsp;&nbsp;.then(r => r.json())<br>
                                            &nbsp;&nbsp;.then(data => console.log(data));
                                        </code>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="mdi mdi-earth"></i> Base URL
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p>All API endpoints use this base URL:</p>
                                    <div class="bg-primary text-white p-3 rounded">
                                        <code><?php echo e(url('/api')); ?></code>
                                    </div>
                                    <small class="text-muted">Copy this URL for use in your applications</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Endpoint Categories -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="mdi mdi-book-open"></i> Quran API</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled mb-0">
                                        <li>• Get all Quran data</li>
                                        <li>• Get specific surah/ayah</li>
                                        <li>• Search functionality</li>
                                        <li>• Multi-language translations</li>
                                        <li>• Tafsir access</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="mdi mdi-tag"></i> Topics API</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled mb-0">
                                        <li>• Get all topics</li>
                                        <li>• Search topics</li>
                                        <li>• Active topics only</li>
                                        <li>• Topic categorization</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0"><i class="mdi mdi-book"></i> Chapters API</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled mb-0">
                                        <li>• Get all chapters</li>
                                        <li>• Search chapters</li>
                                        <li>• Active chapters only</li>
                                        <li>• Chapter organization</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Documentation Content -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="mdi mdi-file-document-outline"></i> Complete Documentation
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="markdown-content" class="markdown-body">
                                <!-- Markdown content will be rendered here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.2.0/github-markdown-light.min.css">
    <style>
        .markdown-body {
            box-sizing: border-box;
            min-width: 200px;
            max-width: 980px;
            margin: 0 auto;
            padding: 45px;
        }

        .markdown-body pre {
            background-color: #f6f8fa;
            border-radius: 6px;
            padding: 16px;
            overflow: auto;
        }

        .markdown-body code {
            background-color: rgba(175, 184, 193, 0.2);
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-size: 85%;
        }

        .markdown-body table {
            border-collapse: collapse;
            width: 100%;
        }

        .markdown-body table th,
        .markdown-body table td {
            border: 1px solid #d0d7de;
            padding: 6px 13px;
        }

        .markdown-body table th {
            background-color: #f6f8fa;
            font-weight: 600;
        }

        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/4.3.0/marked.min.js"></script>
    <script>
        $(document).ready(function() {
            // Render markdown content
            const markdownContent = <?php echo json_encode($content); ?>;
            const htmlContent = marked.parse(markdownContent);
            document.getElementById('markdown-content').innerHTML = htmlContent;

            // Add syntax highlighting to code blocks
            document.querySelectorAll('pre code').forEach((block) => {
                block.style.backgroundColor = '#f6f8fa';
                block.style.color = '#24292e';
            });

            // Make tables responsive
            document.querySelectorAll('table').forEach((table) => {
                table.classList.add('table', 'table-striped', 'table-bordered');
                const wrapper = document.createElement('div');
                wrapper.classList.add('table-responsive');
                table.parentNode.insertBefore(wrapper, table);
                wrapper.appendChild(table);
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\alquran\resources\views/api/documentation.blade.php ENDPATH**/ ?>