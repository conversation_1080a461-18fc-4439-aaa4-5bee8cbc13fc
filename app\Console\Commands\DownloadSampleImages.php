<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;

class DownloadSampleImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'library:download-samples';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Download sample images for library items';

    /**
     * Sample image URLs for different media types
     */
    protected $sampleImages = [
        'pdf' => [
            'https://source.unsplash.com/random/400x300/?book,quran,islam',
            'https://source.unsplash.com/random/400x300/?book,reading,study',
            'https://source.unsplash.com/random/400x300/?library,books,education',
            'https://source.unsplash.com/random/400x300/?manuscript,document,paper',
            'https://source.unsplash.com/random/400x300/?text,writing,knowledge',
        ],
        'video' => [
            'https://source.unsplash.com/random/400x300/?video,film,movie',
            'https://source.unsplash.com/random/400x300/?camera,recording,cinema',
            'https://source.unsplash.com/random/400x300/?lecture,presentation,speech',
            'https://source.unsplash.com/random/400x300/?screen,display,monitor',
            'https://source.unsplash.com/random/400x300/?projector,theater,audience',
        ],
        'audio' => [
            'https://source.unsplash.com/random/400x300/?audio,sound,music',
            'https://source.unsplash.com/random/400x300/?podcast,radio,broadcast',
            'https://source.unsplash.com/random/400x300/?microphone,recording,studio',
            'https://source.unsplash.com/random/400x300/?headphones,listening,speaker',
            'https://source.unsplash.com/random/400x300/?voice,song,melody',
        ],
        'other' => [
            'https://source.unsplash.com/random/400x300/?document,file,folder',
            'https://source.unsplash.com/random/400x300/?archive,collection,resource',
            'https://source.unsplash.com/random/400x300/?data,information,content',
        ],
    ];

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Downloading sample images for library items...');
        
        // Create directories if they don't exist
        $thumbnailDir = public_path('samples/thumbnails');
        $filesDir = public_path('samples/files');
        
        if (!File::exists($thumbnailDir)) {
            File::makeDirectory($thumbnailDir, 0755, true);
        }
        
        if (!File::exists($filesDir)) {
            File::makeDirectory($filesDir, 0755, true);
        }
        
        // Download sample images for each media type
        foreach ($this->sampleImages as $type => $urls) {
            $this->info("Downloading {$type} images...");
            
            foreach ($urls as $index => $url) {
                try {
                    $response = Http::timeout(10)->get($url);
                    
                    if ($response->successful()) {
                        $filename = "{$type}_sample_{$index}.jpg";
                        File::put($thumbnailDir . '/' . $filename, $response->body());
                        $this->info("Downloaded {$filename}");
                    } else {
                        $this->warn("Failed to download from {$url}: " . $response->status());
                    }
                } catch (\Exception $e) {
                    $this->error("Error downloading from {$url}: " . $e->getMessage());
                }
                
                // Add a small delay to avoid rate limiting
                sleep(1);
            }
        }
        
        // Create a sample PDF file
        $this->info('Creating sample PDF file...');
        $this->createSamplePdf($filesDir, 'sample_document.pdf');
        
        $this->info('Sample images and files downloaded successfully!');
        
        return 0;
    }
    
    /**
     * Create a sample PDF file
     */
    private function createSamplePdf(string $dir, string $filename): void
    {
        // For simplicity, we'll just create a text file with .pdf extension
        $content = "This is a sample PDF document for testing purposes.\n\n";
        $content .= "Islamic Library System\n";
        $content .= "Sample Document\n\n";
        $content .= "This file was generated by the DownloadSampleImages command.";
        
        file_put_contents($dir . '/' . $filename, $content);
        $this->info("Created {$filename}");
    }
}