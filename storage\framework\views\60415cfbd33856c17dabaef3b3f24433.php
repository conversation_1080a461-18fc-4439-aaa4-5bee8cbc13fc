<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('quran.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item active">Users</li>
                    </ol>
                </div>
                <h4 class="page-title">User Management</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h4 class="header-title">
                                <i class="mdi mdi-account-multiple text-primary"></i> Users
                            </h4>
                        </div>
                        <div class="col-md-6 text-end">
                            <?php if(auth()->user()->hasPermission('users.create')): ?>
                                <a href="<?php echo e(route('users.create')); ?>" class="btn btn-primary">
                                    <i class="mdi mdi-plus"></i> Create User
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <form method="GET" action="<?php echo e(route('users.index')); ?>" class="row g-3">
                                <div class="col-md-4">
                                    <input type="text" name="search" class="form-control" placeholder="Search users..."
                                        value="<?php echo e(request('search')); ?>">
                                </div>
                                <div class="col-md-2">
                                    <select name="status" class="form-control">
                                        <option value="">All Status</option>
                                        <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Active
                                        </option>
                                        <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>
                                            Inactive</option>
                                        <option value="verified" <?php echo e(request('status') == 'verified' ? 'selected' : ''); ?>>
                                            Verified</option>
                                        <option value="unverified"
                                            <?php echo e(request('status') == 'unverified' ? 'selected' : ''); ?>>Unverified</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select name="role" class="form-control">
                                        <option value="">All Roles</option>
                                        <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($role->name); ?>"
                                                <?php echo e(request('role') == $role->name ? 'selected' : ''); ?>>
                                                <?php echo e($role->display_name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <button type="submit" class="btn btn-secondary">
                                        <i class="mdi mdi-filter"></i> Filter
                                    </button>
                                    <a href="<?php echo e(route('users.index')); ?>" class="btn btn-light">
                                        <i class="mdi mdi-refresh"></i> Reset
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Users Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Email</th>
                                    <th>Roles</th>
                                    <th>Last Login</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="<?php echo e($user->avatar_url); ?>" alt="<?php echo e($user->name); ?>"
                                                    class="rounded-circle me-2" width="40" height="40">
                                                <div>
                                                    <strong><?php echo e($user->full_name); ?></strong>
                                                    <?php if($user->username): ?>
                                                        <br><small class="text-muted">{{ $user - > username }}</small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?php echo e($user->email); ?></td>
                                        <td>
                                            <?php $__currentLoopData = $user->activeRoles->take(2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <span class="badge me-1"
                                                    style="background-color: <?php echo e($role->color); ?>; color: white;">
                                                    <?php echo e($role->display_name); ?>

                                                </span>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php if($user->activeRoles->count() > 2): ?>
                                                <span class="text-muted">+<?php echo e($user->activeRoles->count() - 2); ?> more</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($user->last_login_at): ?>
                                                <?php echo e($user->last_login_at->diffForHumans()); ?>

                                                <br><small
                                                    class="text-muted"><?php echo e($user->last_login_at->format('M j, Y g:i A')); ?></small>
                                            <?php else: ?>
                                                <span class="text-muted">Never</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo $user->status_badge; ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <?php if(auth()->user()->hasPermission('users.read')): ?>
                                                    <a href="<?php echo e(route('users.show', $user)); ?>" class="btn btn-sm btn-info">
                                                        <i class="mdi mdi-eye"></i>
                                                    </a>
                                                <?php endif; ?>

                                                <?php if(auth()->user()->hasPermission('users.update')): ?>
                                                    <a href="<?php echo e(route('users.edit', $user)); ?>"
                                                        class="btn btn-sm btn-warning">
                                                        <i class="mdi mdi-pencil"></i>
                                                    </a>
                                                <?php endif; ?>

                                                <?php if(auth()->user()->hasPermission('users.update') && $user->id !== auth()->id()): ?>
                                                    <button type="button"
                                                        class="btn btn-sm btn-<?php echo e($user->is_active ? 'secondary' : 'success'); ?>"
                                                        onclick="toggleStatus(<?php echo e($user->id); ?>)">
                                                        <i class="mdi mdi-<?php echo e($user->is_active ? 'pause' : 'play'); ?>"></i>
                                                    </button>
                                                <?php endif; ?>

                                                <?php if(auth()->user()->hasPermission('users.delete') && $user->id !== auth()->id()): ?>
                                                    <form method="POST" action="<?php echo e(route('users.destroy', $user)); ?>"
                                                        style="display: inline;">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="btn btn-sm btn-danger"
                                                            onclick="return confirm('Are you sure you want to delete this user?')">
                                                            <i class="mdi mdi-delete"></i>
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <i class="mdi mdi-account-outline h1 text-muted"></i>
                                            <h4 class="text-muted">No users found</h4>
                                            <p class="text-muted">No users match your current filters.</p>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <p class="text-muted">
                                Showing <?php echo e($users->firstItem() ?? 0); ?> to <?php echo e($users->lastItem() ?? 0); ?>

                                of <?php echo e($users->total()); ?> users
                            </p>
                        </div>
                        <div>
                            <?php echo e($users->appends(request()->query())->links()); ?>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script>
        function toggleStatus(userId) {
            if (confirm('Are you sure you want to change the status of this user?')) {
                fetch(`/users/${userId}/toggle-status`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            location.reload();
                        } else {
                            alert(data.error || 'An error occurred');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred while updating the user status');
                    });
            }
        }
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\alquran\resources\views/users/index.blade.php ENDPATH**/ ?>