<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Al-Quran API Tester</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
        }
        h2 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .endpoint {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .endpoint-title {
            font-weight: bold;
            color: #2980b9;
            margin-bottom: 10px;
        }
        .endpoint-url {
            background: #34495e;
            color: white;
            padding: 8px;
            border-radius: 3px;
            font-family: monospace;
            margin: 5px 0;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        .response {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .input-group {
            margin: 10px 0;
        }
        input, select {
            padding: 8px;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
            margin: 0 5px;
        }
        .status-success {
            color: #27ae60;
        }
        .status-error {
            color: #e74c3c;
        }
        .loading {
            color: #f39c12;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🕌 Al-Quran API Tester</h1>
        <p>Test the Al-Quran API endpoints directly from your browser.</p>
        
        <div class="input-group">
            <label>Base URL:</label>
            <input type="text" id="baseUrl" value="http://localhost:8000/api" style="width: 300px;">
        </div>
    </div>

    <div class="container">
        <h2>🕌 Quran Endpoints</h2>
        
        <div class="endpoint">
            <div class="endpoint-title">Get All Quran Data</div>
            <div class="endpoint-url">GET /quran</div>
            <button onclick="testEndpoint('/quran')">Test</button>
            <div id="response-quran" class="response" style="display: none;"></div>
        </div>

        <div class="endpoint">
            <div class="endpoint-title">Get Specific Surah</div>
            <div class="endpoint-url">GET /quran/surah/{number}</div>
            <input type="number" id="surahNumber" value="1" min="1" max="114" placeholder="Surah number">
            <button onclick="testSurah()">Test</button>
            <div id="response-surah" class="response" style="display: none;"></div>
        </div>

        <div class="endpoint">
            <div class="endpoint-title">Get Specific Ayah</div>
            <div class="endpoint-url">GET /quran/surah/{surah}/ayah/{ayah}</div>
            <input type="number" id="ayahSurah" value="1" min="1" max="114" placeholder="Surah">
            <input type="number" id="ayahNumber" value="1" min="1" placeholder="Ayah">
            <button onclick="testAyah()">Test</button>
            <div id="response-ayah" class="response" style="display: none;"></div>
        </div>

        <div class="endpoint">
            <div class="endpoint-title">Search Quran</div>
            <div class="endpoint-url">GET /quran/search?q={query}</div>
            <input type="text" id="searchQuery" value="Allah" placeholder="Search query">
            <button onclick="testSearch()">Test</button>
            <div id="response-search" class="response" style="display: none;"></div>
        </div>

        <div class="endpoint">
            <div class="endpoint-title">Get Translation</div>
            <div class="endpoint-url">GET /quran/translation/{surah}/{ayah}/{language}</div>
            <input type="number" id="transSurah" value="1" min="1" max="114" placeholder="Surah">
            <input type="number" id="transAyah" value="1" min="1" placeholder="Ayah">
            <select id="transLang">
                <option value="en">English</option>
                <option value="ur">Urdu</option>
                <option value="tr">Turkish</option>
                <option value="fr">French</option>
                <option value="es">Spanish</option>
                <option value="id">Indonesian</option>
                <option value="ru">Russian</option>
                <option value="sv">Swedish</option>
                <option value="bn">Bengali</option>
                <option value="zh">Chinese</option>
            </select>
            <button onclick="testTranslation()">Test</button>
            <div id="response-translation" class="response" style="display: none;"></div>
        </div>

        <div class="endpoint">
            <div class="endpoint-title">Get All Translations</div>
            <div class="endpoint-url">GET /quran/translations/{surah}/{ayah}</div>
            <input type="number" id="allTransSurah" value="1" min="1" max="114" placeholder="Surah">
            <input type="number" id="allTransAyah" value="1" min="1" placeholder="Ayah">
            <button onclick="testAllTranslations()">Test</button>
            <div id="response-all-translations" class="response" style="display: none;"></div>
        </div>

        <div class="endpoint">
            <div class="endpoint-title">Get Available Languages</div>
            <div class="endpoint-url">GET /quran/languages</div>
            <button onclick="testEndpoint('/quran/languages', 'languages')">Test</button>
            <div id="response-languages" class="response" style="display: none;"></div>
        </div>
    </div>

    <div class="container">
        <h2>📚 Topics Endpoints</h2>
        
        <div class="endpoint">
            <div class="endpoint-title">Get All Topics</div>
            <div class="endpoint-url">GET /topics</div>
            <button onclick="testEndpoint('/topics', 'topics')">Test</button>
            <div id="response-topics" class="response" style="display: none;"></div>
        </div>

        <div class="endpoint">
            <div class="endpoint-title">Search Topics</div>
            <div class="endpoint-url">GET /topics/search?q={query}</div>
            <input type="text" id="topicsSearchQuery" value="God" placeholder="Search query">
            <button onclick="testTopicsSearch()">Test</button>
            <div id="response-topics-search" class="response" style="display: none;"></div>
        </div>
    </div>

    <div class="container">
        <h2>📖 Chapters Endpoints</h2>
        
        <div class="endpoint">
            <div class="endpoint-title">Get All Chapters</div>
            <div class="endpoint-url">GET /chapters</div>
            <button onclick="testEndpoint('/chapters', 'chapters')">Test</button>
            <div id="response-chapters" class="response" style="display: none;"></div>
        </div>

        <div class="endpoint">
            <div class="endpoint-title">Search Chapters</div>
            <div class="endpoint-url">GET /chapters/search?q={query}</div>
            <input type="text" id="chaptersSearchQuery" value="Alif" placeholder="Search query">
            <button onclick="testChaptersSearch()">Test</button>
            <div id="response-chapters-search" class="response" style="display: none;"></div>
        </div>
    </div>

    <script>
        function getBaseUrl() {
            return document.getElementById('baseUrl').value;
        }

        async function testEndpoint(endpoint, responseId = null) {
            const id = responseId || endpoint.replace(/[^a-zA-Z0-9]/g, '-');
            const responseDiv = document.getElementById(`response-${id}`);
            
            responseDiv.style.display = 'block';
            responseDiv.innerHTML = '<span class="loading">Loading...</span>';
            
            try {
                const response = await fetch(getBaseUrl() + endpoint);
                const data = await response.json();
                
                const status = response.ok ? 'success' : 'error';
                const statusClass = response.ok ? 'status-success' : 'status-error';
                
                responseDiv.innerHTML = `<span class="${statusClass}">Status: ${response.status} ${response.statusText}</span>\n\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                responseDiv.innerHTML = `<span class="status-error">Error: ${error.message}</span>`;
            }
        }

        function testSurah() {
            const surahNumber = document.getElementById('surahNumber').value;
            testEndpoint(`/quran/surah/${surahNumber}`, 'surah');
        }

        function testAyah() {
            const surah = document.getElementById('ayahSurah').value;
            const ayah = document.getElementById('ayahNumber').value;
            testEndpoint(`/quran/surah/${surah}/ayah/${ayah}`, 'ayah');
        }

        function testSearch() {
            const query = document.getElementById('searchQuery').value;
            testEndpoint(`/quran/search?q=${encodeURIComponent(query)}`, 'search');
        }

        function testTranslation() {
            const surah = document.getElementById('transSurah').value;
            const ayah = document.getElementById('transAyah').value;
            const lang = document.getElementById('transLang').value;
            testEndpoint(`/quran/translation/${surah}/${ayah}/${lang}`, 'translation');
        }

        function testAllTranslations() {
            const surah = document.getElementById('allTransSurah').value;
            const ayah = document.getElementById('allTransAyah').value;
            testEndpoint(`/quran/translations/${surah}/${ayah}`, 'all-translations');
        }

        function testTopicsSearch() {
            const query = document.getElementById('topicsSearchQuery').value;
            testEndpoint(`/topics/search?q=${encodeURIComponent(query)}`, 'topics-search');
        }

        function testChaptersSearch() {
            const query = document.getElementById('chaptersSearchQuery').value;
            testEndpoint(`/chapters/search?q=${encodeURIComponent(query)}`, 'chapters-search');
        }
    </script>
</body>
</html>
