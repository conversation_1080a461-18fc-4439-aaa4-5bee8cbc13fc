<?php

namespace App\Http\Controllers;

use App\Models\LibraryItem;
use App\Services\ActivityLogger;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class LibraryController extends Controller
{
    public function index()
    {
        $items = LibraryItem::all();
        return view('library.index', compact('items'));
    }

    public function create()
    {
        return view('library.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'author' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'media_type' => 'required|in:pdf,video,audio,other',
            'thumbnail' => 'nullable|image|max:2048', // 2MB max
            'file' => 'nullable|file|max:20480', // 20MB max
            'external_url' => 'nullable|url',
            'duration' => 'nullable|string|max:10',
            'language' => 'nullable|string|max:50',
            'category' => 'nullable|string|max:50',
            'keywords' => 'nullable|string|max:255',
        ]);

        $isactive = $request->has('isactive') ? 1 : 0;

        // Initialize data array
        $data = [
            'title' => $request->title,
            'author' => $request->author,
            'description' => $request->description,
            'media_type' => $request->media_type,
            'external_url' => $request->external_url,
            'duration' => $request->duration,
            'language' => $request->language,
            'category' => $request->category,
            'isactive' => $isactive,
            'created_by' => Auth::user()->name ?? "Admin",
        ];

        // Handle thumbnail upload
        if ($request->hasFile('thumbnail')) {
            $thumbnail = $request->file('thumbnail');
            $thumbnailName = time() . '_thumb_' . $thumbnail->getClientOriginalName();
            $thumbnailPath = $thumbnail->storeAs('library/thumbnails', $thumbnailName, 'public');
            $data['thumbnail_path'] = $thumbnailPath;
        }

        // Handle file upload for PDFs, audios, etc.
        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $filename = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('library/' . $request->media_type, $filename, 'public');
            $data['file_path'] = $filePath;
            $data['file_size'] = $file->getSize();
        }

        $item = LibraryItem::create($data);

        // Log the creation activity
        ActivityLogger::logCreate($item);

        return redirect()->route('library.index')->with('success', 'Item added successfully!');
    }

    public function show(LibraryItem $library)
    {
        // Log the view activity
        ActivityLogger::logView($library);

        return view('library.show', compact('library'));
    }

    public function edit(LibraryItem $library)
    {
        return view('library.edit', compact('library'));
    }

    public function update(Request $request, LibraryItem $library)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'author' => 'nullable|string|max:255',
            'description' => 'required|string',
            'media_type' => 'required|in:pdf,video,audio,other',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'file' => 'nullable|file|max:20480', // 20MB max
            'external_url' => 'nullable|url',
            'duration' => 'nullable|string|max:50',
            'language' => 'nullable|string|max:50',
            'category' => 'nullable|string|max:100',
            'keywords' => 'nullable|string|max:255',
        ]);

        $library->title = $request->title;
        $library->author = $request->author;
        $library->description = $request->description;
        $library->media_type = $request->media_type;
        $library->external_url = $request->external_url;
        $library->duration = $request->duration;
        $library->language = $request->language;
        $library->category = $request->category;
        $library->keywords = $request->keywords;
        $library->isactive = $request->has('isactive') ? 1 : 0;
        $library->updated_by = Auth::user()->name ?? 'Admin';

        // Handle thumbnail upload
        if ($request->hasFile('thumbnail')) {
            // Delete old thumbnail if exists
            if ($library->thumbnail_path) {
                Storage::disk('public')->delete($library->thumbnail_path);
            }

            $thumbnailPath = $request->file('thumbnail')->store('library/thumbnails', 'public');
            $library->thumbnail_path = $thumbnailPath;
        }

        // Handle file upload
        if ($request->hasFile('file')) {
            // Delete old file if exists
            if ($library->file_path) {
                Storage::disk('public')->delete($library->file_path);
            }

            $file = $request->file('file');
            $fileSize = $file->getSize(); // Get file size in bytes
            $library->file_size = $fileSize;

            // Store in the appropriate directory based on media type
            $filePath = $file->store('library/' . $request->media_type, 'public');
            $library->file_path = $filePath;
        }

        $library->save();

        // Log the update activity
        ActivityLogger::logUpdate($library);

        return redirect()->route('library.index')
            ->with('success', 'Library item updated successfully.');
    }

    public function destroy(LibraryItem $library)
    {
        // Log the deletion activity before deleting
        ActivityLogger::logDelete($library);

        // Delete associated files
        if ($library->thumbnail_path) {
            Storage::disk('public')->delete($library->thumbnail_path);
        }

        if ($library->file_path) {
            Storage::disk('public')->delete($library->file_path);
        }

        $library->delete();

        return redirect()->route('library.index')
            ->with('success', 'Library item deleted successfully.');
    }
}
