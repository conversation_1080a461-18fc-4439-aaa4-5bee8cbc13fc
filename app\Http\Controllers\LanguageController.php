<?php

namespace App\Http\Controllers;

use Session;
use Carbon\Carbon;
use App\Models\Language;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LanguageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
        $langs=Language::all();
        return view('langs.langslst',compact('langs'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
        return view('langs.langsadd');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //

        $isactive=0;
        if($request->isactive==1)
            $isactive=1;

        $lang=new Language();
        $lang->name=$request->langname;
        $lang->code=$request->langcode;
        $lang->isactive=$isactive;
        $lang->created_by=Auth::user()->name ?? "Shahid";
        $lang->created_at=Now();
        $lang->save();

        return redirect()->route('langs.index')->with('success', 'Language added successfully!');

    }

    /**
     * Display the specified resource.
     */
    public function show(Language $language)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Language $lang)
    {
        //
        return view('langs.langsedit',compact('lang'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Language $lang)
    {
        //
        $isactive=0;
        if($request->isactive==1)
            $isactive=1;

        $lang = Language::find($lang->id);
        $lang->name = $request->langname;
        $lang->code = $request->langcode;
        $lang->isactive = $isactive;
        $lang->save();

        return redirect()->route('langs.index')->with('success', 'Record updated successfully!');

    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Language $lang)
    {
        //
        $i=$lang->delete();
        return redirect()->route('langs.index')->with('success', 'Record deleted successfully!');

    }
}
