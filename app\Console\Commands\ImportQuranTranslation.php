<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Quran;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\DB;

class ImportQuranTranslation extends Command
{
    protected $signature = 'import:quran-translation {language} {--directory=json-files}';
    protected $description = 'Import Quran translations in multiple languages from JSON files into the database';

    public function handle()
    {
        $language = $this->argument('language'); // en, ur, tr, etc.
        $directory = $this->option('directory');

        $column = match ($language) {
            'bn' => 'translation_bn', //Bengali
            'zh' => 'translation_zh', //Chinese
            'en' => 'translation_en', //English
            'es' => 'translation_es', //Spanish
            'fr' => 'translation_fr', //French
            'id' => 'translation_id', //Indonesian
            'ru' => 'translation_ru', //Russian
            'sv' => 'translation_sv', //Swedish
            'tr' => 'translation_tr', //Turkish
            'ur' => 'translation_ur', //Urdu
            default => null,
        };

        if (!$column) {
            $this->error('Invalid language. Supported: bn, zh, en, es, fr, id, ru, sv, tr, ur');
            return 1;
        }

        // Check if directory exists
        if (!File::isDirectory($directory)) {
            $this->error("Directory not found: $directory");
            return 1;
        }

        // Find the JSON file for the specified language
        $jsonFiles = File::glob("$directory/*$language*.json");

        if (empty($jsonFiles)) {
            $this->error("No JSON files found for language '$language' in directory '$directory'");
            return 1;
        }

        $filePath = $jsonFiles[0];
        $this->info("Using file: $filePath");

        $this->info("Reading Quran Translation JSON file for language: $language...");
        $translationData = json_decode(File::get($filePath), true);

        if (!$translationData) {
            $this->error('Invalid JSON format.');
            return 1;
        }

        // Debug the JSON structure
        $firstKey = array_key_first($translationData);
        $this->info("First key: $firstKey");
        $this->info("First value type: " . gettype($translationData[$firstKey]));

        if (is_array($translationData[$firstKey])) {
            $this->info("First value keys: " . json_encode(array_keys($translationData[$firstKey])));
        }

        $this->info("Saving Quran Translation ($language) into database...");
        $bar = $this->output->createProgressBar(count($translationData));
        $bar->start();

        DB::beginTransaction();
        try {
            // Determine the format of the JSON file
            $firstKey = array_key_first($translationData);
            $isChapterVerseFormat = strpos($firstKey, ':') !== false;

            if ($isChapterVerseFormat) {
                // Format: "1:1": {"translation": "text"}
                foreach ($translationData as $ayahKey => $ayahInfo) {
                    [$surah, $ayah] = explode(':', $ayahKey);

                    // Look for translation in various possible fields
                    $translationText = null;
                    if (isset($ayahInfo['translation'])) {
                        $translationText = $ayahInfo['translation'];
                    } elseif (isset($ayahInfo['text'])) {
                        $translationText = $ayahInfo['text'];
                    } elseif (is_string($ayahInfo)) {
                        $translationText = $ayahInfo;
                    }

                    if ($translationText === null) {
                        $this->warn("No translation found for $ayahKey: " . json_encode($ayahInfo));
                        $bar->advance();
                        continue;
                    }

                    if (strlen($translationText) > 65000) {
                        $translationText = substr($translationText, 0, 65000);
                    }

                    // Debug the update
                    $this->info("Updating surah $surah, ayah $ayah with translation: " . substr($translationText, 0, 30) . "...");

                    Quran::where('surah_number', $surah)
                        ->where('ayah_number', $ayah)
                        ->update([$column => $translationText]);

                    $bar->advance();
                }
            } else {
                // Format: [{"id": 1, "name": "Al-Fatiha", "verses": [{"id": 1, "text": "..."}]}]
                foreach ($translationData as $surahIndex => $surah) {
                    $surahNumber = $surah['id'] ?? ($surahIndex + 1);

                    if (!isset($surah['verses']) || !is_array($surah['verses'])) {
                        $this->warn("No verses found for surah $surahNumber");
                        continue;
                    }

                    foreach ($surah['verses'] as $verseIndex => $verse) {
                        $ayahNumber = $verse['id'] ?? ($verseIndex + 1);

                        // Look for translation in various possible fields
                        $translationText = null;
                        if (isset($verse['translation'])) {
                            $translationText = $verse['translation'];
                        } elseif (isset($verse['text'])) {
                            $translationText = $verse['text'];
                        }

                        if ($translationText === null) {
                            $this->warn("No translation found for surah $surahNumber, ayah $ayahNumber: " . json_encode($verse));
                            continue;
                        }

                        if (strlen($translationText) > 65000) {
                            $translationText = substr($translationText, 0, 65000);
                        }

                        // Debug the update
                        $this->info("Updating surah $surahNumber, ayah $ayahNumber with translation: " . substr($translationText, 0, 30) . "...");

                        Quran::where('surah_number', $surahNumber)
                            ->where('ayah_number', $ayahNumber)
                            ->update([$column => $translationText]);

                        $bar->advance();
                    }
                }
            }

            DB::commit();
            $bar->finish();
            $this->newLine();
            $this->info("Quran Translation ($language) imported successfully!");
            return 0;
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Error importing translation: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
            return 1;
        }
    }
}
