<?php

namespace App\Http\Controllers;

use App\Models\Permission;
use Illuminate\Http\Request;
use App\Services\ActivityLogger;

class PermissionManagementController extends Controller
{
    /**
     * Display a listing of permissions
     */
    public function index(Request $request)
    {
        // Get all permissions with roles for DataTables
        $permissions = Permission::with('roles')->ordered()->get();

        // Get filter options
        $modules = Permission::distinct()->pluck('module')->sort();
        $actions = Permission::distinct()->pluck('action')->sort();

        return view('permissions.index', compact('permissions', 'modules', 'actions'));
    }

    /**
     * Show the form for creating a new permission
     */
    public function create()
    {
        $modules = $this->getAvailableModules();
        $actions = $this->getAvailableActions();

        return view('permissions.create', compact('modules', 'actions'));
    }

    /**
     * Store a newly created permission
     */
    public function store(Request $request)
    {
        $request->validate([
            'display_name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'module' => ['required', 'string', 'max:50'],
            'action' => ['required', 'string', 'max:50'],
            'resource' => ['nullable', 'string', 'max:100'],
            'is_active' => ['boolean'],
            'sort_order' => ['integer', 'min:0'],
        ]);

        // Generate permission name
        $name = Permission::generateName(
            $request->module,
            $request->action,
            $request->resource
        );

        // Check if permission already exists
        if (Permission::where('name', $name)->exists()) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Permission with this combination already exists.');
        }

        $permissionData = $request->all();
        $permissionData['name'] = $name;
        $permissionData['is_system'] = false; // Custom permissions are never system permissions

        $permission = Permission::create($permissionData);

        // Log activity
        ActivityLogger::log('permission_create', "Created permission: {$permission->display_name}", $permission, [
            'permission_id' => $permission->id,
            'permission_name' => $permission->name,
        ]);

        return redirect()->route('permissions.index')
            ->with('success', 'Permission created successfully.');
    }

    /**
     * Display the specified permission
     */
    public function show(Permission $permission)
    {
        $permission->load(['roles' => function ($query) {
            $query->ordered();
        }]);

        return view('permissions.show', compact('permission'));
    }

    /**
     * Show the form for editing the specified permission
     */
    public function edit(Permission $permission)
    {
        $modules = $this->getAvailableModules();
        $actions = $this->getAvailableActions();

        return view('permissions.edit', compact('permission', 'modules', 'actions'));
    }

    /**
     * Update the specified permission
     */
    public function update(Request $request, Permission $permission)
    {
        $request->validate([
            'display_name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'module' => ['required', 'string', 'max:50'],
            'action' => ['required', 'string', 'max:50'],
            'resource' => ['nullable', 'string', 'max:100'],
            'is_active' => ['boolean'],
            'sort_order' => ['integer', 'min:0'],
        ]);

        // Prevent editing system permission core properties
        $updateData = $request->all();
        if ($permission->is_system) {
            $updateData = $request->only(['description', 'sort_order']);
        } else {
            // Generate new permission name for custom permissions
            $newName = Permission::generateName(
                $request->module,
                $request->action,
                $request->resource
            );

            // Check if new name conflicts with existing permission
            if ($newName !== $permission->name && Permission::where('name', $newName)->exists()) {
                return redirect()->back()
                    ->withInput()
                    ->with('error', 'Permission with this combination already exists.');
            }

            $updateData['name'] = $newName;
        }

        $permission->update($updateData);

        // Log activity
        ActivityLogger::log('permission_update', "Updated permission: {$permission->display_name}", $permission, [
            'permission_id' => $permission->id,
            'permission_name' => $permission->name,
        ]);

        return redirect()->route('permissions.index')
            ->with('success', 'Permission updated successfully.');
    }

    /**
     * Remove the specified permission
     */
    public function destroy(Permission $permission)
    {
        // Prevent deletion of system permissions
        if ($permission->is_system) {
            return redirect()->route('permissions.index')
                ->with('error', 'System permissions cannot be deleted.');
        }

        // Check if permission is assigned to any roles
        if ($permission->roles()->count() > 0) {
            return redirect()->route('permissions.index')
                ->with('error', 'Cannot delete permission that is assigned to roles.');
        }

        // Log activity before deletion
        ActivityLogger::log('permission_delete', "Deleted permission: {$permission->display_name}", $permission, [
            'permission_id' => $permission->id,
            'permission_name' => $permission->name,
        ]);

        $permission->delete();

        return redirect()->route('permissions.index')
            ->with('success', 'Permission deleted successfully.');
    }

    /**
     * Toggle permission status
     */
    public function toggleStatus(Permission $permission)
    {
        // Prevent deactivating system permissions
        if ($permission->is_system && $permission->is_active) {
            return response()->json(['error' => 'System permissions cannot be deactivated.'], 400);
        }

        $permission->update(['is_active' => !$permission->is_active]);

        $status = $permission->is_active ? 'activated' : 'deactivated';

        // Log activity
        ActivityLogger::log('permission_status_change', "Permission {$status}: {$permission->display_name}", $permission, [
            'permission_id' => $permission->id,
            'new_status' => $permission->is_active,
        ]);

        return response()->json([
            'success' => true,
            'message' => "Permission {$status} successfully.",
            'status' => $permission->is_active,
        ]);
    }

    /**
     * Bulk create permissions for a module
     */
    public function bulkCreate()
    {
        $modules = $this->getAvailableModules();
        $actions = $this->getAvailableActions();

        return view('permissions.bulk-create', compact('modules', 'actions'));
    }

    /**
     * Store bulk created permissions
     */
    public function storeBulk(Request $request)
    {
        $request->validate([
            'module' => ['required', 'string', 'max:50'],
            'actions' => ['required', 'array', 'min:1'],
            'actions.*' => ['string', 'max:50'],
            'resource' => ['nullable', 'string', 'max:100'],
            'is_active' => ['boolean'],
        ]);

        $created = 0;
        $skipped = 0;

        foreach ($request->actions as $action) {
            $name = Permission::generateName(
                $request->module,
                $action,
                $request->resource
            );

            // Skip if permission already exists
            if (Permission::where('name', $name)->exists()) {
                $skipped++;
                continue;
            }

            $displayName = ucfirst($action) . ' ' . ucfirst($request->module);
            if ($request->resource) {
                $displayName .= ' ' . ucfirst($request->resource);
            }

            Permission::create([
                'name' => $name,
                'display_name' => $displayName,
                'description' => "Permission to {$action} {$request->module}" . ($request->resource ? " {$request->resource}" : ''),
                'module' => $request->module,
                'action' => $action,
                'resource' => $request->resource,
                'is_system' => false,
                'is_active' => $request->boolean('is_active', true),
                'sort_order' => 0,
            ]);

            $created++;
        }

        // Log activity
        ActivityLogger::log('permission_bulk_create', "Bulk created {$created} permissions for module: {$request->module}", null, [
            'module' => $request->module,
            'actions' => $request->actions,
            'created_count' => $created,
            'skipped_count' => $skipped,
        ]);

        $message = "Created {$created} permissions successfully.";
        if ($skipped > 0) {
            $message .= " Skipped {$skipped} existing permissions.";
        }

        return redirect()->route('permissions.index')
            ->with('success', $message);
    }

    /**
     * Get available modules
     */
    private function getAvailableModules(): array
    {
        return [
            'users' => 'Users',
            'roles' => 'Roles',
            'permissions' => 'Permissions',
            'quran' => 'Quran',
            'topics' => 'Topics',
            'chapters' => 'Chapters',
            'books' => 'Books',
            'library' => 'Library',
            'languages' => 'Languages',
            'backup' => 'Backup',
            'audit' => 'Audit',
            'system' => 'System',
            'api' => 'API',
        ];
    }

    /**
     * Get available actions
     */
    private function getAvailableActions(): array
    {
        return [
            'create' => 'Create',
            'read' => 'Read',
            'update' => 'Update',
            'delete' => 'Delete',
            'manage' => 'Manage',
            'export' => 'Export',
            'import' => 'Import',
            'restore' => 'Restore',
            'backup' => 'Backup',
            'view' => 'View',
            'list' => 'List',
        ];
    }
}
