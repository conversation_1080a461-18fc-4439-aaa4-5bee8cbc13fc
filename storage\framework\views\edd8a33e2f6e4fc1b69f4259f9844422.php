<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('quran.dashboard')); ?>">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo e(route('user-activities.index')); ?>">User Activities</a></li>
                    <li class="breadcrumb-item active">Activity Dashboard</li>
                </ol>
            </div>
            <h4 class="page-title">Activity Dashboard</h4>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row">
    <div class="col-md-3">
        <div class="card widget-flat">
            <div class="card-body">
                <div class="float-right">
                    <i class="mdi mdi-account-clock widget-icon bg-success-lighten text-success"></i>
                </div>
                <h5 class="text-muted font-weight-normal mt-0" title="Total Activities">Total Activities</h5>
                <h3 class="mt-3 mb-3"><?php echo e(number_format($stats['total'])); ?></h3>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card widget-flat">
            <div class="card-body">
                <div class="float-right">
                    <i class="mdi mdi-calendar-today widget-icon bg-info-lighten text-info"></i>
                </div>
                <h5 class="text-muted font-weight-normal mt-0" title="Today">Today</h5>
                <h3 class="mt-3 mb-3"><?php echo e(number_format($stats['today'])); ?></h3>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card widget-flat">
            <div class="card-body">
                <div class="float-right">
                    <i class="mdi mdi-calendar-week widget-icon bg-warning-lighten text-warning"></i>
                </div>
                <h5 class="text-muted font-weight-normal mt-0" title="This Week">This Week</h5>
                <h3 class="mt-3 mb-3"><?php echo e(number_format($stats['this_week'])); ?></h3>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card widget-flat">
            <div class="card-body">
                <div class="float-right">
                    <i class="mdi mdi-calendar-month widget-icon bg-primary-lighten text-primary"></i>
                </div>
                <h5 class="text-muted font-weight-normal mt-0" title="This Month">This Month</h5>
                <h3 class="mt-3 mb-3"><?php echo e(number_format($stats['this_month'])); ?></h3>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Top Activity Types -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title">Top Activity Types</h4>
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>Activity Type</th>
                                <th>Count</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $stats['by_type']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <span class="badge badge-soft-primary">
                                            <?php echo e(ucfirst(str_replace('_', ' ', $stat->activity_type))); ?>

                                        </span>
                                    </td>
                                    <td><?php echo e(number_format($stat->count)); ?></td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="2" class="text-center">No data available</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Subject Types -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title">Top Subject Types</h4>
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>Subject Type</th>
                                <th>Count</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $stats['by_subject']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <span class="badge badge-soft-info">
                                            <?php echo e(class_basename($stat->subject_type)); ?>

                                        </span>
                                    </td>
                                    <td><?php echo e(number_format($stat->count)); ?></td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="2" class="text-center">No data available</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Most Active Users -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title">Most Active Users</h4>
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Activities</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $stats['top_users']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <?php if($stat->user): ?>
                                            <span class="badge badge-soft-primary"><?php echo e($stat->user->name); ?></span>
                                        <?php else: ?>
                                            <span class="badge badge-soft-secondary">Unknown User</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e(number_format($stat->count)); ?></td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="2" class="text-center">No data available</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Logins -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title">Recent Logins</h4>
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Time</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $stats['recent_logins']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $login): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <?php if($login->user): ?>
                                            <span class="badge badge-soft-success"><?php echo e($login->user->name); ?></span>
                                        <?php else: ?>
                                            <span class="badge badge-soft-secondary">Unknown User</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small><?php echo e($login->created_at->format('M d, H:i')); ?></small>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="2" class="text-center">No recent logins</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title">Recent Activities</h4>
                <p class="text-muted font-13 mb-4">
                    Latest 20 user activities across the system.
                </p>

                <div class="table-responsive">
                    <table class="table table-striped table-centered mb-0">
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>User</th>
                                <th>Activity</th>
                                <th>Description</th>
                                <th>IP</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $recentActivities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <small><?php echo e($activity->created_at->format('H:i:s')); ?></small>
                                        <br>
                                        <small class="text-muted"><?php echo e($activity->created_at->format('M d')); ?></small>
                                    </td>
                                    <td>
                                        <?php if($activity->user): ?>
                                            <span class="badge badge-soft-primary"><?php echo e($activity->user->name); ?></span>
                                        <?php else: ?>
                                            <span class="badge badge-soft-secondary">Guest</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge badge-soft-<?php echo e($activity->activity_type == 'login' ? 'success' : 
                                            ($activity->activity_type == 'logout' ? 'warning' : 
                                            ($activity->activity_type == 'delete' ? 'danger' : 'info'))); ?>">
                                            <?php echo e($activity->formatted_activity_type); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <span class="text-muted"><?php echo e(Str::limit($activity->description, 40)); ?></span>
                                    </td>
                                    <td>
                                        <small class="text-muted"><?php echo e($activity->ip_address); ?></small>
                                    </td>
                                    <td>
                                        <a href="<?php echo e(route('user-activities.show', $activity)); ?>" class="btn btn-xs btn-outline-primary">
                                            <i class="mdi mdi-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="6" class="text-center">No recent activities found.</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <div class="mt-3 text-center">
                    <a href="<?php echo e(route('user-activities.index')); ?>" class="btn btn-primary">
                        View All Activities
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\alquran\resources\views/user-activities/dashboard.blade.php ENDPATH**/ ?>