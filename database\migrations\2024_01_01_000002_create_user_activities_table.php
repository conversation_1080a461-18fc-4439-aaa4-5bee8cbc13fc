<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_activities', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->string('activity_type'); // login, logout, view, download, etc.
            $table->string('description'); // Human readable description
            $table->string('subject_type')->nullable(); // Related model class
            $table->unsignedBigInteger('subject_id')->nullable(); // Related model ID
            $table->json('properties')->nullable(); // Additional data
            $table->string('method')->nullable(); // HTTP method
            $table->string('url')->nullable(); // Request URL
            $table->string('ip_address', 45)->nullable(); // IP address
            $table->string('user_agent')->nullable(); // User agent
            $table->string('session_id')->nullable(); // Session ID
            $table->timestamp('created_at')->useCurrent();

            // Foreign key constraint
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');

            // Indexes for better performance
            $table->index('user_id');
            $table->index('activity_type');
            $table->index(['subject_type', 'subject_id']);
            $table->index('created_at');
            $table->index('ip_address');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_activities');
    }
};
