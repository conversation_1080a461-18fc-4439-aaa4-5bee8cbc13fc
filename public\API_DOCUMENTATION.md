# 🕌 Al-Quran API Documentation

## 📋 Table of Contents

-   [Overview](#overview)
-   [Base URL](#base-url)
-   [Authentication](#authentication)
-   [Response Format](#response-format)
-   [Error Handling](#error-handling)
-   [Quran API Endpoints](#quran-api-endpoints)
-   [Topics API Endpoints](#topics-api-endpoints)
-   [Chapters API Endpoints](#chapters-api-endpoints)
-   [Language Support](#language-support)
-   [Rate Limiting](#rate-limiting)
-   [Examples](#examples)

## 🌟 Overview

The Al-Quran API provides access to:

-   Complete Quran text in Arabic
-   Translations in 10+ languages
-   Search functionality across all translations
-   Tafsir (commentary) where available
-   Topics and chapters management
-   Multi-language support

## 🔗 Base URL

```
http://your-domain.com/api
```

## 🔐 Authentication

Currently, most endpoints are **public** and don't require authentication. Some endpoints may require authentication in the future.

## 📊 Response Format

All API responses are in JSON format:

```json
{
    "data": "...",
    "status": "success|error",
    "message": "..."
}
```

## ❌ Error Handling

| Status Code | Description                        |
| ----------- | ---------------------------------- |
| 200         | Success                            |
| 400         | Bad Request - Invalid parameters   |
| 404         | Not Found - Resource doesn't exist |
| 500         | Internal Server Error              |

---

## 🕌 Quran API Endpoints

### 1. Get All Quran Data

**Endpoint:** `GET /api/quran`

**Description:** Returns all Quran verses with translations.

**Response:**

```json
[
    {
        "id": 1,
        "surah_number": 1,
        "surah_name": "الفاتحة",
        "ayah_number": 1,
        "ayah_text": "بِسۡمِ ٱللَّهِ ٱلرَّحۡمَٰنِ ٱلرَّحِيمِ",
        "translation_en": "In the name of Allah, the Beneficent, the Merciful.",
        "translation_ur": "اللہ کے نام سے جو بڑا مہربان نہایت رحم والا ہے",
        "tafsir": "..."
    }
]
```

### 2. Get Specific Surah

**Endpoint:** `GET /api/quran/surah/{number}`

**Parameters:**

-   `number` (required): Surah number (1-114)

**Example:** `GET /api/quran/surah/1`

**Response:** Array of all ayahs in the specified surah.

### 3. Get Specific Ayah

**Endpoint:** `GET /api/quran/surah/{surah}/ayah/{ayah}`

**Parameters:**

-   `surah` (required): Surah number (1-114)
-   `ayah` (required): Ayah number

**Example:** `GET /api/quran/surah/1/ayah/1`

**Response:**

```json
{
    "id": 1,
    "surah_number": 1,
    "ayah_number": 1,
    "ayah_text": "بِسۡمِ ٱللَّهِ ٱلرَّحۡمَٰنِ ٱلرَّحِيمِ",
    "translation_en": "In the name of Allah, the Beneficent, the Merciful.",
    "translation_ur": "اللہ کے نام سے جو بڑا مہربان نہایت رحم والا ہے"
}
```

### 4. Search Quran

**Endpoint:** `GET /api/quran/search`

**Parameters:**

-   `q` (required): Search query

**Example:** `GET /api/quran/search?q=Allah`

**Response:** Array of matching ayahs (limited to 50 results).

### 5. Get Ayah with Language

**Endpoint:** `GET /api/quran/ayah/{surah}/{ayah}/{language?}`

**Parameters:**

-   `surah` (required): Surah number
-   `ayah` (required): Ayah number
-   `language` (optional): Language code (default: 'en')

**Example:** `GET /api/quran/ayah/1/1/ur`

**Response:**

```json
{
    "surah_number": 1,
    "ayah_number": 1,
    "ayah_text": "بِسۡمِ ٱللَّهِ ٱلرَّحۡمَٰنِ ٱلرَّحِيمِ",
    "translation": "اللہ کے نام سے جو بڑا مہربان نہایت رحم والا ہے"
}
```

### 6. Get All Translations for Ayah

**Endpoint:** `GET /api/quran/translations/{surah}/{ayah}`

**Parameters:**

-   `surah` (required): Surah number
-   `ayah` (required): Ayah number

**Example:** `GET /api/quran/translations/1/1`

**Response:**

```json
{
    "surah_number": 1,
    "ayah_number": 1,
    "translations": {
        "arabic": "بِسۡمِ ٱللَّهِ ٱلرَّحۡمَٰنِ ٱلرَّحِيمِ",
        "en": "In the name of Allah, the Beneficent, the Merciful.",
        "ur": "اللہ کے نام سے جو بڑا مہربان نہایت رحم والا ہے",
        "tr": "Rahman ve Rahim olan Allah'ın adıyla",
        "fr": "Au nom d'Allah, le Tout Miséricordieux, le Très Miséricordieux"
    },
    "tafsir": "..."
}
```

### 7. Get Specific Translation

**Endpoint:** `GET /api/quran/translation/{surah}/{ayah}/{language}`

**Parameters:**

-   `surah` (required): Surah number
-   `ayah` (required): Ayah number
-   `language` (required): Language code

**Example:** `GET /api/quran/translation/1/1/fr`

**Response:**

```json
{
    "surah_number": 1,
    "ayah_number": 1,
    "arabic": "بِسۡمِ ٱللَّهِ ٱلرَّحۡمَٰنِ ٱلرَّحِيمِ",
    "translation": "Au nom d'Allah, le Tout Miséricordieux, le Très Miséricordieux",
    "language": "fr"
}
```

### 8. Get Tafsir

**Endpoint:** `GET /api/quran/tafsir/{surah}/{ayah}`

**Parameters:**

-   `surah` (required): Surah number
-   `ayah` (required): Ayah number

**Example:** `GET /api/quran/tafsir/1/1`

**Response:**

```json
{
    "surah_number": 1,
    "ayah_number": 1,
    "ayah_text": "بِسۡمِ ٱللَّهِ ٱلرَّحۡمَٰنِ ٱلرَّحِيمِ",
    "ayah_translation": "In the name of Allah, the Beneficent, the Merciful.",
    "tafsir": "This is the opening verse of the Quran..."
}
```

**Note:** Returns 404 if tafsir is not available for the ayah.

### 9. Get Tafsir with Language

**Endpoint:** `GET /api/quran/tafsir/{surah}/{ayah}/{language?}`

**Parameters:**

-   `surah` (required): Surah number
-   `ayah` (required): Ayah number
-   `language` (optional): Language code (default: 'en')

**Example:** `GET /api/quran/tafsir/1/1/ur`

### 10. Get Available Languages

**Endpoint:** `GET /api/quran/languages`

**Response:**

```json
{
    "available_languages": {
        "bn": "Bengali",
        "zh": "Chinese",
        "en": "English",
        "es": "Spanish",
        "fr": "French",
        "id": "Indonesian",
        "ru": "Russian",
        "sv": "Swedish",
        "tr": "Turkish",
        "ur": "Urdu"
    }
}
```

---

## 📚 Topics API Endpoints

### 1. Get All Topics

**Endpoint:** `GET /api/topics`

**Description:** Returns all active topics.

**Response:**

```json
[
    {
        "id": 1,
        "topicname": "God",
        "topiccode": "0001",
        "language_id": 1,
        "isactive": 1,
        "created_at": null,
        "created_by": null
    }
]
```

### 2. Search Topics

**Endpoint:** `GET /api/topics/search`

**Parameters:**

-   `q` (required): Search query

**Example:** `GET /api/topics/search?q=God`

**Response:** Array of matching topics.

---

## 📖 Chapters API Endpoints

### 1. Get All Chapters

**Endpoint:** `GET /api/chapters`

**Description:** Returns all active chapters.

**Response:**

```json
[
    {
        "id": 1,
        "chaptername": "Alif laam meem(آلم )",
        "isactive": 1,
        "created_at": "2025-02-01T08:34:40.000000Z",
        "created_by": "Shahid"
    }
]
```

### 2. Search Chapters

**Endpoint:** `GET /api/chapters/search`

**Parameters:**

-   `q` (required): Search query

**Example:** `GET /api/chapters/search?q=Alif`

**Response:** Array of matching chapters.

---

## 🌍 Language Support

The API supports the following languages for translations:

| Code | Language   | Native Name      |
| ---- | ---------- | ---------------- |
| `bn` | Bengali    | বাংলা            |
| `zh` | Chinese    | 中文             |
| `en` | English    | English          |
| `es` | Spanish    | Español          |
| `fr` | French     | Français         |
| `id` | Indonesian | Bahasa Indonesia |
| `ru` | Russian    | Русский          |
| `sv` | Swedish    | Svenska          |
| `tr` | Turkish    | Türkçe           |
| `ur` | Urdu       | اردو             |

---

## ⚡ Rate Limiting

Currently, there are no rate limits applied. However, search results are limited to 50 items for performance.

**Recommendations:**

-   Cache responses when possible
-   Use specific endpoints instead of fetching all data
-   Implement client-side pagination for large datasets

---

## 📝 Examples

### JavaScript/Fetch Examples

#### Get Surah Al-Fatiha

```javascript
fetch("http://your-domain.com/api/quran/surah/1")
    .then((response) => response.json())
    .then((data) => {
        console.log("Surah Al-Fatiha:", data);
    })
    .catch((error) => console.error("Error:", error));
```

#### Search for "Allah"

```javascript
fetch("http://your-domain.com/api/quran/search?q=Allah")
    .then((response) => response.json())
    .then((data) => {
        console.log("Search results:", data);
    })
    .catch((error) => console.error("Error:", error));
```

#### Get Ayah in Urdu

```javascript
fetch("http://your-domain.com/api/quran/translation/1/1/ur")
    .then((response) => response.json())
    .then((data) => {
        console.log("Urdu translation:", data.translation);
    })
    .catch((error) => console.error("Error:", error));
```

### cURL Examples

#### Get All Topics

```bash
curl -X GET "http://your-domain.com/api/topics" \
  -H "Accept: application/json"
```

#### Search Chapters

```bash
curl -X GET "http://your-domain.com/api/chapters/search?q=Alif" \
  -H "Accept: application/json"
```

#### Get Available Languages

```bash
curl -X GET "http://your-domain.com/api/quran/languages" \
  -H "Accept: application/json"
```

### Python/Requests Examples

#### Get Specific Ayah

```python
import requests

response = requests.get('http://your-domain.com/api/quran/surah/1/ayah/1')
if response.status_code == 200:
    ayah = response.json()
    print(f"Arabic: {ayah['ayah_text']}")
    print(f"English: {ayah['translation_en']}")
else:
    print(f"Error: {response.status_code}")
```

#### Search with Error Handling

```python
import requests

def search_quran(query):
    try:
        response = requests.get(f'http://your-domain.com/api/quran/search?q={query}')
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error searching: {e}")
        return None

results = search_quran("Allah")
if results:
    print(f"Found {len(results)} results")
```

### PHP Examples

#### Get All Translations for an Ayah

```php
<?php
$url = 'http://your-domain.com/api/quran/translations/1/1';
$response = file_get_contents($url);
$data = json_decode($response, true);

if ($data) {
    echo "Arabic: " . $data['translations']['arabic'] . "\n";
    echo "English: " . $data['translations']['en'] . "\n";
    echo "Urdu: " . $data['translations']['ur'] . "\n";
}
?>
```

#### Search Topics

```php
<?php
function searchTopics($query) {
    $url = 'http://your-domain.com/api/topics/search?q=' . urlencode($query);
    $response = file_get_contents($url);
    return json_decode($response, true);
}

$topics = searchTopics('God');
foreach ($topics as $topic) {
    echo $topic['topicname'] . " (" . $topic['topiccode'] . ")\n";
}
?>
```

---

## 🔧 Integration Tips

### 1. Error Handling

Always check for HTTP status codes and handle errors gracefully:

```javascript
async function getAyah(surah, ayah) {
    try {
        const response = await fetch(`/api/quran/surah/${surah}/ayah/${ayah}`);

        if (!response.ok) {
            if (response.status === 404) {
                throw new Error("Ayah not found");
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error("Failed to fetch ayah:", error);
        throw error;
    }
}
```

### 2. Caching

Implement caching for better performance:

```javascript
const cache = new Map();

async function getCachedSurah(surahNumber) {
    const cacheKey = `surah_${surahNumber}`;

    if (cache.has(cacheKey)) {
        return cache.get(cacheKey);
    }

    const response = await fetch(`/api/quran/surah/${surahNumber}`);
    const data = await response.json();

    cache.set(cacheKey, data);
    return data;
}
```

### 3. Pagination for Large Results

For search results, implement pagination:

```javascript
async function searchWithPagination(query, page = 1, limit = 10) {
    const allResults = await fetch(`/api/quran/search?q=${query}`).then((r) =>
        r.json()
    );

    const start = (page - 1) * limit;
    const end = start + limit;

    return {
        data: allResults.slice(start, end),
        total: allResults.length,
        page: page,
        totalPages: Math.ceil(allResults.length / limit),
    };
}
```

---

## 📞 Support

For API support and questions:

-   **Documentation Issues:** Create an issue in the repository
-   **Feature Requests:** Submit a feature request
-   **Bug Reports:** Report bugs with detailed information

---

## 📄 License

This API documentation is provided under the same license as the Al-Quran application.

---

## 🔄 Changelog

### Version 1.0.0

-   Initial API release
-   Complete Quran endpoints
-   Multi-language support
-   Search functionality
-   Topics and chapters endpoints

---

**Last Updated:** December 2024
**API Version:** 1.0.0
