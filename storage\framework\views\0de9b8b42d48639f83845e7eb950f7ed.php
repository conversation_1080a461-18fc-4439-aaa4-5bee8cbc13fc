<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('quran.dashboard')); ?>">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo e(route('permissions.index')); ?>">Permissions</a></li>
                    <li class="breadcrumb-item active"><?php echo e($permission->display_name); ?></li>
                </ol>
            </div>
            <h4 class="page-title">Permission Details</h4>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 class="header-title">
                        <i class="mdi mdi-key text-primary"></i> <?php echo e($permission->display_name); ?>

                    </h4>
                    <div>
                        <?php if(auth()->user()->hasPermission('permissions.update')): ?>
                            <a href="<?php echo e(route('permissions.edit', $permission)); ?>" class="btn btn-warning">
                                <i class="mdi mdi-pencil"></i> Edit
                            </a>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">Name:</th>
                                <td><code><?php echo e($permission->name); ?></code></td>
                            </tr>
                            <tr>
                                <th>Display Name:</th>
                                <td><?php echo e($permission->display_name); ?></td>
                            </tr>
                            <tr>
                                <th>Module:</th>
                                <td><?php echo $permission->module_badge; ?></td>
                            </tr>
                            <tr>
                                <th>Action:</th>
                                <td><?php echo $permission->action_badge; ?></td>
                            </tr>
                            <?php if($permission->resource): ?>
                                <tr>
                                    <th>Resource:</th>
                                    <td><span class="badge badge-secondary"><?php echo e($permission->resource); ?></span></td>
                                </tr>
                            <?php endif; ?>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">Status:</th>
                                <td><?php echo $permission->badge; ?></td>
                            </tr>
                            <tr>
                                <th>Type:</th>
                                <td>
                                    <?php if($permission->is_system): ?>
                                        <span class="badge badge-danger">System</span>
                                    <?php else: ?>
                                        <span class="badge badge-info">Custom</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <th>Sort Order:</th>
                                <td><?php echo e($permission->sort_order); ?></td>
                            </tr>
                            <tr>
                                <th>Created:</th>
                                <td><?php echo e($permission->created_at->format('M j, Y g:i A')); ?></td>
                            </tr>
                            <tr>
                                <th>Updated:</th>
                                <td><?php echo e($permission->updated_at->format('M j, Y g:i A')); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>

                <?php if($permission->description): ?>
                    <div class="mt-3">
                        <h6>Description:</h6>
                        <p class="text-muted"><?php echo e($permission->description); ?></p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="mdi mdi-account-group"></i> Assigned Roles
                    <span class="badge badge-primary ms-2"><?php echo e($permission->roles->count()); ?></span>
                </h5>

                <?php if($permission->roles->count() > 0): ?>
                    <div class="list-group list-group-flush">
                        <?php $__currentLoopData = $permission->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <div>
                                    <span class="badge me-2" style="background-color: <?php echo e($role->color); ?>; color: white;">
                                        <?php echo e($role->display_name); ?>

                                    </span>
                                    <?php if($role->is_system): ?>
                                        <span class="badge badge-danger">System</span>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <?php if(auth()->user()->hasPermission('roles.read')): ?>
                                        <a href="<?php echo e(route('roles.show', $role)); ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="mdi mdi-eye"></i>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-3">
                        <i class="mdi mdi-account-group-outline h2 text-muted"></i>
                        <p class="text-muted">No roles assigned</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Users with this permission -->
        <div class="card mt-3">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="mdi mdi-account-multiple"></i> Users with Permission
                </h5>

                <?php
                    $usersWithPermission = $permission->users()->take(10)->get();
                ?>

                <?php if($usersWithPermission->count() > 0): ?>
                    <div class="list-group list-group-flush">
                        <?php $__currentLoopData = $usersWithPermission; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="list-group-item d-flex align-items-center px-0">
                                <img src="<?php echo e($user->avatar_url); ?>" alt="<?php echo e($user->name); ?>" 
                                     class="rounded-circle me-2" width="32" height="32">
                                <div class="flex-grow-1">
                                    <div class="fw-bold"><?php echo e($user->full_name); ?></div>
                                    <small class="text-muted"><?php echo e($user->email); ?></small>
                                </div>
                                <?php if(auth()->user()->hasPermission('users.read')): ?>
                                    <a href="<?php echo e(route('users.show', $user)); ?>" class="btn btn-sm btn-outline-primary">
                                        <i class="mdi mdi-eye"></i>
                                    </a>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>

                    <?php if($permission->users()->count() > 10): ?>
                        <div class="text-center mt-2">
                            <small class="text-muted">
                                +<?php echo e($permission->users()->count() - 10); ?> more users
                            </small>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="text-center py-3">
                        <i class="mdi mdi-account-outline h2 text-muted"></i>
                        <p class="text-muted">No users have this permission</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\alquran\resources\views/permissions/show.blade.php ENDPATH**/ ?>