<?php

namespace App\Http\Controllers;

use App\Models\UserActivity;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class UserActivityController extends Controller
{
    /**
     * Display a listing of user activities
     */
    public function index(Request $request)
    {
        $query = UserActivity::with(['user', 'subject']);

        // Filter by user
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Filter by activity type
        if ($request->filled('activity_type')) {
            $query->where('activity_type', $request->activity_type);
        }

        // Filter by subject type
        if ($request->filled('subject_type')) {
            $query->where('subject_type', $request->subject_type);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search in description, URL, or IP address
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('description', 'like', "%{$search}%")
                  ->orWhere('url', 'like', "%{$search}%")
                  ->orWhere('ip_address', 'like', "%{$search}%");
            });
        }

        $activities = $query->orderBy('created_at', 'desc')->paginate(20);

        // Get filter options
        $users = User::select('id', 'name')->orderBy('name')->get();
        $activityTypes = UserActivity::select('activity_type')->distinct()->orderBy('activity_type')->pluck('activity_type');
        $subjectTypes = UserActivity::select('subject_type')->distinct()->whereNotNull('subject_type')->orderBy('subject_type')->pluck('subject_type');

        // Get statistics
        $stats = $this->getActivityStatistics();

        return view('user-activities.index', compact('activities', 'users', 'activityTypes', 'subjectTypes', 'stats'));
    }

    /**
     * Show the specified user activity
     */
    public function show(UserActivity $activity)
    {
        $activity->load(['user', 'subject']);
        return view('user-activities.show', compact('activity'));
    }

    /**
     * Get activity statistics
     */
    private function getActivityStatistics()
    {
        $today = Carbon::today();
        $thisWeek = Carbon::now()->startOfWeek();
        $thisMonth = Carbon::now()->startOfMonth();

        return [
            'total' => UserActivity::count(),
            'today' => UserActivity::whereDate('created_at', $today)->count(),
            'this_week' => UserActivity::where('created_at', '>=', $thisWeek)->count(),
            'this_month' => UserActivity::where('created_at', '>=', $thisMonth)->count(),
            'by_type' => UserActivity::select('activity_type', DB::raw('count(*) as count'))
                ->groupBy('activity_type')
                ->orderBy('count', 'desc')
                ->limit(5)
                ->get(),
            'by_subject' => UserActivity::select('subject_type', DB::raw('count(*) as count'))
                ->whereNotNull('subject_type')
                ->groupBy('subject_type')
                ->orderBy('count', 'desc')
                ->limit(5)
                ->get(),
            'top_users' => UserActivity::select('user_id', DB::raw('count(*) as count'))
                ->whereNotNull('user_id')
                ->with('user:id,name')
                ->groupBy('user_id')
                ->orderBy('count', 'desc')
                ->limit(5)
                ->get(),
            'recent_logins' => UserActivity::where('activity_type', 'login')
                ->with('user:id,name')
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get(),
        ];
    }

    /**
     * Export user activities
     */
    public function export(Request $request)
    {
        $query = UserActivity::with(['user', 'subject']);

        // Apply same filters as index
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('activity_type')) {
            $query->where('activity_type', $request->activity_type);
        }

        if ($request->filled('subject_type')) {
            $query->where('subject_type', $request->subject_type);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $activities = $query->orderBy('created_at', 'desc')->get();

        $filename = 'user_activities_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($activities) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'ID',
                'Date/Time',
                'User',
                'Activity Type',
                'Description',
                'Subject Type',
                'Subject ID',
                'Method',
                'URL',
                'IP Address',
                'Browser'
            ]);

            foreach ($activities as $activity) {
                fputcsv($file, [
                    $activity->id,
                    $activity->created_at->format('Y-m-d H:i:s'),
                    $activity->user ? $activity->user->name : 'Guest',
                    $activity->activity_type,
                    $activity->description,
                    $activity->subject_type ? class_basename($activity->subject_type) : '',
                    $activity->subject_id,
                    $activity->method,
                    $activity->url,
                    $activity->ip_address,
                    $activity->browser
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Clean old activity logs
     */
    public function cleanup(Request $request)
    {
        $request->validate([
            'days' => 'required|integer|min:1|max:365'
        ]);

        $cutoffDate = Carbon::now()->subDays($request->days);
        $deletedCount = UserActivity::where('created_at', '<', $cutoffDate)->delete();

        return redirect()->route('user-activities.index')
            ->with('success', "Deleted {$deletedCount} activity records older than {$request->days} days.");
    }

    /**
     * Show user activity dashboard
     */
    public function dashboard()
    {
        $recentActivities = UserActivity::with(['user', 'subject'])
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get();

        $stats = $this->getActivityStatistics();

        return view('user-activities.dashboard', compact('recentActivities', 'stats'));
    }
}
