<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('quran.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item active">User Activities</li>
                    </ol>
                </div>
                <h4 class="page-title">User Activities</h4>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-md-3">
            <div class="card widget-flat">
                <div class="card-body">
                    <div class="float-right">
                        <i class="mdi mdi-account-clock widget-icon bg-success-lighten text-success"></i>
                    </div>
                    <h5 class="text-muted font-weight-normal mt-0" title="Total Activities">Total Activities</h5>
                    <h3 class="mt-3 mb-3"><?php echo e(number_format($stats['total'])); ?></h3>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card widget-flat">
                <div class="card-body">
                    <div class="float-right">
                        <i class="mdi mdi-calendar-today widget-icon bg-info-lighten text-info"></i>
                    </div>
                    <h5 class="text-muted font-weight-normal mt-0" title="Today">Today</h5>
                    <h3 class="mt-3 mb-3"><?php echo e(number_format($stats['today'])); ?></h3>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card widget-flat">
                <div class="card-body">
                    <div class="float-right">
                        <i class="mdi mdi-calendar-week widget-icon bg-warning-lighten text-warning"></i>
                    </div>
                    <h5 class="text-muted font-weight-normal mt-0" title="This Week">This Week</h5>
                    <h3 class="mt-3 mb-3"><?php echo e(number_format($stats['this_week'])); ?></h3>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card widget-flat">
                <div class="card-body">
                    <div class="float-right">
                        <i class="mdi mdi-calendar-month widget-icon bg-primary-lighten text-primary"></i>
                    </div>
                    <h5 class="text-muted font-weight-normal mt-0" title="This Month">This Month</h5>
                    <h3 class="mt-3 mb-3"><?php echo e(number_format($stats['this_month'])); ?></h3>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Filter User Activities</h4>

                    <form method="GET" action="<?php echo e(route('user-activities.index')); ?>" class="row">
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="user_id">User</label>
                                <select name="user_id" id="user_id" class="form-control">
                                    <option value="">All Users</option>
                                    <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($user->id); ?>"
                                            <?php echo e(request('user_id') == $user->id ? 'selected' : ''); ?>>
                                            <?php echo e($user->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="activity_type">Activity Type</label>
                                <select name="activity_type" id="activity_type" class="form-control">
                                    <option value="">All Types</option>
                                    <?php $__currentLoopData = $activityTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($type); ?>"
                                            <?php echo e(request('activity_type') == $type ? 'selected' : ''); ?>>
                                            <?php echo e(ucfirst(str_replace('_', ' ', $type))); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="subject_type">Subject Type</label>
                                <select name="subject_type" id="subject_type" class="form-control">
                                    <option value="">All Subjects</option>
                                    <?php $__currentLoopData = $subjectTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subjectType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($subjectType); ?>"
                                            <?php echo e(request('subject_type') == $subjectType ? 'selected' : ''); ?>>
                                            <?php echo e(class_basename($subjectType)); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="date_from">Date From</label>
                                <input type="date" name="date_from" id="date_from" class="form-control"
                                    value="<?php echo e(request('date_from')); ?>">
                            </div>
                        </div>

                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="date_to">Date To</label>
                                <input type="date" name="date_to" id="date_to" class="form-control"
                                    value="<?php echo e(request('date_to')); ?>">
                            </div>
                        </div>

                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="search">Search</label>
                                <input type="text" name="search" id="search" class="form-control"
                                    placeholder="Description, URL..." value="<?php echo e(request('search')); ?>">
                            </div>
                        </div>

                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">Filter</button>
                            <a href="<?php echo e(route('user-activities.index')); ?>" class="btn btn-secondary">Clear</a>
                            <a href="<?php echo e(route('user-activities.export')); ?>?<?php echo e(http_build_query(request()->all())); ?>"
                                class="btn btn-success">Export CSV</a>
                            <a href="<?php echo e(route('user-activities.dashboard')); ?>" class="btn btn-info">Dashboard</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Activities Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title">User Activities</h4>
                    <p class="text-muted font-13 mb-4">
                        Complete log of all user activities and system interactions.
                    </p>

                    <div class="table-responsive">
                        <table id="activitiesTable" class="table table-striped table-centered mb-0">
                            <thead>
                                <tr>
                                    <th>Date/Time</th>
                                    <th>User</th>
                                    <th>Activity</th>
                                    <th>Description</th>
                                    <th>Subject</th>
                                    <th>IP Address</th>
                                    <th>Browser</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $activities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td data-order="<?php echo e($activity->created_at->timestamp); ?>">
                                            <small><?php echo e($activity->created_at->format('Y-m-d H:i:s')); ?></small>
                                        </td>
                                        <td>
                                            <?php if($activity->user): ?>
                                                <span class="badge badge-soft-primary"><?php echo e($activity->user->name); ?></span>
                                            <?php else: ?>
                                                <span class="badge badge-soft-secondary">Guest</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span
                                                class="badge badge-soft-<?php echo e($activity->activity_type == 'login'
                                                    ? 'success'
                                                    : ($activity->activity_type == 'logout'
                                                        ? 'warning'
                                                        : ($activity->activity_type == 'delete'
                                                            ? 'danger'
                                                            : 'info'))); ?>">
                                                <?php echo e($activity->formatted_activity_type); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <span class="text-muted"
                                                title="<?php echo e($activity->description); ?>"><?php echo e(Str::limit($activity->description, 50)); ?></span>
                                        </td>
                                        <td>
                                            <?php if($activity->subject_type): ?>
                                                <span class="text-muted"><?php echo e($activity->subject_name); ?></span>
                                                <small class="d-block">#<?php echo e($activity->subject_id); ?></small>
                                            <?php else: ?>
                                                <small class="text-muted">-</small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?php echo e($activity->ip_address); ?></small>
                                        </td>
                                        <td>
                                            <small class="text-muted"
                                                title="<?php echo e($activity->user_agent); ?>"><?php echo e($activity->browser); ?></small>
                                        </td>
                                        <td>
                                            <a href="<?php echo e(route('user-activities.show', $activity)); ?>"
                                                class="btn btn-xs btn-outline-primary">
                                                <i class="mdi mdi-eye"></i> View
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="8" class="text-center">No activities found.</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cleanup Modal -->
    <div class="modal fade" id="cleanupModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Cleanup Old Activity Logs</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form method="POST" action="<?php echo e(route('user-activities.cleanup')); ?>">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="days">Delete activity logs older than (days):</label>
                            <input type="number" name="days" id="days" class="form-control" min="1"
                                max="365" value="90" required>
                            <small class="form-text text-muted">This action cannot be undone.</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">Delete Old Logs</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script>
        $(document).ready(function() {
            // Add cleanup button to page
            $('.page-title-right').prepend(
                '<button type="button" class="btn btn-danger btn-sm mr-2" data-toggle="modal" data-target="#cleanupModal"><i class="mdi mdi-delete"></i> Cleanup</button>'
                );
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\alquran\resources\views/user-activities/index.blade.php ENDPATH**/ ?>