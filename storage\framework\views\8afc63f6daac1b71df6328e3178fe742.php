<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('quran.dashboard')); ?>">Dashboard</a></li>
                    <li class="breadcrumb-item active">Roles</li>
                </ol>
            </div>
            <h4 class="page-title">Role Management</h4>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <h4 class="header-title">
                            <i class="mdi mdi-account-group text-primary"></i> Roles
                        </h4>
                    </div>
                    <div class="col-md-6 text-end">
                        <?php if(auth()->user()->hasPermission('roles.create')): ?>
                            <a href="<?php echo e(route('roles.create')); ?>" class="btn btn-primary">
                                <i class="mdi mdi-plus"></i> Create Role
                            </a>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Filters -->
                <div class="row mb-3">
                    <div class="col-12">
                        <form method="GET" action="<?php echo e(route('roles.index')); ?>" class="row g-3">
                            <div class="col-md-4">
                                <input type="text" name="search" class="form-control" placeholder="Search roles..." value="<?php echo e(request('search')); ?>">
                            </div>
                            <div class="col-md-3">
                                <select name="status" class="form-control">
                                    <option value="">All Status</option>
                                    <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Active</option>
                                    <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                                    <option value="system" <?php echo e(request('status') == 'system' ? 'selected' : ''); ?>>System</option>
                                    <option value="custom" <?php echo e(request('status') == 'custom' ? 'selected' : ''); ?>>Custom</option>
                                </select>
                            </div>
                            <div class="col-md-5">
                                <button type="submit" class="btn btn-secondary">
                                    <i class="mdi mdi-filter"></i> Filter
                                </button>
                                <a href="<?php echo e(route('roles.index')); ?>" class="btn btn-light">
                                    <i class="mdi mdi-refresh"></i> Reset
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Roles Table -->
                <div class="table-responsive">
                    <table class="table table-striped table-bordered">
                        <thead>
                            <tr>
                                <th>Role</th>
                                <th>Description</th>
                                <th>Permissions</th>
                                <th>Users</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <?php echo $role->color_badge; ?>

                                        <?php if($role->is_system): ?>
                                            <span class="badge badge-danger ms-1">System</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo e($role->display_name); ?></strong>
                                            <?php if($role->description): ?>
                                                <br><small class="text-muted"><?php echo e($role->description); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-info"><?php echo e($role->permissions->count()); ?> permissions</span>
                                    </td>
                                    <td>
                                        <span class="badge badge-success"><?php echo e($role->activeUsers->count()); ?> users</span>
                                    </td>
                                    <td><?php echo $role->badge; ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <?php if(auth()->user()->hasPermission('roles.read')): ?>
                                                <a href="<?php echo e(route('roles.show', $role)); ?>" class="btn btn-sm btn-info">
                                                    <i class="mdi mdi-eye"></i>
                                                </a>
                                            <?php endif; ?>
                                            
                                            <?php if(auth()->user()->hasPermission('roles.update')): ?>
                                                <a href="<?php echo e(route('roles.edit', $role)); ?>" class="btn btn-sm btn-warning">
                                                    <i class="mdi mdi-pencil"></i>
                                                </a>
                                            <?php endif; ?>

                                            <?php if(auth()->user()->hasPermission('roles.create')): ?>
                                                <a href="<?php echo e(route('roles.clone', $role)); ?>" class="btn btn-sm btn-secondary">
                                                    <i class="mdi mdi-content-copy"></i>
                                                </a>
                                            <?php endif; ?>

                                            <?php if(auth()->user()->hasPermission('roles.update') && (!$role->is_system || !$role->is_active)): ?>
                                                <button type="button" class="btn btn-sm btn-<?php echo e($role->is_active ? 'secondary' : 'success'); ?>" 
                                                        onclick="toggleStatus(<?php echo e($role->id); ?>)">
                                                    <i class="mdi mdi-<?php echo e($role->is_active ? 'pause' : 'play'); ?>"></i>
                                                </button>
                                            <?php endif; ?>

                                            <?php if(auth()->user()->hasPermission('roles.delete') && !$role->is_system): ?>
                                                <form method="POST" action="<?php echo e(route('roles.destroy', $role)); ?>" style="display: inline;">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="btn btn-sm btn-danger" 
                                                            onclick="return confirm('Are you sure you want to delete this role?')">
                                                        <i class="mdi mdi-delete"></i>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <i class="mdi mdi-account-group-outline h1 text-muted"></i>
                                        <h4 class="text-muted">No roles found</h4>
                                        <p class="text-muted">No roles match your current filters.</p>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <p class="text-muted">
                            Showing <?php echo e($roles->firstItem() ?? 0); ?> to <?php echo e($roles->lastItem() ?? 0); ?> 
                            of <?php echo e($roles->total()); ?> roles
                        </p>
                    </div>
                    <div>
                        <?php echo e($roles->appends(request()->query())->links()); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
function toggleStatus(roleId) {
    if (confirm('Are you sure you want to change the status of this role?')) {
        fetch(`/roles/${roleId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.error || 'An error occurred');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the role status');
        });
    }
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\alquran\resources\views/roles/index.blade.php ENDPATH**/ ?>