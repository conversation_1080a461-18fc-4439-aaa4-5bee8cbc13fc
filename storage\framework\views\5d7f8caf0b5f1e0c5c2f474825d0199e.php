<?php $__env->startSection('content'); ?>
    <!-- Row -->

<div class="row">
    <div class="col-lg-6">

        <div class="card-box">
            <h4 class="header-title">Edit Surah Detail</h4>
            <p class="sub-header">
                Modify Surah options in the system.
            </p>

            <form method="POST" class="parsley-examples" action="<?php echo e(route('surahs.update',$surah->id)); ?>">
                <?php echo method_field('PUT'); ?>
                <?php echo csrf_field(); ?>
                <div class="form-group">
                    <label for="surahname">Surah Name: <span class="text-danger">*</span></label>
                    <input type="text" name="surahname" parsley-trigger="change" required
                           placeholder="Enter surahuage" class="form-control" id="surahname" value="<?php echo e($surah->surahname); ?>">
                </div>
                <div class="form-group">
                    <label for="surahcode">Surah Code: <span class="text-danger">*</span></label>
                    <input type="text" name="surahcode" parsley-trigger="change" required
                           placeholder="Enter surahcode" class="form-control" id="surahcode" value="<?php echo e($surah->surahcode); ?>">
                </div>
                <div class="form-group">
                    <label for="surahcode">Chapters <span class="text-danger">*</span></label>
                    <select class="select2 form-control select2" name="chapter_id" data-placeholder="">
                        <?php $__currentLoopData = $chapts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $chapt): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($chapt->id); ?>"  <?php echo e($chapt->id==$surah->chapter_id?"selected":""); ?>><?php echo e($chapt->chaptername); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div class="form-group">
                    <div class="checkbox checkbox-purple">
                        <input id="isactive" name="isactive" type="checkbox" value="1" <?php echo e($surah->isactive ? "checked":""); ?>>
                        <label for="isactive">
                            Is Active
                        </label>
                    </div>

                </div>

                <div class="form-group text-right mb-0">
                    <button class="btn btn-primary waves-effect waves-light" type="submit"><i class="fas fa-save mr-1"></i>
                        Update Lanaguage
                    </button>
                    <button type="reset" onclick="window.history.go(-1);" class="btn btn-light waves-effect ml-1">
                        Cancel
                    </button>
                </div>

            </form>
        </div> <!-- end card-box -->
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\alquran\resources\views/surahs/surahsedit.blade.php ENDPATH**/ ?>