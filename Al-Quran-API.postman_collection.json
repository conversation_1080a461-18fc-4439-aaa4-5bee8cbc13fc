{"info": {"name": "Al-Quran API", "description": "Complete API collection for Al-Quran application", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "variable": [{"key": "base_url", "value": "http://localhost:8000/api", "type": "string"}], "item": [{"name": "Quran API", "item": [{"name": "Get All Quran Data", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/quran", "host": ["{{base_url}}"], "path": ["quran"]}}, "response": []}, {"name": "Get Specific Surah", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/quran/surah/1", "host": ["{{base_url}}"], "path": ["quran", "surah", "1"]}}, "response": []}, {"name": "Get Specific Ayah", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/quran/surah/1/ayah/1", "host": ["{{base_url}}"], "path": ["quran", "surah", "1", "ayah", "1"]}}, "response": []}, {"name": "Search Quran", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/quran/search?q=Allah", "host": ["{{base_url}}"], "path": ["quran", "search"], "query": [{"key": "q", "value": "<PERSON>"}]}}, "response": []}, {"name": "Get Ayah with Language", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/quran/ayah/1/1/ur", "host": ["{{base_url}}"], "path": ["quran", "ayah", "1", "1", "ur"]}}, "response": []}, {"name": "Get All Translations", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/quran/translations/1/1", "host": ["{{base_url}}"], "path": ["quran", "translations", "1", "1"]}}, "response": []}, {"name": "Get Specific Translation", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/quran/translation/1/1/en", "host": ["{{base_url}}"], "path": ["quran", "translation", "1", "1", "en"]}}, "response": []}, {"name": "Get Tafsir", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/quran/tafsir/1/1", "host": ["{{base_url}}"], "path": ["quran", "tafsir", "1", "1"]}}, "response": []}, {"name": "Get Tafsir with Language", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/quran/tafsir/1/1/ur", "host": ["{{base_url}}"], "path": ["quran", "tafsir", "1", "1", "ur"]}}, "response": []}, {"name": "Get Available Languages", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/quran/languages", "host": ["{{base_url}}"], "path": ["quran", "languages"]}}, "response": []}]}, {"name": "Topics API", "item": [{"name": "Get All Topics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/topics", "host": ["{{base_url}}"], "path": ["topics"]}}, "response": []}, {"name": "Search Topics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/topics/search?q=God", "host": ["{{base_url}}"], "path": ["topics", "search"], "query": [{"key": "q", "value": "God"}]}}, "response": []}]}, {"name": "Chapters API", "item": [{"name": "Get All Chapters", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/chapters", "host": ["{{base_url}}"], "path": ["chapters"]}}, "response": []}, {"name": "Search Chapters", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/chapters/search?q=Alif", "host": ["{{base_url}}"], "path": ["chapters", "search"], "query": [{"key": "q", "value": "<PERSON><PERSON>"}]}}, "response": []}]}]}