@extends('layouts.master')

@section('content')
    <!-- Row -->

    <div class="row">
        <div class="col-12">
            <div class="card-box table-responsive">
                <a href="{{ route('topicdetails.create') }}" class="btn btn-primary float-right">Add Topic Details</a>
                <h4 class="header-title"><b>Quran Topics Detail List</b></h4>
                <p class="sub-header">
                    List Details of Quran Topics.
                </p>

                <table id="datatable-buttons" class="table table-striped table-bordered dt-responsive"
                    style="border-collapse: collapse; border-spacing: 0; width: 100%;">
                    <thead>
                        <tr>
                            <th class="border-bottom-0">ID</th>
                            <th class="border-bottom-0">Topic Name</th>
                            <th class="border-bottom-0">Surah Name</th>
                            <th class="border-bottom-0">Ayat Number</th>
                            <th class="border-bottom-0">Langugae</th>
                            <th class="border-bottom-0">Status</th>
                            <th class="border-bottom-0">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($topicdetails as $topic)
                            <tr>
                                <td>{{ $topic->id }}</td>
                                <td>{{ $topic->topic->topicname }} ({{ $topic->topic->topiccode }})</td>
                                <td>{{ $topic->surah->surahname }} ({{ $topic->surah->surahcode }})</td>
                                <td>{{ $topic->topicdetail }}</td>
                                <td>{{ $topic->language->name }}</td>

                                <td><span
                                        class="badge badge-{{ $topic->isactive == 1 ? 'primary' : 'danger' }}">{{ $topic->isactive == 1 ? 'Active' : 'Disable' }}</span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('topicdetails.edit', $topic->id) }}"
                                            class="btn btn-sm btn-purple"><i class="far fa-edit"></i></a>
                                        <form action="{{ route('topicdetails.destroy', $topic) }}" method="POST"
                                            style="display: inline-block">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit"
                                                onclick="return confirm('Are you sure to delete this record?')"
                                                class="btn btn-danger btn-sm"><i class="fas fa-trash-alt"></i></button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach

                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <!-- End Row -->
@endsection
