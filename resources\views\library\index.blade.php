@extends('layouts.master')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card-box">
                <div class="d-flex justify-content-between mb-3">
                    <h4 class="header-title">Library</h4>
                    <div>
                        <a href="{{ route('library.create') }}" class="btn btn-primary">Add New Item</a>
                    </div>
                </div>

                <!-- Filter buttons -->
                <div class="mb-3">
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-primary active filter-btn"
                            data-filter="all">All</button>
                        <button type="button" class="btn btn-outline-primary filter-btn" data-filter="pdf">PDF
                            Books</button>
                        <button type="button" class="btn btn-outline-primary filter-btn"
                            data-filter="video">Videos</button>
                        <button type="button" class="btn btn-outline-primary filter-btn" data-filter="audio">Audio</button>
                        <button type="button" class="btn btn-outline-primary filter-btn" data-filter="other">Other</button>
                    </div>
                </div>

                <div class="row" id="library-items">
                    @foreach ($items as $item)
                        <div class="col-md-4 col-xl-3 mb-4 library-item" data-type="{{ $item->media_type }}">
                            <div class="card h-100">
                                <div class="card-img-top position-relative">
                                    <img src="{{ $item->getThumbnailUrl() }}" alt="{{ $item->title }}" class="img-fluid"
                                        style="height: 180px; width: 100%; object-fit: cover;">
                                    <div class="position-absolute" style="top: 10px; right: 10px;">
                                        <span class="badge badge-pill badge-{{ $item->isactive ? 'success' : 'danger' }}">
                                            {{ $item->isactive ? 'Active' : 'Inactive' }}
                                        </span>
                                    </div>
                                    <div class="position-absolute" style="top: 10px; left: 10px;">
                                        <span class="badge badge-pill badge-primary">
                                            {{ ucfirst($item->media_type) }}
                                        </span>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <h5 class="card-title text-truncate">{{ $item->title }}</h5>
                                    <p class="card-text text-muted small mb-2">
                                        @if ($item->author)
                                            <i class="far fa-user mr-1"></i> {{ $item->author }}<br>
                                        @endif
                                        @if ($item->category)
                                            <i class="far fa-folder mr-1"></i> {{ $item->category }}<br>
                                        @endif
                                        @if ($item->language)
                                            <i class="far fa-flag mr-1"></i> {{ $item->language }}<br>
                                        @endif
                                        @if ($item->duration)
                                            <i class="far fa-clock mr-1"></i> {{ $item->duration }}<br>
                                        @endif
                                        @if ($item->file_size)
                                            <i class="far fa-hdd mr-1"></i> {{ $item->getFormattedFileSizeAttribute() }}
                                        @endif
                                    </p>
                                </div>
                                <div class="card-footer">
                                    <div class="btn-group btn-block">
                                        <a href="{{ route('library.show', $item->id) }}" class="btn btn-info btn-sm">
                                            <i class="far fa-eye"></i> View
                                        </a>
                                        <a href="{{ route('library.edit', $item->id) }}" class="btn btn-warning btn-sm">
                                            <i class="far fa-edit"></i> Edit
                                        </a>
                                        <form action="{{ route('library.destroy', $item->id) }}" method="POST"
                                            style="display:inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-danger btn-sm"
                                                onclick="return confirm('Are you sure?')">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Debug: Log all library items and their data-type attributes
            console.log('Library items found:', $('.library-item').length);
            $('.library-item').each(function() {
                console.log('Item type:', $(this).data('type'));
            });

            // Filter functionality
            $('.filter-btn').click(function() {
                $('.filter-btn').removeClass('active');
                $(this).addClass('active');

                const filter = $(this).data('filter');
                console.log('Filter clicked:', filter);

                if (filter === 'all') {
                    $('.library-item').show();
                    console.log('Showing all items:', $('.library-item').length);
                } else {
                    $('.library-item').hide();
                    const matchingItems = $('.library-item[data-type="' + filter + '"]');
                    console.log('Matching items for', filter + ':', matchingItems.length);
                    matchingItems.show();
                }
            });
        });
    </script>
@endsection
