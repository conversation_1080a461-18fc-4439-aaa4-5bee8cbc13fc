@extends('layouts.master')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('quran.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('permissions.index') }}">Permissions</a></li>
                    <li class="breadcrumb-item active">Bulk Create</li>
                </ol>
            </div>
            <h4 class="page-title">Bulk Create Permissions</h4>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title">
                    <i class="mdi mdi-plus-box-multiple text-primary"></i> Bulk Create Permissions
                </h4>
                <p class="text-muted">Create multiple permissions for a module at once.</p>

                <form method="POST" action="{{ route('permissions.store-bulk') }}">
                    @csrf

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="module" class="form-label">Module <span class="text-danger">*</span></label>
                                <select class="form-control @error('module') is-invalid @enderror" id="module" name="module" required>
                                    <option value="">Select Module</option>
                                    @foreach($modules as $key => $value)
                                        <option value="{{ $key }}" {{ old('module') == $key ? 'selected' : '' }}>
                                            {{ $value }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('module')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="resource" class="form-label">Resource (Optional)</label>
                                <input type="text" class="form-control @error('resource') is-invalid @enderror" 
                                       id="resource" name="resource" value="{{ old('resource') }}"
                                       placeholder="e.g., own, all, specific">
                                @error('resource')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Specify resource scope if needed</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Actions <span class="text-danger">*</span></label>
                        <div class="row">
                            @foreach($actions as $key => $value)
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="action_{{ $key }}" 
                                               name="actions[]" value="{{ $key }}" 
                                               {{ in_array($key, old('actions', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="action_{{ $key }}">
                                            {{ $value }}
                                        </label>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        @error('actions')
                            <div class="text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1" 
                                       {{ old('is_active', true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    Active
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <button type="button" class="btn btn-secondary" onclick="selectAll()">Select All</button>
                            <button type="button" class="btn btn-light" onclick="selectNone()">Select None</button>
                        </div>
                    </div>

                    <!-- Preview -->
                    <div class="mt-4">
                        <h6>Permission Preview:</h6>
                        <div class="alert alert-info">
                            <div id="permission-preview">
                                <em>Select module and actions to see preview</em>
                            </div>
                        </div>
                    </div>

                    <div class="text-end">
                        <a href="{{ route('permissions.index') }}" class="btn btn-secondary me-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">Create Permissions</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const moduleSelect = document.getElementById('module');
    const resourceInput = document.getElementById('resource');
    const actionCheckboxes = document.querySelectorAll('input[name="actions[]"]');
    const preview = document.getElementById('permission-preview');

    function updatePreview() {
        const module = moduleSelect.value;
        const resource = resourceInput.value;
        const selectedActions = Array.from(actionCheckboxes)
            .filter(cb => cb.checked)
            .map(cb => cb.value);

        if (module && selectedActions.length > 0) {
            let previewHtml = '<strong>Permissions to be created:</strong><br>';
            selectedActions.forEach(action => {
                let permissionName = module + '.' + action;
                if (resource) {
                    permissionName += '.' + resource;
                }
                previewHtml += '<code>' + permissionName + '</code><br>';
            });
            preview.innerHTML = previewHtml;
        } else {
            preview.innerHTML = '<em>Select module and actions to see preview</em>';
        }
    }

    moduleSelect.addEventListener('change', updatePreview);
    resourceInput.addEventListener('input', updatePreview);
    actionCheckboxes.forEach(cb => {
        cb.addEventListener('change', updatePreview);
    });

    // Initial preview update
    updatePreview();
});

function selectAll() {
    const checkboxes = document.querySelectorAll('input[name="actions[]"]');
    checkboxes.forEach(cb => cb.checked = true);
    document.getElementById('permission-preview').dispatchEvent(new Event('change'));
    updatePreview();
}

function selectNone() {
    const checkboxes = document.querySelectorAll('input[name="actions[]"]');
    checkboxes.forEach(cb => cb.checked = false);
    updatePreview();
}

function updatePreview() {
    const moduleSelect = document.getElementById('module');
    const resourceInput = document.getElementById('resource');
    const actionCheckboxes = document.querySelectorAll('input[name="actions[]"]');
    const preview = document.getElementById('permission-preview');

    const module = moduleSelect.value;
    const resource = resourceInput.value;
    const selectedActions = Array.from(actionCheckboxes)
        .filter(cb => cb.checked)
        .map(cb => cb.value);

    if (module && selectedActions.length > 0) {
        let previewHtml = '<strong>Permissions to be created:</strong><br>';
        selectedActions.forEach(action => {
            let permissionName = module + '.' + action;
            if (resource) {
                permissionName += '.' + resource;
            }
            previewHtml += '<code>' + permissionName + '</code><br>';
        });
        preview.innerHTML = previewHtml;
    } else {
        preview.innerHTML = '<em>Select module and actions to see preview</em>';
    }
}
</script>
@endsection
