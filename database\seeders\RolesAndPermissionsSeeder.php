<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Role;
use App\Models\Permission;
use App\Models\User;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Permissions
        $permissions = $this->createPermissions();
        
        // Create Roles
        $roles = $this->createRoles();
        
        // Assign Permissions to Roles
        $this->assignPermissionsToRoles($roles, $permissions);
        
        // Create default admin user if not exists
        $this->createDefaultAdmin($roles['super_admin']);
    }

    /**
     * Create system permissions
     */
    private function createPermissions(): array
    {
        $permissionGroups = [
            'users' => ['create', 'read', 'update', 'delete', 'manage'],
            'roles' => ['create', 'read', 'update', 'delete', 'manage'],
            'permissions' => ['create', 'read', 'update', 'delete', 'manage'],
            'quran' => ['create', 'read', 'update', 'delete', 'manage', 'export'],
            'topics' => ['create', 'read', 'update', 'delete', 'manage'],
            'chapters' => ['create', 'read', 'update', 'delete', 'manage'],
            'books' => ['create', 'read', 'update', 'delete', 'manage'],
            'library' => ['create', 'read', 'update', 'delete', 'manage'],
            'languages' => ['create', 'read', 'update', 'delete', 'manage'],
            'backup' => ['create', 'read', 'restore', 'delete', 'manage'],
            'audit' => ['read', 'export', 'manage'],
            'system' => ['manage', 'view'],
            'api' => ['read', 'manage'],
        ];

        $permissions = [];

        foreach ($permissionGroups as $module => $actions) {
            foreach ($actions as $action) {
                $name = "{$module}.{$action}";
                $displayName = ucfirst($action) . ' ' . ucfirst($module);
                
                $permission = Permission::create([
                    'name' => $name,
                    'display_name' => $displayName,
                    'description' => "Permission to {$action} {$module}",
                    'module' => $module,
                    'action' => $action,
                    'is_system' => true,
                    'is_active' => true,
                    'sort_order' => array_search($action, $actions),
                ]);

                $permissions[$name] = $permission;
            }
        }

        return $permissions;
    }

    /**
     * Create system roles
     */
    private function createRoles(): array
    {
        $rolesData = [
            'super_admin' => [
                'display_name' => 'Super Administrator',
                'description' => 'Full system access with all permissions',
                'color' => '#dc3545',
                'sort_order' => 1,
            ],
            'admin' => [
                'display_name' => 'Administrator',
                'description' => 'Administrative access to most system features',
                'color' => '#007bff',
                'sort_order' => 2,
            ],
            'editor' => [
                'display_name' => 'Editor',
                'description' => 'Can create and edit content but limited administrative access',
                'color' => '#28a745',
                'sort_order' => 3,
            ],
            'moderator' => [
                'display_name' => 'Moderator',
                'description' => 'Can moderate content and manage basic features',
                'color' => '#ffc107',
                'sort_order' => 4,
            ],
            'viewer' => [
                'display_name' => 'Viewer',
                'description' => 'Read-only access to system content',
                'color' => '#6c757d',
                'sort_order' => 5,
            ],
            'api_user' => [
                'display_name' => 'API User',
                'description' => 'Access to API endpoints only',
                'color' => '#17a2b8',
                'sort_order' => 6,
            ],
        ];

        $roles = [];

        foreach ($rolesData as $name => $data) {
            $role = Role::create([
                'name' => $name,
                'display_name' => $data['display_name'],
                'description' => $data['description'],
                'color' => $data['color'],
                'is_system' => true,
                'is_active' => true,
                'sort_order' => $data['sort_order'],
            ]);

            $roles[$name] = $role;
        }

        return $roles;
    }

    /**
     * Assign permissions to roles
     */
    private function assignPermissionsToRoles(array $roles, array $permissions): void
    {
        // Super Admin - All permissions
        $roles['super_admin']->permissions()->sync(array_keys($permissions));

        // Admin - Most permissions except super admin specific ones
        $adminPermissions = collect($permissions)->filter(function ($permission) {
            return !in_array($permission->name, [
                'system.manage',
                'users.delete', // Can't delete users
                'roles.delete', // Can't delete roles
                'permissions.delete', // Can't delete permissions
            ]);
        })->keys()->toArray();
        $roles['admin']->permissions()->sync($adminPermissions);

        // Editor - Content management permissions
        $editorPermissions = collect($permissions)->filter(function ($permission) {
            return in_array($permission->module, ['quran', 'topics', 'chapters', 'books', 'library', 'languages']) &&
                   in_array($permission->action, ['create', 'read', 'update']);
        })->keys()->toArray();
        $roles['editor']->permissions()->sync($editorPermissions);

        // Moderator - Read and moderate content
        $moderatorPermissions = collect($permissions)->filter(function ($permission) {
            return in_array($permission->module, ['quran', 'topics', 'chapters', 'books', 'library']) &&
                   in_array($permission->action, ['read', 'update']);
        })->keys()->toArray();
        $roles['moderator']->permissions()->sync($moderatorPermissions);

        // Viewer - Read-only permissions
        $viewerPermissions = collect($permissions)->filter(function ($permission) {
            return $permission->action === 'read';
        })->keys()->toArray();
        $roles['viewer']->permissions()->sync($viewerPermissions);

        // API User - API related permissions
        $apiPermissions = collect($permissions)->filter(function ($permission) {
            return $permission->module === 'api' || 
                   ($permission->module === 'quran' && $permission->action === 'read');
        })->keys()->toArray();
        $roles['api_user']->permissions()->sync($apiPermissions);
    }

    /**
     * Create default admin user
     */
    private function createDefaultAdmin(Role $superAdminRole): void
    {
        // Check if admin user already exists
        $adminUser = User::where('email', '<EMAIL>')->first();

        if (!$adminUser) {
            $adminUser = User::create([
                'name' => 'System Administrator',
                'username' => 'admin',
                'first_name' => 'System',
                'last_name' => 'Administrator',
                'email' => '<EMAIL>',
                'password' => bcrypt('admin123'),
                'timezone' => 'UTC',
                'language' => 'en',
                'is_active' => true,
                'is_verified' => true,
                'email_verified_at' => now(),
            ]);

            // Assign super admin role
            $adminUser->assignRole($superAdminRole);

            $this->command->info('Default admin user created:');
            $this->command->info('Email: <EMAIL>');
            $this->command->info('Password: admin123');
            $this->command->warn('Please change the default password after first login!');
        } else {
            // Ensure existing admin has super admin role
            if (!$adminUser->hasRole('super_admin')) {
                $adminUser->assignRole($superAdminRole);
                $this->command->info('Super admin role assigned to existing admin user.');
            }
        }
    }
}
