@extends('layouts.master')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('quran.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('user-activities.index') }}">User Activities</a></li>
                    <li class="breadcrumb-item active">Activity Details</li>
                </ol>
            </div>
            <h4 class="page-title">Activity Details</h4>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5 class="card-title">Basic Information</h5>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Date/Time:</strong></td>
                                <td>{{ $activity->created_at->format('Y-m-d H:i:s') }}</td>
                            </tr>
                            <tr>
                                <td><strong>User:</strong></td>
                                <td>
                                    @if($activity->user)
                                        <span class="badge badge-soft-primary">{{ $activity->user->name }}</span>
                                    @else
                                        <span class="badge badge-soft-secondary">Guest</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Activity Type:</strong></td>
                                <td>
                                    <span class="badge badge-soft-{{ 
                                        $activity->activity_type == 'login' ? 'success' : 
                                        ($activity->activity_type == 'logout' ? 'warning' : 
                                        ($activity->activity_type == 'delete' ? 'danger' : 'info')) 
                                    }}">
                                        {{ $activity->formatted_activity_type }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Description:</strong></td>
                                <td>{{ $activity->description }}</td>
                            </tr>
                            <tr>
                                <td><strong>HTTP Method:</strong></td>
                                <td>
                                    @if($activity->method)
                                        <span class="badge badge-soft-secondary">{{ $activity->method }}</span>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td><strong>IP Address:</strong></td>
                                <td>{{ $activity->ip_address }}</td>
                            </tr>
                            <tr>
                                <td><strong>Session ID:</strong></td>
                                <td><small>{{ $activity->session_id }}</small></td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="col-md-6">
                        <h5 class="card-title">Technical Details</h5>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Browser:</strong></td>
                                <td>{{ $activity->browser }}</td>
                            </tr>
                            <tr>
                                <td><strong>User Agent:</strong></td>
                                <td><small class="text-muted">{{ $activity->user_agent }}</small></td>
                            </tr>
                            <tr>
                                <td><strong>URL:</strong></td>
                                <td><small>{{ $activity->url }}</small></td>
                            </tr>
                            @if($activity->subject_type)
                                <tr>
                                    <td><strong>Subject:</strong></td>
                                    <td>{{ $activity->subject_name }} #{{ $activity->subject_id }}</td>
                                </tr>
                            @endif
                        </table>
                    </div>
                </div>

                @if($activity->properties)
                    <hr>
                    <h5 class="card-title">Additional Properties</h5>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Property</th>
                                    <th>Value</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($activity->properties as $key => $value)
                                    <tr>
                                        <td><strong>{{ ucfirst(str_replace('_', ' ', $key)) }}</strong></td>
                                        <td>
                                            @if(is_null($value))
                                                <span class="text-muted">null</span>
                                            @elseif(is_bool($value))
                                                <span class="badge badge-soft-{{ $value ? 'success' : 'danger' }}">
                                                    {{ $value ? 'true' : 'false' }}
                                                </span>
                                            @elseif(is_array($value) || is_object($value))
                                                <pre class="bg-light p-2 rounded"><code>{{ json_encode($value, JSON_PRETTY_PRINT) }}</code></pre>
                                            @else
                                                <code>{{ $value }}</code>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @endif

                <div class="mt-3">
                    <a href="{{ route('user-activities.index') }}" class="btn btn-secondary">
                        <i class="mdi mdi-arrow-left"></i> Back to Activities
                    </a>
                    
                    @if($activity->subject)
                        <a href="#" class="btn btn-info" onclick="alert('Subject view not implemented yet')">
                            <i class="mdi mdi-eye"></i> View {{ $activity->subject_name }}
                        </a>
                    @endif

                    @if($activity->user)
                        <a href="#" class="btn btn-primary" onclick="alert('User profile not implemented yet')">
                            <i class="mdi mdi-account"></i> View User Profile
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
