<?php

namespace App\Http\Controllers;

use App\Models\Surah;
use App\Models\Topics;
use App\Models\TopicDetail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TopicDetailController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //\
        $topicdetails=TopicDetail::all();
        return view('topicdetails.topicdetailslst',compact('topicdetails'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
        $topics=Topics::all();
        $surahs=Surah::all();
        return view('topicdetails.topicdetailsadd',compact('topics','surahs'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
        // dd($request->all());
        $isactive=0;
        if($request->isactive==1)
            $isactive=1;

        $surahdetail = explode("|", $request->surah_id);
        $surah_id = $surahdetail[0];
        $surahcode = $surahdetail[1];

        $topicdetail = explode("|", $request->topic_id);
        $topic_id = $topicdetail[0];
        $topiccode = $topicdetail[1];


        $topdet=new TopicDetail();
        $topdet->topic_id=$topic_id;
        $topdet->topiccode=$topiccode;
        $topdet->surah_id=$surah_id;
        $topdet->surahcode=$surahcode;
        $topdet->language_id=1;
        $topdet->topicdetail=$request->ayatdetail;
        $topdet->description="";
        $topdet->isactive=$isactive;
        $topdet->created_by=Auth::user()->name ?? "Shahid";
        $topdet->created_at=Now();
        $topdet->save();

        return redirect()->route('topicdetails.index')->with('success', 'Surah added successfully!');

    }

    /**
     * Display the specified resource.
     */
    public function show(TopicDetail $topicdetail)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(TopicDetail $topicdetail)
    {
        //
        $topics=Topics::all();
        $surahs=Surah::all();
        return view('topicdetails.topicdetailsedit',compact('topics','surahs','topicdetail'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, TopicDetail $topicdetail)
    {
        //
        // dd($request->all());
        $ayatdetail=str_replace("\t","",$request->ayatdetail);
        $ayatdetail=str_replace(" ","",$ayatdetail);

        $isactive=0;
        if($request->isactive==1)
            $isactive=1;

        $surahdetail = explode("|", $request->surah_id);
        $surah_id = $surahdetail[0];
        $surahcode = $surahdetail[1];

        $topicmaster = explode("|", $request->topic_id);
        $topic_id = $topicmaster[0];
        $topiccode = $topicmaster[1];


        $topdet=TopicDetail::find($topicdetail->id);
        $topdet->topic_id=$topic_id;
        $topdet->topiccode=$topiccode;
        $topdet->surah_id=$surah_id;
        $topdet->surahcode=$surahcode;
        $topdet->language_id=1;
        $topdet->topicdetail=$request->ayatdetail;
        $topdet->description="";
        $topdet->isactive=$isactive;
        $topdet->created_by=Auth::user()->name ?? "Shahid";
        $topdet->created_at=Now();
        $topdet->save();

        return redirect()->route('topicdetails.index')->with('success', 'Surah added successfully!');

    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(TopicDetail $topicdetail)
    {
        //
        $topicdetail->delete();
        return redirect()->route('topicdetails.index')->with('success', 'Surah added successfully!');
    }
}
