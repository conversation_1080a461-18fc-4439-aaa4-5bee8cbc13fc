<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use App\Traits\AuditableTrait;
use App\Contracts\AuditableInterface;

class Permission extends Model implements AuditableInterface
{
    use HasFactory, AuditableTrait;

    protected $fillable = [
        'name',
        'display_name',
        'description',
        'module',
        'action',
        'resource',
        'is_system',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_system' => 'boolean',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the roles that have this permission
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'role_permissions')
                    ->withTimestamps();
    }

    /**
     * Get users who have this permission through roles
     */
    public function users()
    {
        return User::whereHas('roles.permissions', function ($query) {
            $query->where('permissions.id', $this->id);
        });
    }

    /**
     * Get permission badge HTML
     */
    public function getBadgeAttribute(): string
    {
        $status = $this->is_active ? 'Active' : 'Inactive';
        $badgeClass = $this->is_active ? 'success' : 'danger';
        
        return "<span class='badge badge-{$badgeClass}'>{$status}</span>";
    }

    /**
     * Get module badge HTML
     */
    public function getModuleBadgeAttribute(): string
    {
        $colors = [
            'users' => 'primary',
            'quran' => 'info',
            'topics' => 'success',
            'chapters' => 'warning',
            'books' => 'secondary',
            'library' => 'dark',
            'system' => 'danger',
            'api' => 'light',
            'backup' => 'purple',
        ];

        $color = $colors[$this->module] ?? 'secondary';
        
        return "<span class='badge badge-{$color}'>" . ucfirst($this->module) . "</span>";
    }

    /**
     * Get action badge HTML
     */
    public function getActionBadgeAttribute(): string
    {
        $colors = [
            'create' => 'success',
            'read' => 'info',
            'update' => 'warning',
            'delete' => 'danger',
            'manage' => 'primary',
            'export' => 'secondary',
            'import' => 'dark',
        ];

        $color = $colors[$this->action] ?? 'secondary';
        
        return "<span class='badge badge-{$color}'>" . ucfirst($this->action) . "</span>";
    }

    /**
     * Scope for active permissions
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for system permissions
     */
    public function scopeSystem($query)
    {
        return $query->where('is_system', true);
    }

    /**
     * Scope for non-system permissions
     */
    public function scopeNonSystem($query)
    {
        return $query->where('is_system', false);
    }

    /**
     * Scope for specific module
     */
    public function scopeModule($query, string $module)
    {
        return $query->where('module', $module);
    }

    /**
     * Scope for specific action
     */
    public function scopeAction($query, string $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope for ordered permissions
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('module')
                    ->orderBy('sort_order')
                    ->orderBy('display_name');
    }

    /**
     * Get permissions grouped by module
     */
    public static function getGroupedByModule()
    {
        return static::active()
                    ->ordered()
                    ->get()
                    ->groupBy('module');
    }

    /**
     * Generate permission name from components
     */
    public static function generateName(string $module, string $action, ?string $resource = null): string
    {
        $parts = [$module, $action];
        
        if ($resource) {
            $parts[] = $resource;
        }
        
        return implode('.', $parts);
    }

    /**
     * Create permission with auto-generated name
     */
    public static function createWithName(array $attributes): self
    {
        if (!isset($attributes['name'])) {
            $attributes['name'] = static::generateName(
                $attributes['module'],
                $attributes['action'],
                $attributes['resource'] ?? null
            );
        }

        return static::create($attributes);
    }
}
