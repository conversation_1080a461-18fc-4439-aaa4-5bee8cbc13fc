<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\ActivityLogger;
use Illuminate\Support\Facades\Auth;

class AuditMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only log for authenticated users and specific routes
        if (Auth::check() && $this->shouldLog($request)) {
            $this->logActivity($request, $response);
        }

        return $response;
    }

    /**
     * Determine if the request should be logged
     */
    private function shouldLog(Request $request): bool
    {
        $method = $request->method();
        $path = $request->path();

        // Skip logging for certain routes
        $skipRoutes = [
            'api/*',
            '_debugbar/*',
            'telescope/*',
            'horizon/*',
            'nova/*',
            'livewire/*',
        ];

        foreach ($skipRoutes as $skipRoute) {
            if (fnmatch($skipRoute, $path)) {
                return false;
            }
        }

        // Skip logging for asset requests
        if (preg_match('/\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/i', $path)) {
            return false;
        }

        // Skip AJAX requests for certain actions
        if ($request->ajax() && in_array($method, ['GET'])) {
            return false;
        }

        // Log all POST, PUT, PATCH, DELETE requests
        if (in_array($method, ['POST', 'PUT', 'PATCH', 'DELETE'])) {
            return true;
        }

        // Log specific GET requests
        $logRoutes = [
            'dashboard',
            'library/*',
            'books/*',
            'surahs/*',
            'chapters/*',
            'topics/*',
            'audits/*',
            'user-activities/*',
        ];

        foreach ($logRoutes as $logRoute) {
            if (fnmatch($logRoute, $path)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Log the activity
     */
    private function logActivity(Request $request, Response $response): void
    {
        $method = $request->method();
        $path = $request->path();
        $statusCode = $response->getStatusCode();

        // Determine activity type based on method and route
        $activityType = $this->determineActivityType($method, $path);
        
        // Create description
        $description = $this->createDescription($method, $path, $statusCode);

        // Additional properties
        $properties = [
            'method' => $method,
            'status_code' => $statusCode,
            'response_time' => microtime(true) - LARAVEL_START,
        ];

        // Add request data for certain methods (excluding sensitive data)
        if (in_array($method, ['POST', 'PUT', 'PATCH'])) {
            $requestData = $request->except([
                'password',
                'password_confirmation',
                'current_password',
                '_token',
                '_method',
            ]);
            
            if (!empty($requestData)) {
                $properties['request_data'] = $requestData;
            }
        }

        ActivityLogger::log($activityType, $description, null, $properties);
    }

    /**
     * Determine activity type based on method and path
     */
    private function determineActivityType(string $method, string $path): string
    {
        // Map specific routes to activity types
        $routeMap = [
            'login' => 'login',
            'logout' => 'logout',
            'register' => 'register',
            'dashboard' => 'view_dashboard',
        ];

        foreach ($routeMap as $route => $type) {
            if (str_contains($path, $route)) {
                return $type;
            }
        }

        // Map by HTTP method
        switch ($method) {
            case 'GET':
                return 'view';
            case 'POST':
                return 'create';
            case 'PUT':
            case 'PATCH':
                return 'update';
            case 'DELETE':
                return 'delete';
            default:
                return 'request';
        }
    }

    /**
     * Create a human-readable description
     */
    private function createDescription(string $method, string $path, int $statusCode): string
    {
        $action = $this->getActionFromMethod($method);
        $resource = $this->getResourceFromPath($path);
        
        $description = "{$action} {$resource}";
        
        if ($statusCode >= 400) {
            $description .= " (Error: {$statusCode})";
        }

        return $description;
    }

    /**
     * Get action description from HTTP method
     */
    private function getActionFromMethod(string $method): string
    {
        return match ($method) {
            'GET' => 'Viewed',
            'POST' => 'Created',
            'PUT', 'PATCH' => 'Updated',
            'DELETE' => 'Deleted',
            default => 'Accessed',
        };
    }

    /**
     * Get resource name from path
     */
    private function getResourceFromPath(string $path): string
    {
        // Remove leading slash and get first segment
        $segments = explode('/', trim($path, '/'));
        $resource = $segments[0] ?? 'page';

        // Convert to human-readable format
        return ucfirst(str_replace(['-', '_'], ' ', $resource));
    }
}
