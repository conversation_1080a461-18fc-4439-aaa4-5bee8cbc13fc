@extends('layouts.master')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('quran.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Users</li>
                    </ol>
                </div>
                <h4 class="page-title">User Management</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h4 class="header-title">
                                <i class="mdi mdi-account-multiple text-primary"></i> Users
                            </h4>
                        </div>
                        <div class="col-md-6 text-end">
                            @if (auth()->user()->hasPermission('users.create'))
                                <a href="{{ route('users.create') }}" class="btn btn-primary">
                                    <i class="mdi mdi-plus"></i> Create User
                                </a>
                            @endif
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <form method="GET" action="{{ route('users.index') }}" class="row g-3">
                                <div class="col-md-4">
                                    <input type="text" name="search" class="form-control" placeholder="Search users..."
                                        value="{{ request('search') }}">
                                </div>
                                <div class="col-md-2">
                                    <select name="status" class="form-control">
                                        <option value="">All Status</option>
                                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active
                                        </option>
                                        <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>
                                            Inactive</option>
                                        <option value="verified" {{ request('status') == 'verified' ? 'selected' : '' }}>
                                            Verified</option>
                                        <option value="unverified"
                                            {{ request('status') == 'unverified' ? 'selected' : '' }}>Unverified</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select name="role" class="form-control">
                                        <option value="">All Roles</option>
                                        @foreach ($roles as $role)
                                            <option value="{{ $role->name }}"
                                                {{ request('role') == $role->name ? 'selected' : '' }}>
                                                {{ $role->display_name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <button type="submit" class="btn btn-secondary">
                                        <i class="mdi mdi-filter"></i> Filter
                                    </button>
                                    <a href="{{ route('users.index') }}" class="btn btn-light">
                                        <i class="mdi mdi-refresh"></i> Reset
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Users Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Email</th>
                                    <th>Roles</th>
                                    <th>Last Login</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($users as $user)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="{{ $user->avatar_url }}" alt="{{ $user->name }}"
                                                    class="rounded-circle me-2" width="40" height="40">
                                                <div>
                                                    <strong>{{ $user->full_name }}</strong>
                                                    @if ($user->username)
                                                        <br><small class="text-muted">@{{ $user - > username }}</small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ $user->email }}</td>
                                        <td>
                                            @foreach ($user->activeRoles->take(2) as $role)
                                                <span class="badge me-1"
                                                    style="background-color: {{ $role->color }}; color: white;">
                                                    {{ $role->display_name }}
                                                </span>
                                            @endforeach
                                            @if ($user->activeRoles->count() > 2)
                                                <span class="text-muted">+{{ $user->activeRoles->count() - 2 }} more</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if ($user->last_login_at)
                                                {{ $user->last_login_at->diffForHumans() }}
                                                <br><small
                                                    class="text-muted">{{ $user->last_login_at->format('M j, Y g:i A') }}</small>
                                            @else
                                                <span class="text-muted">Never</span>
                                            @endif
                                        </td>
                                        <td>{!! $user->status_badge !!}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                @if (auth()->user()->hasPermission('users.read'))
                                                    <a href="{{ route('users.show', $user) }}" class="btn btn-sm btn-info">
                                                        <i class="mdi mdi-eye"></i>
                                                    </a>
                                                @endif

                                                @if (auth()->user()->hasPermission('users.update'))
                                                    <a href="{{ route('users.edit', $user) }}"
                                                        class="btn btn-sm btn-warning">
                                                        <i class="mdi mdi-pencil"></i>
                                                    </a>
                                                @endif

                                                @if (auth()->user()->hasPermission('users.update') && $user->id !== auth()->id())
                                                    <button type="button"
                                                        class="btn btn-sm btn-{{ $user->is_active ? 'secondary' : 'success' }}"
                                                        onclick="toggleStatus({{ $user->id }})">
                                                        <i class="mdi mdi-{{ $user->is_active ? 'pause' : 'play' }}"></i>
                                                    </button>
                                                @endif

                                                @if (auth()->user()->hasPermission('users.delete') && $user->id !== auth()->id())
                                                    <form method="POST" action="{{ route('users.destroy', $user) }}"
                                                        style="display: inline;">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-danger"
                                                            onclick="return confirm('Are you sure you want to delete this user?')">
                                                            <i class="mdi mdi-delete"></i>
                                                        </button>
                                                    </form>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <i class="mdi mdi-account-outline h1 text-muted"></i>
                                            <h4 class="text-muted">No users found</h4>
                                            <p class="text-muted">No users match your current filters.</p>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <p class="text-muted">
                                Showing {{ $users->firstItem() ?? 0 }} to {{ $users->lastItem() ?? 0 }}
                                of {{ $users->total() }} users
                            </p>
                        </div>
                        <div>
                            {{ $users->appends(request()->query())->links() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        function toggleStatus(userId) {
            if (confirm('Are you sure you want to change the status of this user?')) {
                fetch(`/users/${userId}/toggle-status`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            location.reload();
                        } else {
                            alert(data.error || 'An error occurred');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred while updating the user status');
                    });
            }
        }
    </script>
@endsection
