@extends('layouts.master')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('quran.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('user-activities.index') }}">User Activities</a></li>
                    <li class="breadcrumb-item active">Export Activity Data</li>
                </ol>
            </div>
            <h4 class="page-title">Export Activity Data</h4>
        </div>
    </div>
</div>

<!-- Export Form -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">Export User Activities</h4>
                <p class="text-muted font-13 mb-4">
                    Export user activity data with custom filters and format options.
                </p>

                <form method="POST" action="{{ route('user-activities.export.filtered') }}">
                    @csrf
                    
                    <!-- Export Format -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="format">Export Format <span class="text-danger">*</span></label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="custom-control custom-radio">
                                            <input type="radio" id="format_csv" name="format" value="csv" class="custom-control-input" checked>
                                            <label class="custom-control-label" for="format_csv">
                                                <i class="mdi mdi-file-delimited text-success"></i> CSV Format
                                                <small class="d-block text-muted">Comma-separated values, compatible with Excel</small>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="custom-control custom-radio">
                                            <input type="radio" id="format_json" name="format" value="json" class="custom-control-input">
                                            <label class="custom-control-label" for="format_json">
                                                <i class="mdi mdi-code-json text-info"></i> JSON Format
                                                <small class="d-block text-muted">Structured data with metadata</small>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="custom-control custom-radio">
                                            <input type="radio" id="format_excel" name="format" value="excel" class="custom-control-input">
                                            <label class="custom-control-label" for="format_excel">
                                                <i class="mdi mdi-file-excel text-success"></i> Excel Format
                                                <small class="d-block text-muted">Microsoft Excel compatible</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="user_id">User</label>
                                <select name="user_id" id="user_id" class="form-control">
                                    <option value="">All Users</option>
                                    @foreach($users as $user)
                                        <option value="{{ $user->id }}">{{ $user->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="activity_type">Activity Type</label>
                                <select name="activity_type" id="activity_type" class="form-control">
                                    <option value="">All Types</option>
                                    @foreach($activityTypes as $type)
                                        <option value="{{ $type }}">{{ ucfirst(str_replace('_', ' ', $type)) }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="subject_type">Subject Type</label>
                                <select name="subject_type" id="subject_type" class="form-control">
                                    <option value="">All Subjects</option>
                                    @foreach($subjectTypes as $subjectType)
                                        <option value="{{ $subjectType }}">{{ class_basename($subjectType) }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="search">Search</label>
                                <input type="text" name="search" id="search" class="form-control" placeholder="Description, URL, IP...">
                            </div>
                        </div>
                    </div>

                    <!-- Date Range -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="date_from">Date From</label>
                                <input type="date" name="date_from" id="date_from" class="form-control">
                                <small class="form-text text-muted">Leave empty to include all dates</small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="date_to">Date To</label>
                                <input type="date" name="date_to" id="date_to" class="form-control">
                                <small class="form-text text-muted">Leave empty to include all dates</small>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="row">
                        <div class="col-12">
                            <button type="submit" class="btn btn-success">
                                <i class="mdi mdi-download"></i> Export Data
                            </button>
                            <a href="{{ route('user-activities.index') }}" class="btn btn-secondary">
                                <i class="mdi mdi-arrow-left"></i> Back to Activities
                            </a>
                            <a href="{{ route('user-activities.export') }}" class="btn btn-info">
                                <i class="mdi mdi-download"></i> Quick CSV Export (All Data)
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Export Information -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="mdi mdi-information text-info"></i> Export Information
                </h5>
                <ul class="list-unstyled">
                    <li><strong>CSV Format:</strong> Compatible with Excel, Google Sheets</li>
                    <li><strong>JSON Format:</strong> Includes metadata and structured data</li>
                    <li><strong>Excel Format:</strong> Native Excel file format</li>
                    <li><strong>Filters:</strong> Apply multiple filters to customize export</li>
                    <li><strong>Date Range:</strong> Export activities from specific time periods</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="mdi mdi-file-outline text-success"></i> Export Contents
                </h5>
                <ul class="list-unstyled">
                    <li>• Activity ID and timestamp</li>
                    <li>• User information</li>
                    <li>• Activity type and description</li>
                    <li>• Subject type and ID</li>
                    <li>• HTTP method and URL</li>
                    <li>• IP address and browser info</li>
                    <li>• Session ID and properties</li>
                </ul>
            </div>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Set default date range to last 30 days
    var today = new Date();
    var thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
    
    $('#date_to').val(today.toISOString().split('T')[0]);
    $('#date_from').val(thirtyDaysAgo.toISOString().split('T')[0]);
    
    // Form validation
    $('form').on('submit', function(e) {
        var dateFrom = $('#date_from').val();
        var dateTo = $('#date_to').val();
        
        if (dateFrom && dateTo && dateFrom > dateTo) {
            e.preventDefault();
            alert('Date From cannot be later than Date To');
            return false;
        }
        
        // Show loading state
        $(this).find('button[type="submit"]').prop('disabled', true).html('<i class="mdi mdi-loading mdi-spin"></i> Exporting...');
    });
});
</script>
@endsection
