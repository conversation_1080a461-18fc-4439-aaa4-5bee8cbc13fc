<?php

namespace App\Http\Controllers;

use App\Models\Role;
use App\Models\Permission;
use Illuminate\Http\Request;
use App\Services\ActivityLogger;

class RoleManagementController extends Controller
{
    /**
     * Display a listing of roles
     */
    public function index(Request $request)
    {
        $query = Role::with(['permissions', 'activeUsers']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('display_name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            } elseif ($request->status === 'system') {
                $query->system();
            } elseif ($request->status === 'custom') {
                $query->nonSystem();
            }
        }

        $roles = $query->ordered()->paginate(15);

        return view('roles.index', compact('roles'));
    }

    /**
     * Show the form for creating a new role
     */
    public function create()
    {
        $permissions = Permission::active()->ordered()->get()->groupBy('module');
        $colors = $this->getAvailableColors();

        return view('roles.create', compact('permissions', 'colors'));
    }

    /**
     * Store a newly created role
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255', 'unique:roles', 'regex:/^[a-z_]+$/'],
            'display_name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'color' => ['required', 'string', 'regex:/^#[0-9A-Fa-f]{6}$/'],
            'permissions' => ['array'],
            'permissions.*' => ['exists:permissions,id'],
            'is_active' => ['boolean'],
            'sort_order' => ['integer', 'min:0'],
        ]);

        $roleData = $request->except('permissions');
        $roleData['is_system'] = false; // Custom roles are never system roles

        $role = Role::create($roleData);

        // Assign permissions
        if ($request->filled('permissions')) {
            $role->permissions()->sync($request->permissions);
        }

        // Log activity
        ActivityLogger::log('role_create', "Created role: {$role->display_name}", $role, [
            'role_id' => $role->id,
            'permissions_assigned' => $request->permissions ?? [],
        ]);

        return redirect()->route('roles.index')
                        ->with('success', 'Role created successfully.');
    }

    /**
     * Display the specified role
     */
    public function show(Role $role)
    {
        $role->load(['permissions' => function ($query) {
            $query->ordered();
        }, 'activeUsers']);

        $permissionsByModule = $role->permissions->groupBy('module');

        return view('roles.show', compact('role', 'permissionsByModule'));
    }

    /**
     * Show the form for editing the specified role
     */
    public function edit(Role $role)
    {
        $role->load('permissions');
        $permissions = Permission::active()->ordered()->get()->groupBy('module');
        $colors = $this->getAvailableColors();

        return view('roles.edit', compact('role', 'permissions', 'colors'));
    }

    /**
     * Update the specified role
     */
    public function update(Request $request, Role $role)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255', 'unique:roles,name,' . $role->id, 'regex:/^[a-z_]+$/'],
            'display_name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'color' => ['required', 'string', 'regex:/^#[0-9A-Fa-f]{6}$/'],
            'permissions' => ['array'],
            'permissions.*' => ['exists:permissions,id'],
            'is_active' => ['boolean'],
            'sort_order' => ['integer', 'min:0'],
        ]);

        // Prevent editing system role properties
        $updateData = $request->except('permissions');
        if ($role->is_system) {
            $updateData = $request->only(['description', 'color', 'sort_order', 'permissions']);
        }

        $role->update($updateData);

        // Update permissions
        if ($request->has('permissions')) {
            $role->permissions()->sync($request->permissions ?? []);
        }

        // Log activity
        ActivityLogger::log('role_update', "Updated role: {$role->display_name}", $role, [
            'role_id' => $role->id,
            'permissions_assigned' => $request->permissions ?? [],
        ]);

        return redirect()->route('roles.index')
                        ->with('success', 'Role updated successfully.');
    }

    /**
     * Remove the specified role
     */
    public function destroy(Role $role)
    {
        // Prevent deletion of system roles
        if ($role->is_system) {
            return redirect()->route('roles.index')
                            ->with('error', 'System roles cannot be deleted.');
        }

        // Check if role has users
        if ($role->activeUsers()->count() > 0) {
            return redirect()->route('roles.index')
                            ->with('error', 'Cannot delete role that has active users assigned.');
        }

        // Log activity before deletion
        ActivityLogger::log('role_delete', "Deleted role: {$role->display_name}", $role, [
            'role_id' => $role->id,
            'role_name' => $role->name,
        ]);

        $role->delete();

        return redirect()->route('roles.index')
                        ->with('success', 'Role deleted successfully.');
    }

    /**
     * Toggle role status
     */
    public function toggleStatus(Role $role)
    {
        // Prevent deactivating system roles
        if ($role->is_system && $role->is_active) {
            return response()->json(['error' => 'System roles cannot be deactivated.'], 400);
        }

        $role->update(['is_active' => !$role->is_active]);

        $status = $role->is_active ? 'activated' : 'deactivated';
        
        // Log activity
        ActivityLogger::log('role_status_change', "Role {$status}: {$role->display_name}", $role, [
            'role_id' => $role->id,
            'new_status' => $role->is_active,
        ]);

        return response()->json([
            'success' => true,
            'message' => "Role {$status} successfully.",
            'status' => $role->is_active,
        ]);
    }

    /**
     * Clone a role
     */
    public function clone(Role $role)
    {
        $permissions = Permission::active()->ordered()->get()->groupBy('module');
        $colors = $this->getAvailableColors();

        return view('roles.clone', compact('role', 'permissions', 'colors'));
    }

    /**
     * Store cloned role
     */
    public function storeClone(Request $request, Role $originalRole)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255', 'unique:roles', 'regex:/^[a-z_]+$/'],
            'display_name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'color' => ['required', 'string', 'regex:/^#[0-9A-Fa-f]{6}$/'],
            'permissions' => ['array'],
            'permissions.*' => ['exists:permissions,id'],
            'is_active' => ['boolean'],
            'sort_order' => ['integer', 'min:0'],
        ]);

        $roleData = $request->except('permissions');
        $roleData['is_system'] = false; // Cloned roles are never system roles

        $role = Role::create($roleData);

        // Copy permissions from original role or use selected ones
        $permissions = $request->filled('permissions') 
            ? $request->permissions 
            : $originalRole->permissions->pluck('id')->toArray();

        $role->permissions()->sync($permissions);

        // Log activity
        ActivityLogger::log('role_clone', "Cloned role: {$role->display_name} from {$originalRole->display_name}", $role, [
            'role_id' => $role->id,
            'original_role_id' => $originalRole->id,
            'permissions_assigned' => $permissions,
        ]);

        return redirect()->route('roles.index')
                        ->with('success', 'Role cloned successfully.');
    }

    /**
     * Get available colors for roles
     */
    private function getAvailableColors(): array
    {
        return [
            '#007bff' => 'Blue',
            '#6c757d' => 'Gray',
            '#28a745' => 'Green',
            '#dc3545' => 'Red',
            '#ffc107' => 'Yellow',
            '#17a2b8' => 'Cyan',
            '#6f42c1' => 'Purple',
            '#e83e8c' => 'Pink',
            '#fd7e14' => 'Orange',
            '#20c997' => 'Teal',
            '#6610f2' => 'Indigo',
            '#343a40' => 'Dark',
        ];
    }
}
