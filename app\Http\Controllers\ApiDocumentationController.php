<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;

class ApiDocumentationController extends Controller
{
    /**
     * Show API documentation
     */
    public function documentation()
    {
        $documentationPath = public_path('API_DOCUMENTATION.md');
        
        if (!File::exists($documentationPath)) {
            abort(404, 'API documentation not found');
        }
        
        $content = File::get($documentationPath);
        
        return view('api.documentation', compact('content'));
    }

    /**
     * Show API tester
     */
    public function tester()
    {
        $testerPath = public_path('api-test.html');
        
        if (!File::exists($testerPath)) {
            abort(404, 'API tester not found');
        }
        
        return response()->file($testerPath);
    }

    /**
     * Download API quick reference
     */
    public function quickReference()
    {
        $quickRefPath = public_path('API_QUICK_REFERENCE.md');
        
        if (!File::exists($quickRefPath)) {
            abort(404, 'Quick reference not found');
        }
        
        return response()->download($quickRefPath, 'Al-Quran-API-Quick-Reference.md');
    }

    /**
     * Download Postman collection
     */
    public function postmanCollection()
    {
        $collectionPath = public_path('Al-Quran-API.postman_collection.json');
        
        if (!File::exists($collectionPath)) {
            abort(404, 'Postman collection not found');
        }
        
        return response()->download($collectionPath, 'Al-Quran-API.postman_collection.json');
    }

    /**
     * Get API status and statistics
     */
    public function status()
    {
        // Test a few key endpoints to check API health
        $endpoints = [
            'languages' => '/api/quran/languages',
            'topics' => '/api/topics',
            'chapters' => '/api/chapters',
        ];

        $status = [];
        $allWorking = true;

        foreach ($endpoints as $name => $endpoint) {
            try {
                $url = url($endpoint);
                $context = stream_context_create([
                    'http' => [
                        'timeout' => 5,
                        'method' => 'GET',
                    ]
                ]);
                
                $response = @file_get_contents($url, false, $context);
                $httpCode = 200;
                
                // Parse HTTP response code from headers
                if (isset($http_response_header)) {
                    $httpCode = (int) substr($http_response_header[0], 9, 3);
                }
                
                $status[$name] = [
                    'working' => $httpCode === 200,
                    'code' => $httpCode,
                    'endpoint' => $endpoint
                ];
                
                if ($httpCode !== 200) {
                    $allWorking = false;
                }
            } catch (\Exception $e) {
                $status[$name] = [
                    'working' => false,
                    'code' => 500,
                    'endpoint' => $endpoint,
                    'error' => $e->getMessage()
                ];
                $allWorking = false;
            }
        }

        return response()->json([
            'overall_status' => $allWorking ? 'operational' : 'issues',
            'timestamp' => now()->toISOString(),
            'endpoints' => $status,
            'statistics' => [
                'total_endpoints' => 14,
                'quran_endpoints' => 10,
                'topics_endpoints' => 2,
                'chapters_endpoints' => 2,
                'languages_supported' => 10,
            ]
        ]);
    }

    /**
     * API overview for developers
     */
    public function overview()
    {
        $overview = [
            'name' => 'Al-Quran API',
            'version' => '1.0.0',
            'description' => 'Complete API for accessing Quran data, translations, and related content',
            'base_url' => url('/api'),
            'documentation_url' => route('api.docs'),
            'tester_url' => route('api.test'),
            'endpoints' => [
                'quran' => [
                    'count' => 10,
                    'description' => 'Access Quran text, translations, and tafsir',
                    'examples' => [
                        'GET /api/quran/surah/1' => 'Get Surah Al-Fatiha',
                        'GET /api/quran/search?q=Allah' => 'Search for "Allah"',
                        'GET /api/quran/translation/1/1/en' => 'Get English translation of first ayah'
                    ]
                ],
                'topics' => [
                    'count' => 2,
                    'description' => 'Access and search topics',
                    'examples' => [
                        'GET /api/topics' => 'Get all topics',
                        'GET /api/topics/search?q=God' => 'Search topics for "God"'
                    ]
                ],
                'chapters' => [
                    'count' => 2,
                    'description' => 'Access and search chapters',
                    'examples' => [
                        'GET /api/chapters' => 'Get all chapters',
                        'GET /api/chapters/search?q=Alif' => 'Search chapters for "Alif"'
                    ]
                ]
            ],
            'languages' => [
                'supported' => ['bn', 'zh', 'en', 'es', 'fr', 'id', 'ru', 'sv', 'tr', 'ur'],
                'count' => 10,
                'endpoint' => '/api/quran/languages'
            ],
            'features' => [
                'Multi-language translations',
                'Full-text search',
                'Tafsir access',
                'RESTful design',
                'JSON responses',
                'Error handling',
                'No authentication required'
            ]
        ];

        return response()->json($overview);
    }
}
