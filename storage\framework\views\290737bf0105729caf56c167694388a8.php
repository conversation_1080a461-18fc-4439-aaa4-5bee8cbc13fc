<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('quran.dashboard')); ?>">Dashboard</a></li>
                    <li class="breadcrumb-item active">Backup & Data Management</li>
                </ol>
            </div>
            <h4 class="page-title">Backup & Data Management</h4>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row">
    <div class="col-md-3">
        <div class="card widget-flat">
            <div class="card-body">
                <div class="float-right">
                    <i class="mdi mdi-backup-restore widget-icon bg-success-lighten text-success"></i>
                </div>
                <h5 class="text-muted font-weight-normal mt-0" title="Total Backups">Total Backups</h5>
                <h3 class="mt-3 mb-3"><?php echo e($stats['total_backups']); ?></h3>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card widget-flat">
            <div class="card-body">
                <div class="float-right">
                    <i class="mdi mdi-harddisk widget-icon bg-info-lighten text-info"></i>
                </div>
                <h5 class="text-muted font-weight-normal mt-0" title="Total Size">Total Size</h5>
                <h3 class="mt-3 mb-3"><?php echo e(number_format($stats['total_size'] / 1024 / 1024, 2)); ?> MB</h3>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card widget-flat">
            <div class="card-body">
                <div class="float-right">
                    <i class="mdi mdi-clock-outline widget-icon bg-warning-lighten text-warning"></i>
                </div>
                <h5 class="text-muted font-weight-normal mt-0" title="Latest Backup">Latest Backup</h5>
                <h3 class="mt-3 mb-3">
                    <?php if($stats['latest_backup']): ?>
                        <?php echo e($stats['latest_backup']['created_at']->diffForHumans()); ?>

                    <?php else: ?>
                        Never
                    <?php endif; ?>
                </h3>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card widget-flat">
            <div class="card-body">
                <div class="float-right">
                    <i class="mdi mdi-file-multiple widget-icon bg-primary-lighten text-primary"></i>
                </div>
                <h5 class="text-muted font-weight-normal mt-0" title="Backup Types">Backup Types</h5>
                <h3 class="mt-3 mb-3"><?php echo e(count($stats['backup_types'])); ?></h3>
            </div>
        </div>
    </div>
</div>

<!-- Create Backup Section -->
<div class="row" id="create">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">Create New Backup</h4>
                
                <div class="row">
                    <div class="col-md-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="mdi mdi-database-export h1 text-primary"></i>
                                <h5 class="card-title">Full System Backup</h5>
                                <p class="card-text">Complete backup including database, files, and configuration.</p>
                                <form method="POST" action="<?php echo e(route('backups.create.full')); ?>" style="display: inline;">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="btn btn-primary" onclick="return confirm('This may take several minutes. Continue?')">
                                        <i class="mdi mdi-backup-restore"></i> Create Full Backup
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="mdi mdi-database h1 text-info"></i>
                                <h5 class="card-title">Database Only</h5>
                                <p class="card-text">Backup only the database structure and data.</p>
                                <form method="POST" action="<?php echo e(route('backups.create.database')); ?>" style="display: inline;">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="btn btn-info">
                                        <i class="mdi mdi-database-export"></i> Backup Database
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="mdi mdi-folder-zip h1 text-warning"></i>
                                <h5 class="card-title">Files Only</h5>
                                <p class="card-text">Backup uploaded files and storage content.</p>
                                <form method="POST" action="<?php echo e(route('backups.create.files')); ?>" style="display: inline;">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="btn btn-warning">
                                        <i class="mdi mdi-folder-zip-outline"></i> Backup Files
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="mdi mdi-download h1 text-success"></i>
                                <h5 class="card-title">Data Export</h5>
                                <p class="card-text">Export specific data in JSON format.</p>
                                <button type="button" class="btn btn-success" data-toggle="modal" data-target="#exportModal">
                                    <i class="mdi mdi-export"></i> Export Data
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Available Backups -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title">Available Backups</h4>
                <p class="text-muted font-13 mb-4">
                    Manage and download your backup files.
                </p>

                <div class="table-responsive">
                    <table class="table table-striped table-centered mb-0">
                        <thead>
                            <tr>
                                <th>Backup Name</th>
                                <th>Type</th>
                                <th>Size</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $backups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $backup): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <i class="mdi mdi-file-outline mr-2"></i>
                                        <?php echo e($backup['name']); ?>

                                    </td>
                                    <td>
                                        <span class="badge badge-soft-<?php echo e($backup['type'] == 'full' ? 'primary' : 
                                            ($backup['type'] == 'database' ? 'info' : 
                                            ($backup['type'] == 'files' ? 'warning' : 'success'))); ?>">
                                            <?php echo e(ucfirst($backup['type'])); ?>

                                        </span>
                                    </td>
                                    <td><?php echo e(number_format($backup['size'] / 1024 / 1024, 2)); ?> MB</td>
                                    <td>
                                        <span title="<?php echo e($backup['created_at']->format('Y-m-d H:i:s')); ?>">
                                            <?php echo e($backup['created_at']->diffForHumans()); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <a href="<?php echo e(route('backups.download', ['file' => $backup['name']])); ?>" 
                                           class="btn btn-xs btn-outline-primary">
                                            <i class="mdi mdi-download"></i> Download
                                        </a>
                                        
                                        <button type="button" class="btn btn-xs btn-outline-danger" 
                                                onclick="deleteBackup('<?php echo e($backup['name']); ?>')">
                                            <i class="mdi mdi-delete"></i> Delete
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="5" class="text-center">No backups found. Create your first backup above.</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Data Export Modal -->
<div class="modal fade" id="exportModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Export Data</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form method="POST" action="<?php echo e(route('backups.create.export')); ?>">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="export_type">Export Type</label>
                        <select name="export_type" id="export_type" class="form-control" required>
                            <option value="all">Complete Data Export</option>
                            <option value="audit_logs">Audit Logs Only</option>
                            <option value="user_activities">User Activities Only</option>
                            <option value="library_items">Library Items Only</option>
                            <option value="books">Books Only</option>
                            <option value="surahs">Surahs Only</option>
                            <option value="chapters">Chapters Only</option>
                            <option value="users">Users Only</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="date_from">Date From (Optional)</label>
                        <input type="date" name="date_from" id="date_from" class="form-control">
                    </div>

                    <div class="form-group">
                        <label for="date_to">Date To (Optional)</label>
                        <input type="date" name="date_to" id="date_to" class="form-control">
                    </div>

                    <div class="alert alert-info">
                        <i class="mdi mdi-information"></i>
                        Data will be exported in JSON format. Leave date fields empty to export all data.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="mdi mdi-export"></i> Export Data
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Cleanup Modal -->
<div class="modal fade" id="cleanupModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cleanup Old Backups</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form method="POST" action="<?php echo e(route('backups.cleanup')); ?>">
                <?php echo csrf_field(); ?>
                <?php echo method_field('DELETE'); ?>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="keep_days">Keep backups newer than (days):</label>
                        <input type="number" name="keep_days" id="keep_days" class="form-control" min="1" max="365" value="30" required>
                        <small class="form-text text-muted">Backups older than this will be permanently deleted.</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete Old Backups</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
$(document).ready(function() {
    // Add cleanup button to page
    $('.page-title-right').prepend('<button type="button" class="btn btn-warning btn-sm mr-2" data-toggle="modal" data-target="#cleanupModal"><i class="mdi mdi-broom"></i> Cleanup</button>');
});

function deleteBackup(filename) {
    if (confirm('Are you sure you want to delete this backup? This action cannot be undone.')) {
        // Create a form and submit it
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?php echo e(route("backups.delete")); ?>';
        
        // Add CSRF token
        var csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '<?php echo e(csrf_token()); ?>';
        form.appendChild(csrfToken);
        
        // Add method override
        var methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        form.appendChild(methodField);
        
        // Add filename
        var fileField = document.createElement('input');
        fileField.type = 'hidden';
        fileField.name = 'file';
        fileField.value = filename;
        form.appendChild(fileField);
        
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\alquran\resources\views/backups/index.blade.php ENDPATH**/ ?>