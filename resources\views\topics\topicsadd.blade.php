@extends('layouts.master')

@section('content')
    <!-- Row -->

<div class="row">
    <div class="col-lg-6">

        <div class="card-box">
            <h4 class="header-title">Add New Topic</h4>
            <p class="sub-header">
                Define Topics options in the system.
            </p>

            <form method="POST" class="parsley-examples" action="{{route('topics.store')}}">
                @csrf
                <div class="form-group">
                    <label for="topicname">Topic Name:<span class="text-danger">*</span></label>
                    <input type="text" name="topicname" parsley-trigger="change" required
                           placeholder="Enter language" class="form-control" id="topicname">
                </div>
                <div class="form-group">
                    <label for="topiccode">Topic Code: <span class="text-danger">*</span></label>
                    <input type="text" name="topiccode" parsley-trigger="change" required
                           placeholder="Enter topicuage" class="form-control" id="topiccode" value="">
                </div>

                <div class="form-group">
                    <label for="surahcode">Language <span class="text-danger">*</span></label>
                    <select class="select2 form-control select2" name="language_id" data-placeholder="">
                        @foreach($langs as $lang)
                            <option value="{{$lang->id}}" >{{$lang->name}}</option>
                        @endforeach
                    </select>
                </div>
                <div class="form-group">
                    <div class="checkbox checkbox-purple">
                        <input id="isactive" name="isactive" type="checkbox" value="1">
                        <label for="isactive">
                            Is Active
                        </label>
                    </div>

                </div>

                <div class="form-group text-right mb-0">
                    <button class="btn btn-primary waves-effect waves-light" type="submit"><i class="fas fa-save mr-1"></i>
                        Save Lanaguage
                    </button>
                    <button type="reset" onclick="window.history.go(-1);" class="btn btn-light waves-effect ml-1">
                        Cancel
                    </button>
                </div>

            </form>
        </div> <!-- end card-box -->
    </div>
</div>
@endsection
