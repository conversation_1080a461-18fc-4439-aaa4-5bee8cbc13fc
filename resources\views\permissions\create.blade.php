@extends('layouts.master')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('quran.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('permissions.index') }}">Permissions</a></li>
                    <li class="breadcrumb-item active">Create</li>
                </ol>
            </div>
            <h4 class="page-title">Create Permission</h4>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title">
                    <i class="mdi mdi-key-plus text-primary"></i> Create New Permission
                </h4>

                <form method="POST" action="{{ route('permissions.store') }}">
                    @csrf

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="display_name" class="form-label">Display Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('display_name') is-invalid @enderror" 
                                       id="display_name" name="display_name" value="{{ old('display_name') }}" required>
                                @error('display_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="module" class="form-label">Module <span class="text-danger">*</span></label>
                                <select class="form-control @error('module') is-invalid @enderror" id="module" name="module" required>
                                    <option value="">Select Module</option>
                                    @foreach($modules as $key => $value)
                                        <option value="{{ $key }}" {{ old('module') == $key ? 'selected' : '' }}>
                                            {{ $value }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('module')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="action" class="form-label">Action <span class="text-danger">*</span></label>
                                <select class="form-control @error('action') is-invalid @enderror" id="action" name="action" required>
                                    <option value="">Select Action</option>
                                    @foreach($actions as $key => $value)
                                        <option value="{{ $key }}" {{ old('action') == $key ? 'selected' : '' }}>
                                            {{ $value }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('action')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="resource" class="form-label">Resource (Optional)</label>
                                <input type="text" class="form-control @error('resource') is-invalid @enderror" 
                                       id="resource" name="resource" value="{{ old('resource') }}"
                                       placeholder="e.g., own, all, specific">
                                @error('resource')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Specify resource scope if needed</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="3">{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">Sort Order</label>
                                <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                       id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0">
                                @error('sort_order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1" 
                                           {{ old('is_active', true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        Active
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Preview -->
                    <div class="mb-3">
                        <label class="form-label">Permission Name Preview</label>
                        <div class="alert alert-info">
                            <code id="permission-preview">module.action[.resource]</code>
                        </div>
                    </div>

                    <div class="text-end">
                        <a href="{{ route('permissions.index') }}" class="btn btn-secondary me-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">Create Permission</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const moduleSelect = document.getElementById('module');
    const actionSelect = document.getElementById('action');
    const resourceInput = document.getElementById('resource');
    const preview = document.getElementById('permission-preview');

    function updatePreview() {
        const module = moduleSelect.value;
        const action = actionSelect.value;
        const resource = resourceInput.value;

        let permissionName = '';
        if (module && action) {
            permissionName = module + '.' + action;
            if (resource) {
                permissionName += '.' + resource;
            }
        } else {
            permissionName = 'module.action[.resource]';
        }

        preview.textContent = permissionName;
    }

    moduleSelect.addEventListener('change', updatePreview);
    actionSelect.addEventListener('change', updatePreview);
    resourceInput.addEventListener('input', updatePreview);

    // Initial preview update
    updatePreview();
});
</script>
@endsection
