<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('quran.dashboard')); ?>">Dashboard</a></li>
                    <li class="breadcrumb-item active">Audit Trails</li>
                </ol>
            </div>
            <h4 class="page-title">Audit Trails</h4>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row">
    <div class="col-md-3">
        <div class="card widget-flat">
            <div class="card-body">
                <div class="float-right">
                    <i class="mdi mdi-shield-check widget-icon bg-success-lighten text-success"></i>
                </div>
                <h5 class="text-muted font-weight-normal mt-0" title="Total Audits">Total Audits</h5>
                <h3 class="mt-3 mb-3"><?php echo e(number_format($stats['total'])); ?></h3>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card widget-flat">
            <div class="card-body">
                <div class="float-right">
                    <i class="mdi mdi-calendar-today widget-icon bg-info-lighten text-info"></i>
                </div>
                <h5 class="text-muted font-weight-normal mt-0" title="Today">Today</h5>
                <h3 class="mt-3 mb-3"><?php echo e(number_format($stats['today'])); ?></h3>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card widget-flat">
            <div class="card-body">
                <div class="float-right">
                    <i class="mdi mdi-calendar-week widget-icon bg-warning-lighten text-warning"></i>
                </div>
                <h5 class="text-muted font-weight-normal mt-0" title="This Week">This Week</h5>
                <h3 class="mt-3 mb-3"><?php echo e(number_format($stats['this_week'])); ?></h3>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card widget-flat">
            <div class="card-body">
                <div class="float-right">
                    <i class="mdi mdi-calendar-month widget-icon bg-primary-lighten text-primary"></i>
                </div>
                <h5 class="text-muted font-weight-normal mt-0" title="This Month">This Month</h5>
                <h3 class="mt-3 mb-3"><?php echo e(number_format($stats['this_month'])); ?></h3>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">Filter Audit Logs</h4>
                
                <form method="GET" action="<?php echo e(route('audits.index')); ?>" class="row">
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="user_id">User</label>
                            <select name="user_id" id="user_id" class="form-control">
                                <option value="">All Users</option>
                                <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($user->id); ?>" <?php echo e(request('user_id') == $user->id ? 'selected' : ''); ?>>
                                        <?php echo e($user->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="event">Event</label>
                            <select name="event" id="event" class="form-control">
                                <option value="">All Events</option>
                                <?php $__currentLoopData = $events; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($event); ?>" <?php echo e(request('event') == $event ? 'selected' : ''); ?>>
                                        <?php echo e(ucfirst($event)); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="model_type">Model Type</label>
                            <select name="model_type" id="model_type" class="form-control">
                                <option value="">All Models</option>
                                <?php $__currentLoopData = $modelTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $modelType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($modelType); ?>" <?php echo e(request('model_type') == $modelType ? 'selected' : ''); ?>>
                                        <?php echo e(class_basename($modelType)); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_from">Date From</label>
                            <input type="date" name="date_from" id="date_from" class="form-control" value="<?php echo e(request('date_from')); ?>">
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_to">Date To</label>
                            <input type="date" name="date_to" id="date_to" class="form-control" value="<?php echo e(request('date_to')); ?>">
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="search">Search</label>
                            <input type="text" name="search" id="search" class="form-control" placeholder="URL, IP..." value="<?php echo e(request('search')); ?>">
                        </div>
                    </div>

                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">Filter</button>
                        <a href="<?php echo e(route('audits.index')); ?>" class="btn btn-secondary">Clear</a>
                        <a href="<?php echo e(route('audits.export')); ?>?<?php echo e(http_build_query(request()->all())); ?>" class="btn btn-success">Export CSV</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Audit Logs Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title">Audit Logs</h4>
                <p class="text-muted font-13 mb-4">
                    Complete audit trail of all system changes and user actions.
                </p>

                <div class="table-responsive">
                    <table class="table table-striped table-centered mb-0">
                        <thead>
                            <tr>
                                <th>Date/Time</th>
                                <th>User</th>
                                <th>Event</th>
                                <th>Model</th>
                                <th>Changes</th>
                                <th>IP Address</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $audits; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $audit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <small><?php echo e($audit->created_at->format('Y-m-d H:i:s')); ?></small>
                                    </td>
                                    <td>
                                        <?php if($audit->user): ?>
                                            <span class="badge badge-soft-primary"><?php echo e($audit->user->name); ?></span>
                                        <?php else: ?>
                                            <span class="badge badge-soft-secondary">System</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge badge-soft-<?php echo e($audit->event == 'created' ? 'success' : ($audit->event == 'updated' ? 'warning' : 'danger')); ?>">
                                            <?php echo e($audit->formatted_event); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <span class="text-muted"><?php echo e($audit->model_name); ?></span>
                                        <small class="d-block">#<?php echo e($audit->auditable_id); ?></small>
                                    </td>
                                    <td>
                                        <?php if($audit->changes): ?>
                                            <small class="text-muted"><?php echo e(count($audit->changes)); ?> field(s) changed</small>
                                        <?php else: ?>
                                            <small class="text-muted">-</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small class="text-muted"><?php echo e($audit->ip_address); ?></small>
                                    </td>
                                    <td>
                                        <a href="<?php echo e(route('audits.show', $audit)); ?>" class="btn btn-xs btn-outline-primary">
                                            <i class="mdi mdi-eye"></i> View
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="7" class="text-center">No audit logs found.</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="mt-3">
                    <?php echo e($audits->appends(request()->query())->links()); ?>

                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cleanup Modal -->
<div class="modal fade" id="cleanupModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cleanup Old Audit Logs</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form method="POST" action="<?php echo e(route('audits.cleanup')); ?>">
                <?php echo csrf_field(); ?>
                <?php echo method_field('DELETE'); ?>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="days">Delete audit logs older than (days):</label>
                        <input type="number" name="days" id="days" class="form-control" min="1" max="365" value="90" required>
                        <small class="form-text text-muted">This action cannot be undone.</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete Old Logs</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
$(document).ready(function() {
    // Add cleanup button to page
    $('.page-title-right').prepend('<button type="button" class="btn btn-danger btn-sm mr-2" data-toggle="modal" data-target="#cleanupModal"><i class="mdi mdi-delete"></i> Cleanup</button>');
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\alquran\resources\views/audits/index.blade.php ENDPATH**/ ?>