<!-- ========== Left Sidebar Start ========== -->
<div class="left-side-menu">

    <div class="slimscroll-menu">

        <!--- Sidemenu -->
        <div id="sidebar-menu">

            <ul class="metismenu" id="side-menu">

                <li class="menu-title">Navigation</li>

                <li>
                    <a href="<?php echo e(route('quran.dashboard')); ?>">
                        <i class="fe-grid"></i>
                        <span> Quran Dashboard </span>
                    </a>
                </li>

                <li>
                    <a href="javascript: void(0);">
                        <i class="mdi mdi-translate"></i>
                        <span>Languages</span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="<?php echo e(route('langs.index')); ?>">List of Languages</a></li>
                        <li><a href="<?php echo e(route('langs.create')); ?>">Add Language</a></li>
                    </ul>
                </li>

                <li>
                    <a href="javascript: void(0);">
                        <i class="mdi mdi-book-open-page-variant"></i>
                        <span>Chapters </span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="<?php echo e(route('chapts.index')); ?>">List of Chapter</a></li>
                        <li><a href="<?php echo e(route('chapts.create')); ?>">Add Chapter</a></li>
                    </ul>
                </li>

                <li>
                    <a href="javascript: void(0);">
                        <i class="fe-book"></i>
                        <span>Quran Surah</span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="<?php echo e(route('surahs.index')); ?>">List of Surah</a></li>
                        <li><a href="<?php echo e(route('surahs.create')); ?>">Add Surah</a></li>
                    </ul>
                </li>

                <li>
                    <a href="javascript: void(0);">
                        <i class="mdi mdi-sitemap"></i>
                        <span> Topics </span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="<?php echo e(route('topics.index')); ?>">List of Topics</a></li>
                        <li><a href="<?php echo e(route('topics.create')); ?>">Add Topic</a></li>
                    </ul>
                </li>

                <li>
                    <a href="javascript: void(0);">
                        <i class="fe-file-text"></i>
                        <span> Topic Details </span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="<?php echo e(route('topicdetails.index')); ?>">List of Topic Details</a></li>
                        <li><a href="<?php echo e(route('topicdetails.create')); ?>">Add Topic Details</a></li>
                    </ul>
                </li>

                

                <li>
                    <a href="javascript: void(0);">
                        <i class="mdi mdi-library"></i>
                        <span>Library</span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="<?php echo e(route('library.index')); ?>">All Library Items</a></li>
                        <li><a href="<?php echo e(route('library.create')); ?>">Add New Item</a></li>
                    </ul>
                </li>

                <li class="menu-title">System Monitoring</li>

                <li>
                    <a href="javascript: void(0);">
                        <i class="mdi mdi-shield-check"></i>
                        <span>Audit Trails</span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="<?php echo e(route('audits.index')); ?>">View Audit Logs</a></li>
                        <li><a href="<?php echo e(route('audits.export')); ?>">Export Audit Data</a></li>
                    </ul>
                </li>

                <li>
                    <a href="javascript: void(0);">
                        <i class="mdi mdi-account-clock"></i>
                        <span>User Activities</span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="<?php echo e(route('user-activities.index')); ?>">View Activities</a></li>
                        <li><a href="<?php echo e(route('user-activities.dashboard')); ?>">Activity Dashboard</a></li>
                        <li><a href="<?php echo e(route('user-activities.export-form')); ?>">Export Activity Data</a></li>
                    </ul>
                </li>

                <li>
                    <a href="javascript: void(0);">
                        <i class="mdi mdi-backup-restore"></i>
                        <span>Backup & Data</span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="<?php echo e(route('backups.index')); ?>"><i class="mdi mdi-folder-multiple"></i> Backup
                                Management</a></li>
                        <li><a href="javascript:void(0);" onclick="scrollToSection('create')"><i
                                    class="mdi mdi-plus-circle"></i> Create Backup</a></li>
                        <li><a href="javascript:void(0);" onclick="scrollToSection('export')"><i
                                    class="mdi mdi-export"></i> Export Data</a></li>
                        <li><a href="<?php echo e(route('backups.restore.form')); ?>"><i class="mdi mdi-restore"></i> Restore from
                                Backup</a></li>
                    </ul>
                </li>

                <!-- API Documentation & Testing -->
                <?php if(auth()->check() &&
                        auth()->user()->hasAnyPermission(['api.read', 'api.manage'])): ?>
                    <li class="has_sub">
                        <a href="javascript:void(0);" class="waves-effect">
                            <i class="mdi mdi-book-open"></i>
                            <span>API & Documentation</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level" aria-expanded="false">
                            <?php if(auth()->user()->hasPermission('api.read')): ?>
                                <li><a href="<?php echo e(route('api.docs')); ?>"><i class="mdi mdi-book-open-page-variant"></i>
                                        API
                                        Documentation</a></li>
                                <li><a href="<?php echo e(route('api.test')); ?>" target="_blank"><i class="mdi mdi-test-tube"></i>
                                        API
                                        Tester</a></li>
                                <li><a href="<?php echo e(route('api.quick.reference')); ?>"><i class="mdi mdi-lightning-bolt"></i>
                                        Quick
                                        Reference</a></li>
                                <li><a href="<?php echo e(route('api.postman.collection')); ?>"><i class="mdi mdi-download"></i>
                                        Postman
                                        Collection</a></li>
                                <li><a href="<?php echo e(route('api.status')); ?>" target="_blank"><i
                                            class="mdi mdi-heart-pulse"></i> API
                                        Status</a></li>
                                <li><a href="<?php echo e(route('api.overview')); ?>" target="_blank"><i
                                            class="mdi mdi-chart-line"></i>
                                        API Overview</a></li>
                            <?php endif; ?>
                        </ul>
                    </li>
                <?php endif; ?>

                <!-- User & Rights Management -->
                <?php if(auth()->check() &&
                        auth()->user()->hasAnyPermission(['users.manage', 'roles.manage', 'permissions.manage'])): ?>
                    <li class="has_sub">
                        <a href="javascript:void(0);" class="waves-effect">
                            <i class="mdi mdi-account-multiple"></i>
                            <span>User Management</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level" aria-expanded="false">
                            <?php if(auth()->user()->hasPermission('users.manage')): ?>
                                <li><a href="<?php echo e(route('users.index')); ?>"><i class="mdi mdi-account-multiple"></i>
                                        Users</a></li>
                            <?php endif; ?>
                            <?php if(auth()->user()->hasPermission('roles.manage')): ?>
                                <li><a href="<?php echo e(route('roles.index')); ?>"><i class="mdi mdi-account-group"></i>
                                        Roles</a></li>
                            <?php endif; ?>
                            <?php if(auth()->user()->hasPermission('permissions.manage')): ?>
                                <li><a href="<?php echo e(route('permissions.index')); ?>"><i class="mdi mdi-key"></i>
                                        Permissions</a></li>
                            <?php endif; ?>
                        </ul>
                    </li>
                <?php endif; ?>

            </ul>

        </div>
        <!-- End Sidebar -->

        <div class="clearfix"></div>

    </div>
    <!-- Sidebar -left -->

</div>
<!-- Left Sidebar End -->
<?php /**PATH C:\laragon\www\alquran\resources\views/include/sidebar.blade.php ENDPATH**/ ?>