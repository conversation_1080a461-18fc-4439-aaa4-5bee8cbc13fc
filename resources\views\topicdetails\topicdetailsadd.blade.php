@extends('layouts.master')

@section('content')
    <!-- Row -->

<div class="row">
    <div class="col-lg-6">

        <div class="card-box">
            <h4 class="header-title">Add New Topic Detail</h4>
            <p class="sub-header">
                Define Topics options in the system.
            </p>

            <form method="POST" class="parsley-examples" action="{{route('topicdetails.store')}}">
                @csrf
                <div class="form-group">
                    <label for="topic_id">Topic Name:<span class="text-danger">*</span></label>
                    <select class="select2 form-control select2" name="topic_id" data-placeholder="">
                        @foreach($topics as $topic)
                            <option value="{{$topic->id}}|{{$topic->topiccode}}" >{{$topic->topicname}}</option>
                        @endforeach
                    </select>
                </div>
                <div class="form-group">
                    <label for="surah_id">Surah Name <span class="text-danger">*</span></label>
                    <select class="select2 form-control select2" name="surah_id" data-placeholder="">
                        @foreach($surahs as $surah)
                            <option value="{{$surah->id}}|{{$surah->surahcode}}" >{{$surah->surahname}}</option>
                        @endforeach
                    </select>
                </div>
                <div class="form-group">
                    <label for="ayatdetail">Ayat Numbers: <span class="text-danger">*</span></label>
                    <textarea name="ayatdetail" id="ayatdetail" cols="96" rows="10"></textarea>
                </div>

                <div class="form-group">
                    <div class="checkbox checkbox-purple">
                        <input id="isactive" name="isactive" type="checkbox" value="1">
                        <label for="isactive">
                            Is Active
                        </label>
                    </div>

                </div>

                <div class="form-group text-right mb-0">
                    <button class="btn btn-primary waves-effect waves-light" type="submit"><i class="fas fa-save mr-1"></i>
                        Save Lanaguage
                    </button>
                    <button type="reset" onclick="window.history.go(-1);" class="btn btn-light waves-effect ml-1">
                        Cancel
                    </button>
                </div>

            </form>
        </div> <!-- end card-box -->
    </div>
</div>
@endsection
