<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use App\Models\Quran;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\DB;

/*
|--------------------------------------------------------------------------
| Console Routes
|--------------------------------------------------------------------------
|
| This file is where you may define all of your Closure based console
| commands. Each Closure is bound to a command instance allowing a
| simple approach to interacting with each command's IO methods.
|
*/

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

Artisan::command('import:quran-translation {language} {--directory=json-files}', function ($language, $directory) {
    $column = match ($language) {
        'bn' => 'translation_bn', //Bengali
        'zh' => 'translation_zh', //Chinese
        'en' => 'translation_en', //English
        'es' => 'translation_es', //Spanish
        'fr' => 'translation_fr', //French
        'id' => 'translation_id', //Indonesian
        'ru' => 'translation_ru', //Russian
        'sv' => 'translation_sv', //Swedish
        'tr' => 'translation_tr', //Turkish
        'ur' => 'translation_ur', //Urdu
        default => null,
    };

    if (!$column) {
        $this->error('Invalid language. Supported: bn, zh, en, es, fr, id, ru, sv, tr, ur');
        return 1;
    }

    // Check if directory exists
    if (!File::isDirectory($directory)) {
        $this->error("Directory not found: $directory");
        return 1;
    }

    // Find the JSON file for the specified language
    $jsonFiles = File::glob("$directory/*$language*.json");

    if (empty($jsonFiles)) {
        $this->error("No JSON files found for language '$language' in directory '$directory'");
        return 1;
    }

    $filePath = $jsonFiles[0];
    $this->info("Using file: $filePath");

    $this->info("Reading Quran Translation JSON file for language: $language...");
    $translationData = json_decode(File::get($filePath), true);

    if (!$translationData) {
        $this->error('Invalid JSON format.');
        return 1;
    }

    $this->info("Saving Quran Translation ($language) into database...");
    $bar = $this->output->createProgressBar(count($translationData));
    $bar->start();

    DB::beginTransaction();
    try {
        // Determine the format of the JSON file
        $firstKey = array_key_first($translationData);
        $isChapterVerseFormat = strpos($firstKey, ':') !== false;

        if ($isChapterVerseFormat) {
            // Format: "1:1": {"translation": "text"}
            foreach ($translationData as $ayahKey => $ayahInfo) {
                [$surah, $ayah] = explode(':', $ayahKey);
                $translationText = $ayahInfo['translation'] ?? $ayahInfo['text'] ?? '';

                if (strlen($translationText) > 65000) {
                    $translationText = substr($translationText, 0, 65000);
                }

                Quran::where('surah_number', $surah)
                    ->where('ayah_number', $ayah)
                    ->update([$column => $translationText]);

                $bar->advance();
            }
        } else {
            // Format: [{"id": 1, "name": "Al-Fatiha", "verses": [{"id": 1, "text": "..."}]}]
            foreach ($translationData as $surahIndex => $surah) {
                $surahNumber = $surah['id'] ?? ($surahIndex + 1);

                foreach ($surah['verses'] as $verseIndex => $verse) {
                    $ayahNumber = $verse['id'] ?? ($verseIndex + 1);
                    $translationText = $verse['text'] ?? $verse['translation'] ?? '';

                    if (strlen($translationText) > 65000) {
                        $translationText = substr($translationText, 0, 65000);
                    }

                    Quran::where('surah_number', $surahNumber)
                        ->where('ayah_number', $ayahNumber)
                        ->update([$column => $translationText]);

                    $bar->advance();
                }
            }
        }

        DB::commit();
        $bar->finish();
        $this->newLine();
        $this->info("Quran Translation ($language) imported successfully!");
        return 0;
    } catch (\Exception $e) {
        DB::rollBack();
        $this->error('Error importing translation: ' . $e->getMessage());
        return 1;
    }
})->purpose('Import Quran translations in multiple languages from JSON files into the database');
