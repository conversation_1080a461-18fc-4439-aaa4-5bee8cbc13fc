<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LibraryItem extends Model
{
    use HasFactory;

    protected $table = 'library_items';

    protected $fillable = [
        'title',
        'author',
        'description',
        'media_type',
        'thumbnail_path',
        'file_path',
        'file_size',
        'external_url',
        'duration',
        'language',
        'category',
        'keywords',
        'isactive',
        'created_by',
        'updated_by'
    ];

    /**
     * Get formatted file size (KB, MB, etc.)
     */
    public function getFormattedFileSizeAttribute()
    {
        if (!$this->file_size) {
            return 'N/A';
        }

        $bytes = (float)$this->file_size; // Ensure bytes is a float
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        $bytes = max($bytes, 0);

        // Only calculate log if bytes is greater than 0
        if ($bytes > 0) {
            $pow = floor(log($bytes, 1024));
        } else {
            $pow = 0;
        }

        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Get thumbnail URL or default placeholder
     */
    public function getThumbnailUrl()
    {
        if ($this->thumbnail_path) {
            return asset('storage/' . $this->thumbnail_path);
        }

        // Return default placeholder based on media type
        switch ($this->media_type) {
            case 'pdf':
                return asset('assets/images/pdf-placeholder.jpg');
            case 'video':
                return asset('assets/images/video-placeholder.jpg');
            case 'audio':
                return asset('assets/images/audio-placeholder.jpg');
            default:
                return asset('assets/images/file-placeholder.jpg');
        }
    }

    /**
     * Check if this is a PDF file
     */
    public function isPdf()
    {
        return $this->media_type === 'pdf';
    }

    /**
     * Check if this is a video file
     */
    public function isVideo()
    {
        return $this->media_type === 'video';
    }

    /**
     * Check if this is an audio file
     */
    public function isAudio()
    {
        return $this->media_type === 'audio';
    }

    /**
     * Get keywords as an array
     */
    public function getKeywordsArrayAttribute()
    {
        if (empty($this->keywords)) {
            return [];
        }

        return array_map('trim', explode(',', $this->keywords));
    }
}
