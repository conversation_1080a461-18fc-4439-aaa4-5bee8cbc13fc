@extends('layouts.master')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('quran.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">User Activities</li>
                    </ol>
                </div>
                <h4 class="page-title">User Activities</h4>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-md-3">
            <div class="card widget-flat">
                <div class="card-body">
                    <div class="float-right">
                        <i class="mdi mdi-account-clock widget-icon bg-success-lighten text-success"></i>
                    </div>
                    <h5 class="text-muted font-weight-normal mt-0" title="Total Activities">Total Activities</h5>
                    <h3 class="mt-3 mb-3">{{ number_format($stats['total']) }}</h3>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card widget-flat">
                <div class="card-body">
                    <div class="float-right">
                        <i class="mdi mdi-calendar-today widget-icon bg-info-lighten text-info"></i>
                    </div>
                    <h5 class="text-muted font-weight-normal mt-0" title="Today">Today</h5>
                    <h3 class="mt-3 mb-3">{{ number_format($stats['today']) }}</h3>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card widget-flat">
                <div class="card-body">
                    <div class="float-right">
                        <i class="mdi mdi-calendar-week widget-icon bg-warning-lighten text-warning"></i>
                    </div>
                    <h5 class="text-muted font-weight-normal mt-0" title="This Week">This Week</h5>
                    <h3 class="mt-3 mb-3">{{ number_format($stats['this_week']) }}</h3>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card widget-flat">
                <div class="card-body">
                    <div class="float-right">
                        <i class="mdi mdi-calendar-month widget-icon bg-primary-lighten text-primary"></i>
                    </div>
                    <h5 class="text-muted font-weight-normal mt-0" title="This Month">This Month</h5>
                    <h3 class="mt-3 mb-3">{{ number_format($stats['this_month']) }}</h3>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Filter User Activities</h4>

                    <form method="GET" action="{{ route('user-activities.index') }}" class="row">
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="user_id">User</label>
                                <select name="user_id" id="user_id" class="form-control">
                                    <option value="">All Users</option>
                                    @foreach ($users as $user)
                                        <option value="{{ $user->id }}"
                                            {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                            {{ $user->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="activity_type">Activity Type</label>
                                <select name="activity_type" id="activity_type" class="form-control">
                                    <option value="">All Types</option>
                                    @foreach ($activityTypes as $type)
                                        <option value="{{ $type }}"
                                            {{ request('activity_type') == $type ? 'selected' : '' }}>
                                            {{ ucfirst(str_replace('_', ' ', $type)) }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="subject_type">Subject Type</label>
                                <select name="subject_type" id="subject_type" class="form-control">
                                    <option value="">All Subjects</option>
                                    @foreach ($subjectTypes as $subjectType)
                                        <option value="{{ $subjectType }}"
                                            {{ request('subject_type') == $subjectType ? 'selected' : '' }}>
                                            {{ class_basename($subjectType) }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="date_from">Date From</label>
                                <input type="date" name="date_from" id="date_from" class="form-control"
                                    value="{{ request('date_from') }}">
                            </div>
                        </div>

                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="date_to">Date To</label>
                                <input type="date" name="date_to" id="date_to" class="form-control"
                                    value="{{ request('date_to') }}">
                            </div>
                        </div>

                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="search">Search</label>
                                <input type="text" name="search" id="search" class="form-control"
                                    placeholder="Description, URL..." value="{{ request('search') }}">
                            </div>
                        </div>

                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">Filter</button>
                            <a href="{{ route('user-activities.index') }}" class="btn btn-secondary">Clear</a>
                            <a href="{{ route('user-activities.export') }}?{{ http_build_query(request()->all()) }}"
                                class="btn btn-success">Export CSV</a>
                            <a href="{{ route('user-activities.dashboard') }}" class="btn btn-info">Dashboard</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Activities Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title">User Activities</h4>
                    <p class="text-muted font-13 mb-4">
                        Complete log of all user activities and system interactions.
                    </p>

                    <div class="table-responsive">
                        <table id="activitiesTable" class="table table-striped table-centered mb-0">
                            <thead>
                                <tr>
                                    <th>Date/Time</th>
                                    <th>User</th>
                                    <th>Activity</th>
                                    <th>Description</th>
                                    <th>Subject</th>
                                    <th>IP Address</th>
                                    <th>Browser</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($activities as $activity)
                                    <tr>
                                        <td data-order="{{ $activity->created_at->timestamp }}">
                                            <small>{{ $activity->created_at->format('Y-m-d H:i:s') }}</small>
                                        </td>
                                        <td>
                                            @if ($activity->user)
                                                <span class="badge badge-soft-primary">{{ $activity->user->name }}</span>
                                            @else
                                                <span class="badge badge-soft-secondary">Guest</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span
                                                class="badge badge-soft-{{ $activity->activity_type == 'login'
                                                    ? 'success'
                                                    : ($activity->activity_type == 'logout'
                                                        ? 'warning'
                                                        : ($activity->activity_type == 'delete'
                                                            ? 'danger'
                                                            : 'info')) }}">
                                                {{ $activity->formatted_activity_type }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="text-muted"
                                                title="{{ $activity->description }}">{{ Str::limit($activity->description, 50) }}</span>
                                        </td>
                                        <td>
                                            @if ($activity->subject_type)
                                                <span class="text-muted">{{ $activity->subject_name }}</span>
                                                <small class="d-block">#{{ $activity->subject_id }}</small>
                                            @else
                                                <small class="text-muted">-</small>
                                            @endif
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ $activity->ip_address }}</small>
                                        </td>
                                        <td>
                                            <small class="text-muted"
                                                title="{{ $activity->user_agent }}">{{ $activity->browser }}</small>
                                        </td>
                                        <td>
                                            <a href="{{ route('user-activities.show', $activity) }}"
                                                class="btn btn-xs btn-outline-primary">
                                                <i class="mdi mdi-eye"></i> View
                                            </a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center">No activities found.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cleanup Modal -->
    <div class="modal fade" id="cleanupModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Cleanup Old Activity Logs</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form method="POST" action="{{ route('user-activities.cleanup') }}">
                    @csrf
                    @method('DELETE')
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="days">Delete activity logs older than (days):</label>
                            <input type="number" name="days" id="days" class="form-control" min="1"
                                max="365" value="90" required>
                            <small class="form-text text-muted">This action cannot be undone.</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">Delete Old Logs</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <!-- DataTables CSS -->
    <link href="{{ asset('assets/libs/datatables/dataTables.bootstrap4.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ asset('assets/libs/datatables/responsive.bootstrap4.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ asset('assets/libs/datatables/buttons.bootstrap4.css') }}" rel="stylesheet" type="text/css" />

    <!-- DataTables JS -->
    <script src="{{ asset('assets/libs/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('assets/libs/datatables/dataTables.bootstrap4.js') }}"></script>
    <script src="{{ asset('assets/libs/datatables/dataTables.responsive.min.js') }}"></script>
    <script src="{{ asset('assets/libs/datatables/responsive.bootstrap4.min.js') }}"></script>
    <script src="{{ asset('assets/libs/datatables/dataTables.buttons.min.js') }}"></script>
    <script src="{{ asset('assets/libs/datatables/buttons.bootstrap4.min.js') }}"></script>
    <script src="{{ asset('assets/libs/datatables/buttons.html5.min.js') }}"></script>
    <script src="{{ asset('assets/libs/datatables/buttons.flash.min.js') }}"></script>
    <script src="{{ asset('assets/libs/datatables/buttons.print.min.js') }}"></script>

    <script>
        $(document).ready(function() {
            // Add cleanup button to page
            $('.page-title-right').prepend(
                '<button type="button" class="btn btn-danger btn-sm mr-2" data-toggle="modal" data-target="#cleanupModal"><i class="mdi mdi-delete"></i> Cleanup</button>'
            );

            // Initialize DataTable
            $('#activitiesTable').DataTable({
                "pageLength": 25,
                "lengthMenu": [
                    [10, 25, 50, 100, -1],
                    [10, 25, 50, 100, "All"]
                ],
                "order": [
                    [0, "desc"]
                ], // Sort by date/time descending
                "responsive": true,
                "dom": 'Bfrtip',
                "buttons": [{
                        extend: 'copy',
                        className: 'btn btn-secondary btn-sm'
                    },
                    {
                        extend: 'csv',
                        className: 'btn btn-success btn-sm',
                        title: 'User Activities Export'
                    },
                    {
                        extend: 'excel',
                        className: 'btn btn-success btn-sm',
                        title: 'User Activities Export'
                    },
                    {
                        extend: 'pdf',
                        className: 'btn btn-danger btn-sm',
                        title: 'User Activities Export',
                        orientation: 'landscape',
                        pageSize: 'A4'
                    },
                    {
                        extend: 'print',
                        className: 'btn btn-info btn-sm',
                        title: 'User Activities Report'
                    }
                ],
                "columnDefs": [{
                        "targets": [7], // Actions column
                        "orderable": false,
                        "searchable": false
                    },
                    {
                        "targets": [0], // Date column
                        "type": "num" // For proper sorting
                    }
                ],
                "language": {
                    "search": "Search activities:",
                    "lengthMenu": "Show _MENU_ activities per page",
                    "info": "Showing _START_ to _END_ of _TOTAL_ activities",
                    "infoEmpty": "No activities found",
                    "infoFiltered": "(filtered from _MAX_ total activities)",
                    "emptyTable": "No user activities available",
                    "zeroRecords": "No matching activities found"
                }
            });
        });
    </script>
@endsection
