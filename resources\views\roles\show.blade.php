@extends('layouts.master')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('quran.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('roles.index') }}">Roles</a></li>
                    <li class="breadcrumb-item active">{{ $role->display_name }}</li>
                </ol>
            </div>
            <h4 class="page-title">Role Details</h4>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 class="header-title">
                        <i class="mdi mdi-account-group text-primary"></i> {{ $role->display_name }}
                    </h4>
                    <div>
                        @if(auth()->user()->hasPermission('roles.update'))
                            <a href="{{ route('roles.edit', $role) }}" class="btn btn-warning">
                                <i class="mdi mdi-pencil"></i> Edit
                            </a>
                        @endif
                        @if(auth()->user()->hasPermission('roles.create'))
                            <a href="{{ route('roles.clone', $role) }}" class="btn btn-secondary">
                                <i class="mdi mdi-content-copy"></i> Clone
                            </a>
                        @endif
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">Name:</th>
                                <td><code>{{ $role->name }}</code></td>
                            </tr>
                            <tr>
                                <th>Display Name:</th>
                                <td>{!! $role->color_badge !!}</td>
                            </tr>
                            <tr>
                                <th>Status:</th>
                                <td>{!! $role->badge !!}</td>
                            </tr>
                            <tr>
                                <th>Type:</th>
                                <td>
                                    @if($role->is_system)
                                        <span class="badge badge-danger">System</span>
                                    @else
                                        <span class="badge badge-info">Custom</span>
                                    @endif
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">Sort Order:</th>
                                <td>{{ $role->sort_order }}</td>
                            </tr>
                            <tr>
                                <th>Permissions:</th>
                                <td><span class="badge badge-info">{{ $role->permissions->count() }}</span></td>
                            </tr>
                            <tr>
                                <th>Active Users:</th>
                                <td><span class="badge badge-success">{{ $role->activeUsers->count() }}</span></td>
                            </tr>
                            <tr>
                                <th>Created:</th>
                                <td>{{ $role->created_at->format('M j, Y g:i A') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                @if($role->description)
                    <div class="mt-3">
                        <h6>Description:</h6>
                        <p class="text-muted">{{ $role->description }}</p>
                    </div>
                @endif

                <!-- Permissions by Module -->
                <div class="mt-4">
                    <h5>
                        <i class="mdi mdi-key"></i> Permissions
                        <span class="badge badge-primary ms-2">{{ $role->permissions->count() }}</span>
                    </h5>

                    @if($permissionsByModule->count() > 0)
                        <div class="row">
                            @foreach($permissionsByModule as $module => $permissions)
                                <div class="col-md-6 mb-3">
                                    <div class="card border">
                                        <div class="card-header bg-light py-2">
                                            <h6 class="mb-0">
                                                <span class="badge badge-secondary">{{ ucfirst($module) }}</span>
                                                <small class="text-muted">({{ $permissions->count() }} permissions)</small>
                                            </h6>
                                        </div>
                                        <div class="card-body py-2">
                                            <div class="row">
                                                @foreach($permissions as $permission)
                                                    <div class="col-6">
                                                        <small>
                                                            {!! $permission->action_badge !!}
                                                            @if(!$permission->is_active)
                                                                <i class="mdi mdi-pause-circle text-muted" title="Inactive"></i>
                                                            @endif
                                                        </small>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="mdi mdi-key-outline h2 text-muted"></i>
                            <p class="text-muted">No permissions assigned to this role</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Users with this role -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="mdi mdi-account-multiple"></i> Users with Role
                    <span class="badge badge-primary ms-2">{{ $role->activeUsers->count() }}</span>
                </h5>

                @if($role->activeUsers->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($role->activeUsers->take(10) as $user)
                            <div class="list-group-item d-flex align-items-center px-0">
                                <img src="{{ $user->avatar_url }}" alt="{{ $user->name }}" 
                                     class="rounded-circle me-2" width="32" height="32">
                                <div class="flex-grow-1">
                                    <div class="fw-bold">{{ $user->full_name }}</div>
                                    <small class="text-muted">{{ $user->email }}</small>
                                    @if($user->pivot->expires_at)
                                        <br><small class="text-warning">
                                            <i class="mdi mdi-clock-outline"></i>
                                            Expires: {{ $user->pivot->expires_at->format('M j, Y') }}
                                        </small>
                                    @endif
                                </div>
                                <div>
                                    {!! $user->status_badge !!}
                                    @if(auth()->user()->hasPermission('users.read'))
                                        <a href="{{ route('users.show', $user) }}" class="btn btn-sm btn-outline-primary ms-1">
                                            <i class="mdi mdi-eye"></i>
                                        </a>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>

                    @if($role->activeUsers->count() > 10)
                        <div class="text-center mt-2">
                            <small class="text-muted">
                                +{{ $role->activeUsers->count() - 10 }} more users
                            </small>
                        </div>
                    @endif
                @else
                    <div class="text-center py-3">
                        <i class="mdi mdi-account-outline h2 text-muted"></i>
                        <p class="text-muted">No users assigned to this role</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Role Statistics -->
        <div class="card mt-3">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="mdi mdi-chart-pie"></i> Statistics
                </h5>

                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary">{{ $role->permissions->count() }}</h4>
                            <p class="text-muted mb-0">Permissions</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ $role->activeUsers->count() }}</h4>
                        <p class="text-muted mb-0">Active Users</p>
                    </div>
                </div>

                <hr>

                <div class="row text-center">
                    <div class="col-12">
                        <h6 class="text-muted">Permission Coverage</h6>
                        @php
                            $totalPermissions = \App\Models\Permission::active()->count();
                            $coverage = $totalPermissions > 0 ? round(($role->permissions->count() / $totalPermissions) * 100) : 0;
                        @endphp
                        <div class="progress mb-2">
                            <div class="progress-bar" role="progressbar" style="width: {{ $coverage }}%">
                                {{ $coverage }}%
                            </div>
                        </div>
                        <small class="text-muted">{{ $role->permissions->count() }} of {{ $totalPermissions }} permissions</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
