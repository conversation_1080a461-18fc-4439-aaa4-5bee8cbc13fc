<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\AuditableTrait;
use App\Contracts\AuditableInterface;

class Chapter extends Model implements AuditableInterface
{
    use HasFactory, AuditableTrait;

    protected $fillable = [
        'name',
        'description',
        'isactive',
        'created_by',
        'updated_by'
    ];
}
