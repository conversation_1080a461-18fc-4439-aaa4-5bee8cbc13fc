# 🚀 Al-Quran API Quick Reference

## 📋 Endpoint Summary

### 🕌 Quran Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/quran` | Get all Quran data |
| `GET` | `/api/quran/surah/{number}` | Get specific surah |
| `GET` | `/api/quran/surah/{surah}/ayah/{ayah}` | Get specific ayah |
| `GET` | `/api/quran/search?q={query}` | Search in Quran |
| `GET` | `/api/quran/ayah/{surah}/{ayah}/{language?}` | Get ayah with translation |
| `GET` | `/api/quran/translation/{surah}/{ayah}/{language}` | Get specific translation |
| `GET` | `/api/quran/translations/{surah}/{ayah}` | Get all translations |
| `GET` | `/api/quran/tafsir/{surah}/{ayah}` | Get tafsir |
| `GET` | `/api/quran/tafsir/{surah}/{ayah}/{language?}` | Get tafsir with translation |
| `GET` | `/api/quran/languages` | Get available languages |

### 📚 Topics Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/topics` | Get all active topics |
| `GET` | `/api/topics/search?q={query}` | Search topics |

### 📖 Chapters Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/chapters` | Get all active chapters |
| `GET` | `/api/chapters/search?q={query}` | Search chapters |

---

## 🌍 Language Codes

| Code | Language |
|------|----------|
| `bn` | Bengali |
| `zh` | Chinese |
| `en` | English |
| `es` | Spanish |
| `fr` | French |
| `id` | Indonesian |
| `ru` | Russian |
| `sv` | Swedish |
| `tr` | Turkish |
| `ur` | Urdu |

---

## 🔥 Common Use Cases

### Get First Ayah of Al-Fatiha
```
GET /api/quran/surah/1/ayah/1
```

### Search for "Allah"
```
GET /api/quran/search?q=Allah
```

### Get Ayah in Urdu
```
GET /api/quran/translation/1/1/ur
```

### Get All Translations
```
GET /api/quran/translations/1/1
```

### Search Topics
```
GET /api/topics/search?q=God
```

---

## ⚡ Quick Examples

### JavaScript
```javascript
// Get surah
fetch('/api/quran/surah/1')
  .then(r => r.json())
  .then(data => console.log(data));

// Search
fetch('/api/quran/search?q=Allah')
  .then(r => r.json())
  .then(results => console.log(results));
```

### cURL
```bash
# Get ayah
curl "http://domain.com/api/quran/surah/1/ayah/1"

# Search
curl "http://domain.com/api/quran/search?q=Allah"
```

### Python
```python
import requests

# Get ayah
response = requests.get('/api/quran/surah/1/ayah/1')
ayah = response.json()

# Search
response = requests.get('/api/quran/search?q=Allah')
results = response.json()
```

---

## 📊 Response Formats

### Single Ayah
```json
{
  "id": 1,
  "surah_number": 1,
  "ayah_number": 1,
  "ayah_text": "بِسۡمِ ٱللَّهِ...",
  "translation_en": "In the name of Allah...",
  "translation_ur": "اللہ کے نام سے..."
}
```

### Search Results
```json
[
  {
    "id": 1,
    "surah_number": 1,
    "ayah_number": 1,
    "ayah_text": "...",
    "translation_en": "..."
  }
]
```

### All Translations
```json
{
  "surah_number": 1,
  "ayah_number": 1,
  "translations": {
    "arabic": "بِسۡمِ ٱللَّهِ...",
    "en": "In the name of Allah...",
    "ur": "اللہ کے نام سے..."
  }
}
```

---

## ❌ Error Codes

| Code | Meaning |
|------|---------|
| `200` | Success |
| `400` | Bad Request |
| `404` | Not Found |
| `500` | Server Error |

---

## 💡 Tips

1. **Cache responses** for better performance
2. **Use specific endpoints** instead of fetching all data
3. **Handle 404 errors** for missing tafsir
4. **Limit search results** client-side if needed
5. **Use language codes** for specific translations

---

## 🔗 Base URL
```
http://your-domain.com/api
```

---

**For complete documentation, see [API_DOCUMENTATION.md](API_DOCUMENTATION.md)**
