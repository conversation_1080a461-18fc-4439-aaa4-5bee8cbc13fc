<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\ChapterController;
use App\Http\Controllers\SurahController;
use App\Http\Controllers\TopicsController;
use App\Http\Controllers\TopicDetailController;
use App\Http\Controllers\QuranDashboardController;
use App\Http\Controllers\LibraryController;
use App\Http\Controllers\BookController;
use App\Http\Controllers\AuditController;
use App\Http\Controllers\UserActivityController;
use App\Http\Controllers\BackupController;
use App\Http\Controllers\ApiDocumentationController;
use App\Http\Controllers\UserManagementController;
use App\Http\Controllers\RoleManagementController;
use App\Http\Controllers\PermissionManagementController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Redirect root to dashboard if authenticated, otherwise to login
Route::get('/', function () {
    return redirect()->route('login');
})->name('home');

// API Testing & Documentation
Route::prefix('api-docs')->name('api.')->group(function () {
    Route::get('/', [ApiDocumentationController::class, 'documentation'])->name('docs');
    Route::get('/test', [ApiDocumentationController::class, 'tester'])->name('test');
    Route::get('/quick-reference', [ApiDocumentationController::class, 'quickReference'])->name('quick.reference');
    Route::get('/postman-collection', [ApiDocumentationController::class, 'postmanCollection'])->name('postman.collection');
    Route::get('/status', [ApiDocumentationController::class, 'status'])->name('status');
    Route::get('/overview', [ApiDocumentationController::class, 'overview'])->name('overview');
});

// Admin Routes
Route::middleware(['auth'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [QuranDashboardController::class, 'index'])->name('quran.dashboard');

    // Languages
    Route::resource('langs', LanguageController::class);

    // Chapters
    Route::resource('chapts', ChapterController::class);

    // Surahs
    Route::resource('surahs', SurahController::class);

    // Topics
    Route::resource('topics', TopicsController::class);

    // Topic Details
    Route::resource('topicdetails', TopicDetailController::class);

    // Library
    Route::resource('library', LibraryController::class);

    // Books
    Route::resource('books', BookController::class);

    // Audit Trails
    Route::prefix('audits')->name('audits.')->group(function () {
        Route::get('/', [AuditController::class, 'index'])->name('index');
        Route::get('/{audit}', [AuditController::class, 'show'])->name('show');
        Route::get('/export/csv', [AuditController::class, 'export'])->name('export');
        Route::delete('/cleanup', [AuditController::class, 'cleanup'])->name('cleanup');
    });

    // User Activities
    Route::prefix('user-activities')->name('user-activities.')->group(function () {
        Route::get('/', [UserActivityController::class, 'index'])->name('index');
        Route::get('/dashboard', [UserActivityController::class, 'dashboard'])->name('dashboard');
        Route::get('/export-form', [UserActivityController::class, 'exportForm'])->name('export-form');
        Route::get('/export/csv', [UserActivityController::class, 'export'])->name('export');
        Route::post('/export', [UserActivityController::class, 'exportWithFilters'])->name('export.filtered');
        Route::get('/{activity}', [UserActivityController::class, 'show'])->name('show');
        Route::delete('/cleanup', [UserActivityController::class, 'cleanup'])->name('cleanup');
    });

    // Backup & Data Management
    Route::prefix('backups')->name('backups.')->group(function () {
        Route::get('/', [BackupController::class, 'index'])->name('index');
        Route::post('/full', [BackupController::class, 'createFullBackup'])->name('create.full');
        Route::post('/database', [BackupController::class, 'createDatabaseBackup'])->name('create.database');
        Route::post('/files', [BackupController::class, 'createFilesBackup'])->name('create.files');
        Route::post('/export', [BackupController::class, 'createDataExport'])->name('create.export');
        Route::get('/download', [BackupController::class, 'download'])->name('download');
        Route::delete('/delete', [BackupController::class, 'delete'])->name('delete');
        Route::delete('/cleanup', [BackupController::class, 'cleanup'])->name('cleanup');
        Route::get('/restore', [BackupController::class, 'restoreForm'])->name('restore.form');
        Route::post('/restore', [BackupController::class, 'restore'])->name('restore');
        Route::get('/backup-info', [BackupController::class, 'getBackupInfo'])->name('backup.info');
    });

    // API Testing & Documentation
    Route::prefix('api-docs')->name('api.')->group(function () {
        Route::get('/', [ApiDocumentationController::class, 'documentation'])->name('docs');
        Route::get('/test', [ApiDocumentationController::class, 'tester'])->name('test');
        Route::get('/quick-reference', [ApiDocumentationController::class, 'quickReference'])->name('quick.reference');
        Route::get('/postman-collection', [ApiDocumentationController::class, 'postmanCollection'])->name('postman.collection');
        Route::get('/status', [ApiDocumentationController::class, 'status'])->name('status');
        Route::get('/overview', [ApiDocumentationController::class, 'overview'])->name('overview');
    });

    // User Management
    Route::prefix('users')->name('users.')->middleware('permission:users.manage')->group(function () {
        Route::get('/', [UserManagementController::class, 'index'])->name('index');
        Route::get('/create', [UserManagementController::class, 'create'])->name('create');
        Route::post('/', [UserManagementController::class, 'store'])->name('store');
        Route::get('/{user}', [UserManagementController::class, 'show'])->name('show');
        Route::get('/{user}/edit', [UserManagementController::class, 'edit'])->name('edit');
        Route::put('/{user}', [UserManagementController::class, 'update'])->name('update');
        Route::delete('/{user}', [UserManagementController::class, 'destroy'])->name('destroy');
        Route::post('/{user}/toggle-status', [UserManagementController::class, 'toggleStatus'])->name('toggle-status');
    });

    // Role Management
    Route::prefix('roles')->name('roles.')->middleware('permission:roles.manage')->group(function () {
        Route::get('/', [RoleManagementController::class, 'index'])->name('index');
        Route::get('/create', [RoleManagementController::class, 'create'])->name('create');
        Route::post('/', [RoleManagementController::class, 'store'])->name('store');
        Route::get('/{role}', [RoleManagementController::class, 'show'])->name('show');
        Route::get('/{role}/edit', [RoleManagementController::class, 'edit'])->name('edit');
        Route::put('/{role}', [RoleManagementController::class, 'update'])->name('update');
        Route::delete('/{role}', [RoleManagementController::class, 'destroy'])->name('destroy');
        Route::post('/{role}/toggle-status', [RoleManagementController::class, 'toggleStatus'])->name('toggle-status');
        Route::get('/{role}/clone', [RoleManagementController::class, 'clone'])->name('clone');
        Route::post('/{role}/clone', [RoleManagementController::class, 'storeClone'])->name('store-clone');
    });

    // Permission Management
    Route::prefix('permissions')->name('permissions.')->middleware('permission:permissions.manage')->group(function () {
        Route::get('/', [PermissionManagementController::class, 'index'])->name('index');
        Route::get('/create', [PermissionManagementController::class, 'create'])->name('create');
        Route::post('/', [PermissionManagementController::class, 'store'])->name('store');
        Route::get('/bulk-create', [PermissionManagementController::class, 'bulkCreate'])->name('bulk-create');
        Route::post('/bulk-create', [PermissionManagementController::class, 'storeBulk'])->name('store-bulk');
        Route::get('/{permission}', [PermissionManagementController::class, 'show'])->name('show');
        Route::get('/{permission}/edit', [PermissionManagementController::class, 'edit'])->name('edit');
        Route::put('/{permission}', [PermissionManagementController::class, 'update'])->name('update');
        Route::delete('/{permission}', [PermissionManagementController::class, 'destroy'])->name('destroy');
        Route::post('/{permission}/toggle-status', [PermissionManagementController::class, 'toggleStatus'])->name('toggle-status');
    });
});

require __DIR__ . '/auth.php';

// <!-- API Documentation & Testing -->
// {{-- <li class="has_sub">
//     <a href="javascript:void(0);" class="waves-effect">
//         <i class="mdi mdi-api"></i>
//         <span>API & Documentation</span>
//         <span class="menu-arrow"></span>
//     </a>
//     <ul class="nav-second-level" aria-expanded="false">
//         <li><a href="{{ route('api.docs') }}">API Documentation</a></li>
//         <li><a href="{{ route('api.test') }}" target="_blank">API Tester</a></li>
//         <li><a href="{{ route('api.quick.reference') }}">Quick Reference</a></li>
//         <li><a href="{{ route('api.postman.collection') }}">Postman Collection</a></li>
//         <li><a href="{{ route('api.status') }}" target="_blank">API Status</a></li>
//         <li><a href="{{ route('api.overview') }}" target="_blank">API Overview</a></li>
//     </ul>
// </li> --}}
