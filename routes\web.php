<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\ChapterController;
use App\Http\Controllers\SurahController;
use App\Http\Controllers\TopicsController;
use App\Http\Controllers\TopicDetailController;
use App\Http\Controllers\QuranDashboardController;
use App\Http\Controllers\LibraryController;
use App\Http\Controllers\BookController;
use App\Http\Controllers\AuditController;
use App\Http\Controllers\UserActivityController;
use App\Http\Controllers\BackupController;
use App\Http\Controllers\ApiDocumentationController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Redirect root to dashboard if authenticated, otherwise to login
Route::get('/', function () {
    return redirect()->route('login');
})->name('home');







// Admin Routes
Route::middleware(['auth'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [QuranDashboardController::class, 'index'])->name('quran.dashboard');

    // Languages
    Route::resource('langs', LanguageController::class);

    // Chapters
    Route::resource('chapts', ChapterController::class);

    // Surahs
    Route::resource('surahs', SurahController::class);

    // Topics
    Route::resource('topics', TopicsController::class);

    // Topic Details
    Route::resource('topicdetails', TopicDetailController::class);

    // Library
    Route::resource('library', LibraryController::class);

    // Books
    Route::resource('books', BookController::class);

    // Audit Trails
    Route::prefix('audits')->name('audits.')->group(function () {
        Route::get('/', [AuditController::class, 'index'])->name('index');
        Route::get('/{audit}', [AuditController::class, 'show'])->name('show');
        Route::get('/export/csv', [AuditController::class, 'export'])->name('export');
        Route::delete('/cleanup', [AuditController::class, 'cleanup'])->name('cleanup');
    });

    // User Activities
    Route::prefix('user-activities')->name('user-activities.')->group(function () {
        Route::get('/', [UserActivityController::class, 'index'])->name('index');
        Route::get('/dashboard', [UserActivityController::class, 'dashboard'])->name('dashboard');
        Route::get('/export-form', [UserActivityController::class, 'exportForm'])->name('export-form');
        Route::get('/export/csv', [UserActivityController::class, 'export'])->name('export');
        Route::post('/export', [UserActivityController::class, 'exportWithFilters'])->name('export.filtered');
        Route::get('/{activity}', [UserActivityController::class, 'show'])->name('show');
        Route::delete('/cleanup', [UserActivityController::class, 'cleanup'])->name('cleanup');
    });

    // Backup & Data Management
    Route::prefix('backups')->name('backups.')->group(function () {
        Route::get('/', [BackupController::class, 'index'])->name('index');
        Route::post('/full', [BackupController::class, 'createFullBackup'])->name('create.full');
        Route::post('/database', [BackupController::class, 'createDatabaseBackup'])->name('create.database');
        Route::post('/files', [BackupController::class, 'createFilesBackup'])->name('create.files');
        Route::post('/export', [BackupController::class, 'createDataExport'])->name('create.export');
        Route::get('/download', [BackupController::class, 'download'])->name('download');
        Route::delete('/delete', [BackupController::class, 'delete'])->name('delete');
        Route::delete('/cleanup', [BackupController::class, 'cleanup'])->name('cleanup');
        Route::get('/restore', [BackupController::class, 'restoreForm'])->name('restore.form');
        Route::post('/restore', [BackupController::class, 'restore'])->name('restore');
        Route::get('/backup-info', [BackupController::class, 'getBackupInfo'])->name('backup.info');
    });
});


require __DIR__ . '/auth.php';


// <!-- API Documentation & Testing -->
// {{-- <li class="has_sub">
//     <a href="javascript:void(0);" class="waves-effect">
//         <i class="mdi mdi-api"></i>
//         <span>API & Documentation</span>
//         <span class="menu-arrow"></span>
//     </a>
//     <ul class="nav-second-level" aria-expanded="false">
//         <li><a href="{{ route('api.docs') }}">API Documentation</a></li>
//         <li><a href="{{ route('api.test') }}" target="_blank">API Tester</a></li>
//         <li><a href="{{ route('api.quick.reference') }}">Quick Reference</a></li>
//         <li><a href="{{ route('api.postman.collection') }}">Postman Collection</a></li>
//         <li><a href="{{ route('api.status') }}" target="_blank">API Status</a></li>
//         <li><a href="{{ route('api.overview') }}" target="_blank">API Overview</a></li>
//     </ul>
// </li> --}}
