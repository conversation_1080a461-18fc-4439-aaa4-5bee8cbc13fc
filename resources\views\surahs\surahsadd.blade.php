@extends('layouts.master')

@section('content')
    <!-- Row -->

<div class="row">
    <div class="col-lg-6">

        <div class="card-box">
            <h4 class="header-title">Add New Surah</h4>
            <p class="sub-header">
                Define Surah options in the system.
            </p>

            <form method="POST" class="parsley-examples" action="{{route('surahs.store')}}">
                @csrf
                <div class="form-group">
                    <label for="surahname">Surah Name:<span class="text-danger">*</span></label>
                    <input type="text" name="surahname" parsley-trigger="change" required
                           placeholder="Enter surah name" class="form-control" id="surahname">
                </div>
                <div class="form-group">
                    <label for="surahcode">Surah Code<span class="text-danger">*</span></label>
                    <input type="text" name="surahcode" parsley-trigger="change" required
                           placeholder="Enter surah code" class="form-control" id="surahcode">
                </div>
                <div class="form-group">
                    <label for="surahcode">Chapters <span class="text-danger">*</span></label>
                    <select class="select2 form-control select2" name="chapter_id" data-placeholder="">
                        @foreach($chapts as $chapt)
                            <option value="{{$chapt->id}}">{{$chapt->chaptername}}</option>
                        @endforeach
                    </select>
                </div>
                <div class="form-group">
                    <div class="checkbox checkbox-purple">
                        <input id="isactive" name="isactive" type="checkbox" value="1">
                        <label for="isactive">
                            Is Active
                        </label>
                    </div>

                </div>

                <div class="form-group text-right mb-0">
                    <button class="btn btn-primary waves-effect waves-light" type="submit"><i class="fas fa-save mr-1"></i>
                        Save Lanaguage
                    </button>
                    <button type="reset" onclick="window.history.go(-1);" class="btn btn-light waves-effect ml-1">
                        Cancel
                    </button>
                </div>

            </form>
        </div> <!-- end card-box -->
    </div>
</div>
@endsection
