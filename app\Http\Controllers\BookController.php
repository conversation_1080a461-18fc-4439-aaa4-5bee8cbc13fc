<?php

namespace App\Http\Controllers;

use App\Models\Book;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class BookController extends Controller
{
    public function index()
    {
        $books = Book::all();
        return view('books.index', compact('books'));
    }

    public function create()
    {
        return view('books.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'author' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'pdf_file' => 'required|mimes:pdf|max:10240', // 10MB max
        ]);

        $isactive = $request->has('isactive') ? 1 : 0;
        
        if ($request->hasFile('pdf_file')) {
            $file = $request->file('pdf_file');
            $filename = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('books', $filename, 'public');
            
            Book::create([
                'title' => $request->title,
                'author' => $request->author,
                'description' => $request->description,
                'file_path' => $filePath,
                'isactive' => $isactive,
                'created_by' => Auth::user()->name ?? "Admin",
            ]);
            
            return redirect()->route('books.index')->with('success', 'Book added successfully!');
        }
        
        return back()->with('error', 'Failed to upload file.');
    }

    public function show(Book $book)
    {
        return view('books.show', compact('book'));
    }

    public function edit(Book $book)
    {
        return view('books.edit', compact('book'));
    }

    public function update(Request $request, Book $book)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'author' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'pdf_file' => 'nullable|mimes:pdf|max:10240', // 10MB max
        ]);

        $isactive = $request->has('isactive') ? 1 : 0;
        $data = [
            'title' => $request->title,
            'author' => $request->author,
            'description' => $request->description,
            'isactive' => $isactive,
            'updated_by' => Auth::user()->name ?? "Admin",
        ];
        
        if ($request->hasFile('pdf_file')) {
            // Delete old file if exists
            if ($book->file_path && Storage::disk('public')->exists($book->file_path)) {
                Storage::disk('public')->delete($book->file_path);
            }
            
            $file = $request->file('pdf_file');
            $filename = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('books', $filename, 'public');
            $data['file_path'] = $filePath;
        }
        
        $book->update($data);
        return redirect()->route('books.index')->with('success', 'Book updated successfully!');
    }

    public function destroy(Book $book)
    {
        if ($book->file_path && Storage::disk('public')->exists($book->file_path)) {
            Storage::disk('public')->delete($book->file_path);
        }
        
        $book->delete();
        return redirect()->route('books.index')->with('success', 'Book deleted successfully!');
    }
}