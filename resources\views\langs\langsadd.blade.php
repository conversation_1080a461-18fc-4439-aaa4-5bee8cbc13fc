@extends('layouts.master')

@section('content')
    <!-- Row -->

<div class="row">
    <div class="col-lg-6">

        <div class="card-box">
            <h4 class="header-title">Add New Language</h4>
            <p class="sub-header">
                Define languages options in the system.
            </p>

            <form method="POST" class="parsley-examples" action="{{route('langs.store')}}">
                @csrf
                <div class="form-group">
                    <label for="langname">Language Name:<span class="text-danger">*</span></label>
                    <input type="text" name="langname" parsley-trigger="change" required
                           placeholder="Enter language" class="form-control" id="langname">
                </div>
                <div class="form-group">
                    <label for="langcode">Language Code<span class="text-danger">*</span></label>
                    <input type="text" name="langcode" parsley-trigger="change" required
                           placeholder="Enter langcode" class="form-control" id="langcode">
                </div>
                <div class="form-group">
                    <div class="checkbox checkbox-purple">
                        <input id="isactive" name="isactive" type="checkbox" value="1">
                        <label for="isactive">
                            Is Active
                        </label>
                    </div>

                </div>

                <div class="form-group text-right mb-0">
                    <button class="btn btn-primary waves-effect waves-light" type="submit"><i class="fas fa-save mr-1"></i>
                        Save Lanaguage
                    </button>
                    <button type="reset" onclick="window.history.go(-1);" class="btn btn-light waves-effect ml-1">
                        Cancel
                    </button>
                </div>

            </form>
        </div> <!-- end card-box -->
    </div>
</div>
@endsection
