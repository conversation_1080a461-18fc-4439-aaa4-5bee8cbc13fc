<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Database\Seeders\LibraryItemSeeder;

class GenerateLibraryItems extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'library:generate {count=50 : Number of items to generate}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate sample library items for testing';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $count = (int) $this->argument('count');
        
        if ($count <= 0) {
            $this->error('Count must be a positive number');
            return 1;
        }
        
        $this->info("Generating {$count} sample library items...");
        
        // Run the seeder
        $seeder = new LibraryItemSeeder();
        $seeder->setCommand($this);
        $seeder->run();
        
        $this->info('Sample library items generated successfully!');
        
        return 0;
    }
}