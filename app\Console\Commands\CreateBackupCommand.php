<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\BackupService;
use App\Services\ActivityLogger;

class CreateBackupCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup:create 
                            {type=full : Type of backup (full, database, files, config)}
                            {--cleanup=30 : Days to keep old backups}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create system backups and optionally cleanup old ones';

    protected $backupService;

    /**
     * Create a new command instance.
     */
    public function __construct(BackupService $backupService)
    {
        parent::__construct();
        $this->backupService = $backupService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->argument('type');
        $cleanup = $this->option('cleanup');

        $this->info("Starting {$type} backup...");

        try {
            switch ($type) {
                case 'full':
                    $result = $this->backupService->createFullBackup();
                    if ($result['success']) {
                        $this->info("Full backup created successfully: {$result['backup_name']}");
                        $this->info("File size: " . number_format($result['file_size'] / 1024 / 1024, 2) . " MB");
                    } else {
                        $this->error("Full backup failed: {$result['error']}");
                        return 1;
                    }
                    break;

                case 'database':
                    $filePath = $this->backupService->createDatabaseBackup();
                    $this->info("Database backup created: " . basename($filePath));
                    break;

                case 'files':
                    $filePath = $this->backupService->createFilesBackup();
                    $this->info("Files backup created: " . basename($filePath));
                    break;

                case 'config':
                    $filePath = $this->backupService->createConfigBackup();
                    $this->info("Config backup created: " . basename($filePath));
                    break;

                default:
                    $this->error("Invalid backup type: {$type}");
                    $this->info("Valid types: full, database, files, config");
                    return 1;
            }

            // Cleanup old backups if requested
            if ($cleanup > 0) {
                $this->info("Cleaning up backups older than {$cleanup} days...");
                $deletedCount = $this->backupService->cleanupOldBackups($cleanup);
                $this->info("Deleted {$deletedCount} old backup files.");
            }

            $this->info("Backup process completed successfully!");
            return 0;

        } catch (\Exception $e) {
            $this->error("Backup failed: " . $e->getMessage());
            return 1;
        }
    }
}
