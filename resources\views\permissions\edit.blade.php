@extends('layouts.master')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('quran.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('permissions.index') }}">Permissions</a></li>
                    <li class="breadcrumb-item active">Edit</li>
                </ol>
            </div>
            <h4 class="page-title">Edit Permission</h4>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title">
                    <i class="mdi mdi-key-variant text-primary"></i> Edit Permission: {{ $permission->display_name }}
                </h4>

                @if($permission->is_system)
                    <div class="alert alert-warning">
                        <i class="mdi mdi-alert"></i>
                        <strong>System Permission:</strong> Some fields are restricted for system permissions.
                    </div>
                @endif

                <form method="POST" action="{{ route('permissions.update', $permission) }}">
                    @csrf
                    @method('PUT')

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="display_name" class="form-label">Display Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('display_name') is-invalid @enderror" 
                                       id="display_name" name="display_name" value="{{ old('display_name', $permission->display_name) }}" required>
                                @error('display_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="module" class="form-label">Module <span class="text-danger">*</span></label>
                                <select class="form-control @error('module') is-invalid @enderror" id="module" name="module" 
                                        {{ $permission->is_system ? 'disabled' : '' }} required>
                                    <option value="">Select Module</option>
                                    @foreach($modules as $key => $value)
                                        <option value="{{ $key }}" {{ old('module', $permission->module) == $key ? 'selected' : '' }}>
                                            {{ $value }}
                                        </option>
                                    @endforeach
                                </select>
                                @if($permission->is_system)
                                    <input type="hidden" name="module" value="{{ $permission->module }}">
                                @endif
                                @error('module')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="action" class="form-label">Action <span class="text-danger">*</span></label>
                                <select class="form-control @error('action') is-invalid @enderror" id="action" name="action" 
                                        {{ $permission->is_system ? 'disabled' : '' }} required>
                                    <option value="">Select Action</option>
                                    @foreach($actions as $key => $value)
                                        <option value="{{ $key }}" {{ old('action', $permission->action) == $key ? 'selected' : '' }}>
                                            {{ $value }}
                                        </option>
                                    @endforeach
                                </select>
                                @if($permission->is_system)
                                    <input type="hidden" name="action" value="{{ $permission->action }}">
                                @endif
                                @error('action')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="resource" class="form-label">Resource (Optional)</label>
                                <input type="text" class="form-control @error('resource') is-invalid @enderror" 
                                       id="resource" name="resource" value="{{ old('resource', $permission->resource) }}"
                                       {{ $permission->is_system ? 'readonly' : '' }}
                                       placeholder="e.g., own, all, specific">
                                @error('resource')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Specify resource scope if needed</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="3">{{ old('description', $permission->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">Sort Order</label>
                                <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                       id="sort_order" name="sort_order" value="{{ old('sort_order', $permission->sort_order) }}" min="0">
                                @error('sort_order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1" 
                                           {{ old('is_active', $permission->is_active) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        Active
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Current Permission Name -->
                    <div class="mb-3">
                        <label class="form-label">Current Permission Name</label>
                        <div class="alert alert-secondary">
                            <code>{{ $permission->name }}</code>
                        </div>
                    </div>

                    @if(!$permission->is_system)
                        <!-- Preview for custom permissions -->
                        <div class="mb-3">
                            <label class="form-label">New Permission Name Preview</label>
                            <div class="alert alert-info">
                                <code id="permission-preview">{{ $permission->name }}</code>
                            </div>
                        </div>
                    @endif

                    <div class="text-end">
                        <a href="{{ route('permissions.index') }}" class="btn btn-secondary me-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">Update Permission</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    @if(!$permission->is_system)
        const moduleSelect = document.getElementById('module');
        const actionSelect = document.getElementById('action');
        const resourceInput = document.getElementById('resource');
        const preview = document.getElementById('permission-preview');

        function updatePreview() {
            const module = moduleSelect.value;
            const action = actionSelect.value;
            const resource = resourceInput.value;

            let permissionName = '';
            if (module && action) {
                permissionName = module + '.' + action;
                if (resource) {
                    permissionName += '.' + resource;
                }
            } else {
                permissionName = '{{ $permission->name }}';
            }

            preview.textContent = permissionName;
        }

        moduleSelect.addEventListener('change', updatePreview);
        actionSelect.addEventListener('change', updatePreview);
        resourceInput.addEventListener('input', updatePreview);

        // Initial preview update
        updatePreview();
    @endif
});
</script>
@endsection
