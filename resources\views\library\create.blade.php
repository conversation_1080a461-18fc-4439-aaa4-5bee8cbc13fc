@extends('layouts.master')

@section('content')
    <div class="row">
        <div class="col-lg-8">
            <div class="card-box">
                <h4 class="header-title">Add New Library Item</h4>
                <p class="sub-header">Upload books, videos, audio files, and other resources to the library.</p>

                <form method="POST" class="parsley-examples" action="{{ route('library.store') }}"
                    enctype="multipart/form-data">
                    @csrf
                    <div class="form-group">
                        <label for="title">Title<span class="text-danger">*</span></label>
                        <input type="text" name="title" parsley-trigger="change" required placeholder="Enter title"
                            class="form-control" id="title">
                    </div>

                    <div class="form-group">
                        <label for="author">Author/Creator</label>
                        <input type="text" name="author" parsley-trigger="change"
                            placeholder="Enter author/creator name" class="form-control" id="author">
                    </div>

                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea name="description" class="form-control" id="description" rows="4" placeholder="Enter description"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="media_type">Media Type<span class="text-danger">*</span></label>
                        <select name="media_type" id="media_type" class="form-control" required>
                            <option value="pdf">PDF Book</option>
                            <option value="video">Video</option>
                            <option value="audio">Audio</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="thumbnail">Thumbnail Image</label>
                        <input type="file" name="thumbnail" class="form-control filestyle" id="thumbnail"
                            data-btnClass="btn-primary" accept="image/*">
                        <small class="text-muted">Recommended size: 400x300px. Max file size: 2MB</small>
                    </div>

                    <div class="form-group file-upload-group">
                        <label for="file">File Upload</label>
                        <input type="file" name="file" class="form-control filestyle" id="file"
                            data-btnClass="btn-primary">
                        <small class="text-muted">Max file size: 20MB</small>
                    </div>

                    <div class="form-group external-url-group" style="display: none;">
                        <label for="external_url">External URL</label>
                        <input type="url" name="external_url" class="form-control" id="external_url"
                            placeholder="https://example.com/video.mp4 or YouTube/Vimeo URL">
                        <small class="text-muted">For videos or audio hosted elsewhere (YouTube, Vimeo, etc.)</small>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="duration">Duration</label>
                                <input type="text" name="duration" class="form-control" id="duration"
                                    placeholder="e.g. 1:30:45">
                                <small class="text-muted">For videos and audio files</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="language">Language</label>
                                <input type="text" name="language" class="form-control" id="language"
                                    placeholder="e.g. English, Arabic">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="category">Category</label>
                        <input type="text" name="category" class="form-control" id="category"
                            placeholder="e.g. Tafsir, History, Fiqh">
                    </div>

                    <div class="form-group">
                        <label for="keywords">Search Keywords</label>
                        <input type="text" name="keywords" class="form-control" id="keywords"
                            placeholder="e.g. quran, hadith, prayer, fasting">
                        <small class="text-muted">Comma-separated keywords to improve search results</small>
                    </div>

                    <div class="form-group">
                        <div class="checkbox checkbox-purple">
                            <input id="isactive" name="isactive" type="checkbox" value="1" checked>
                            <label for="isactive">
                                Is Active
                            </label>
                        </div>
                    </div>

                    <div class="form-group text-right mb-0">
                        <button class="btn btn-primary waves-effect waves-light" type="submit">
                            <i class="fas fa-save mr-1"></i> Save Item
                        </button>
                        <button type="reset" onclick="window.history.go(-1);" class="btn btn-light waves-effect ml-1">
                            Cancel
                        </button>
                    </div>
                </form>
            </div> <!-- end card-box -->
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Show/hide fields based on media type
            $('#media_type').change(function() {
                const mediaType = $(this).val();

                // Reset all fields
                $('.file-upload-group, .external-url-group').hide();

                // Show appropriate fields based on media type
                if (mediaType === 'pdf' || mediaType === 'other') {
                    $('.file-upload-group').show();
                    $('.external-url-group').hide();
                } else if (mediaType === 'video' || mediaType === 'audio') {
                    $('.file-upload-group').show();
                    $('.external-url-group').show();
                }
            });

            // Trigger change on page load
            $('#media_type').trigger('change');
        });
    </script>
@endsection
