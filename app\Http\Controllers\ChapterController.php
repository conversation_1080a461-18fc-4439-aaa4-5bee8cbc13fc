<?php

namespace App\Http\Controllers;

use App\Models\Chapter;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ChapterController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
        $chapts = Chapter::all();
        return view('chapters.chapterslst', compact('chapts'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
        return view('chapters.chaptersadd');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
        $isactive = 0;
        if ($request->isactive == 1)
            $isactive = 1;

        $chap = new Chapter();
        $chap->chaptername = $request->chaptername;
        $chap->isactive = $isactive;
        $chap->created_by = Auth::user()->name ?? "Shahid";
        $chap->created_at = Now();
        $chap->save();

        return redirect()->route('chapts.index')->with('success', 'Chapter added successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Chapter $chapter)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Chapter $chapt)
    {
        //
        return view('chapters.chaptersedit', compact('chapt'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Chapter $chapt)
    {
        //
        $isactive = 0;
        if ($request->isactive == 1)
            $isactive = 1;

        $chapt = Chapter::find($chapt->id);
        $chapt->chaptername = $request->chaptname;
        $chapt->isactive = $isactive;
        $chapt->save();

        return redirect()->route('chapts.index')->with('success', 'Record updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Chapter $chapt)
    {
        //
        $chapt->delete();
        return redirect()->route('chapts.index')->with('success', 'Chapter deleted successfully!');
    }

    /**
     * API method to get all chapters
     */
    public function apiIndex()
    {
        $chapters = \App\Models\Chapter::where('isactive', 1)->get();
        return response()->json($chapters);
    }

    /**
     * API method to search chapters
     */
    public function apiSearch(Request $request)
    {
        $query = $request->input('q');

        if (!$query) {
            return response()->json([]);
        }

        $chapters = \App\Models\Chapter::where('isactive', 1)
            ->where('chaptername', 'LIKE', "%{$query}%")
            ->get();

        return response()->json($chapters);
    }
}
