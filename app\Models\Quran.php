<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Quran extends Model
{
    use HasFactory;
    protected $table = 'quran';
    protected $fillable = [
        'surah_number',
        'surah_name',
        'ayah_number',
        'ayah_text',
        'translation_bn',
        'translation_zh',
        'translation_en',
        'translation_es',
        'translation_fr',
        'translation_id',
        'translation_ru',
        'translation_sv',
        'translation_tr',
        'translation_ur',
        'tafsir'
    ];
}
