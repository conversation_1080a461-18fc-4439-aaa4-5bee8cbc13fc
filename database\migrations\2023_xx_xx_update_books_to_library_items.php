<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        // Rename books table to library_items
        Schema::rename('books', 'library_items');

        // Add new columns to the library_items table
        Schema::table('library_items', function (Blueprint $table) {
            $table->string('media_type')->default('pdf'); // pdf, video, audio, other
            $table->string('thumbnail_path')->nullable(); // path to thumbnail image
            $table->string('external_url')->nullable(); // for embedded videos/audios
            $table->string('duration')->nullable(); // for videos and audios
            $table->string('file_size')->nullable(); // file size information
            $table->string('language')->nullable(); // language of the content
            $table->string('category')->nullable(); // category of the content

            // Make file_path nullable since some items might use external_url instead
            $table->string('file_path')->nullable()->change();
        });
    }

    public function down()
    {
        // Remove added columns
        Schema::table('library_items', function (Blueprint $table) {
            $table->dropColumn([
                'media_type',
                'thumbnail_path',
                'external_url',
                'duration',
                'file_size',
                'language',
                'category'
            ]);

            // Make file_path required again
            $table->string('file_path')->nullable(false)->change();
        });

        // Rename library_items table back to books
        Schema::rename('library_items', 'books');
    }
};
