@extends('layouts.master')

@section('content')
    <!-- Row -->
    <div class="row row-sm">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <div class="card-head bottom">
                        <a href="{{ route('trans.create') }}" class="btn btn-primary float-right pull-right">Add Laguage</a>

                        <h4 class="header-title"><b>Languages List</b></h4>
                        <p class="sub-header">
                            The Buttons extension for DataTables provides a common set of options"
                        </p>


                    </div>
                    <div class="table-responsive export-table">
                        <table id="datatable-buttons" class="table table-striped table-bordered dt-responsive nowrap"
                            style="border-collapse: collapse; border-spacing: 0; width: 100%;">
                            <thead>
                                <tr>
                                    <th class="border-bottom-0">ID</th>
                                    <th class="border-bottom-0">Bank Name</th>
                                    <th class="border-bottom-0">Branch Name</th>
                                    <th class="border-bottom-0">Account No</th>
                                    <th class="border-bottom-0">Balance</th>
                                    <th class="border-bottom-0">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($trans as $tran)
                                    <tr>
                                        <td>{{ $tran->id }}</td>
                                        <td>{{ $tran->bankname }}</td>
                                        <td>{{ $tran->branchname }}</td>
                                        <td>{{ $tran->accountno }}</td>
                                        <td>{{ $tran->balance }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('bank.edit', $tran->id) }}"
                                                    class="btn btn-sm btn-purple"><i class="fe fe-edit"></i></a>
                                                <form action="{{ route('bank.destroy', $tran) }}" method="POST"
                                                    style="display: inline-block">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit"
                                                        onclick="return confirm('Are you sure to delete this record?')"
                                                        class="btn btn-danger btn-sm"><i class="fe fe-trash"></i></button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End Row -->
    <script type="text/javascript">
        $('.delid').click(function() {
            /* your code here */
            var x = confirm("Are you sure to delete this record?");
            if (x)
                return true;
            else
                return false;
        })
    </script>
@endsection



{{-- <div class="row">
    <div class="col-12">
        <div class="card-box table-responsive">
            <h4 class="header-title"><b>Buttons example</b></h4>
            <p class="sub-header">
                The Buttons extension for DataTables provides a common set of options, API methods and styling to display buttons on a page that will interact with a DataTable. The core library provides the based framework upon which plug-ins can built.
            </p>

            <table id="datatable-buttons" class="table table-striped table-bordered dt-responsive nowrap" style="border-collapse: collapse; border-spacing: 0; width: 100%;">
                <thead>
                <tr>
                    <th>Name</th>
                    <th>Position</th>
                    <th>Office</th>
                    <th>Age</th>
                    <th>Start date</th>
                    <th>Salary</th>
                </tr>
                </thead>



            </table>
        </div>
    </div>
</div> --}}
<!-- end row -->
