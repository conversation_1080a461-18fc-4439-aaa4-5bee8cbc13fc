<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('quran.dashboard')); ?>">Dashboard</a></li>
                    <li class="breadcrumb-item active">Permissions</li>
                </ol>
            </div>
            <h4 class="page-title">Permission Management</h4>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <h4 class="header-title">
                            <i class="mdi mdi-key text-primary"></i> Permissions
                        </h4>
                    </div>
                    <div class="col-md-6 text-end">
                        <?php if(auth()->user()->hasPermission('permissions.create')): ?>
                            <a href="<?php echo e(route('permissions.create')); ?>" class="btn btn-primary">
                                <i class="mdi mdi-plus"></i> Create Permission
                            </a>
                            <a href="<?php echo e(route('permissions.bulk-create')); ?>" class="btn btn-info">
                                <i class="mdi mdi-plus-box-multiple"></i> Bulk Create
                            </a>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Filters -->
                <div class="row mb-3">
                    <div class="col-12">
                        <form method="GET" action="<?php echo e(route('permissions.index')); ?>" class="row g-3">
                            <div class="col-md-3">
                                <input type="text" name="search" class="form-control" placeholder="Search permissions..." value="<?php echo e(request('search')); ?>">
                            </div>
                            <div class="col-md-2">
                                <select name="module" class="form-control">
                                    <option value="">All Modules</option>
                                    <?php $__currentLoopData = $modules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $module): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($module); ?>" <?php echo e(request('module') == $module ? 'selected' : ''); ?>>
                                            <?php echo e(ucfirst($module)); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select name="action" class="form-control">
                                    <option value="">All Actions</option>
                                    <?php $__currentLoopData = $actions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $action): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($action); ?>" <?php echo e(request('action') == $action ? 'selected' : ''); ?>>
                                            <?php echo e(ucfirst($action)); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select name="status" class="form-control">
                                    <option value="">All Status</option>
                                    <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Active</option>
                                    <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                                    <option value="system" <?php echo e(request('status') == 'system' ? 'selected' : ''); ?>>System</option>
                                    <option value="custom" <?php echo e(request('status') == 'custom' ? 'selected' : ''); ?>>Custom</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-secondary">
                                    <i class="mdi mdi-filter"></i> Filter
                                </button>
                                <a href="<?php echo e(route('permissions.index')); ?>" class="btn btn-light">
                                    <i class="mdi mdi-refresh"></i> Reset
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Permissions Table -->
                <div class="table-responsive">
                    <table class="table table-striped table-bordered">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Display Name</th>
                                <th>Module</th>
                                <th>Action</th>
                                <th>Roles</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $permissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <code><?php echo e($permission->name); ?></code>
                                        <?php if($permission->is_system): ?>
                                            <span class="badge badge-danger ms-1">System</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e($permission->display_name); ?></td>
                                    <td><?php echo $permission->module_badge; ?></td>
                                    <td><?php echo $permission->action_badge; ?></td>
                                    <td>
                                        <?php $__currentLoopData = $permission->roles->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <span class="badge badge-secondary me-1"><?php echo e($role->display_name); ?></span>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php if($permission->roles->count() > 3): ?>
                                            <span class="text-muted">+<?php echo e($permission->roles->count() - 3); ?> more</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo $permission->badge; ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <?php if(auth()->user()->hasPermission('permissions.read')): ?>
                                                <a href="<?php echo e(route('permissions.show', $permission)); ?>" class="btn btn-sm btn-info">
                                                    <i class="mdi mdi-eye"></i>
                                                </a>
                                            <?php endif; ?>
                                            
                                            <?php if(auth()->user()->hasPermission('permissions.update')): ?>
                                                <a href="<?php echo e(route('permissions.edit', $permission)); ?>" class="btn btn-sm btn-warning">
                                                    <i class="mdi mdi-pencil"></i>
                                                </a>
                                            <?php endif; ?>

                                            <?php if(auth()->user()->hasPermission('permissions.update')): ?>
                                                <button type="button" class="btn btn-sm btn-<?php echo e($permission->is_active ? 'secondary' : 'success'); ?>" 
                                                        onclick="toggleStatus(<?php echo e($permission->id); ?>)">
                                                    <i class="mdi mdi-<?php echo e($permission->is_active ? 'pause' : 'play'); ?>"></i>
                                                </button>
                                            <?php endif; ?>

                                            <?php if(auth()->user()->hasPermission('permissions.delete') && !$permission->is_system): ?>
                                                <form method="POST" action="<?php echo e(route('permissions.destroy', $permission)); ?>" style="display: inline;">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="btn btn-sm btn-danger" 
                                                            onclick="return confirm('Are you sure you want to delete this permission?')">
                                                        <i class="mdi mdi-delete"></i>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <i class="mdi mdi-key-outline h1 text-muted"></i>
                                        <h4 class="text-muted">No permissions found</h4>
                                        <p class="text-muted">No permissions match your current filters.</p>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <p class="text-muted">
                            Showing <?php echo e($permissions->firstItem() ?? 0); ?> to <?php echo e($permissions->lastItem() ?? 0); ?> 
                            of <?php echo e($permissions->total()); ?> permissions
                        </p>
                    </div>
                    <div>
                        <?php echo e($permissions->appends(request()->query())->links()); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
function toggleStatus(permissionId) {
    if (confirm('Are you sure you want to change the status of this permission?')) {
        fetch(`/permissions/${permissionId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.error || 'An error occurred');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the permission status');
        });
    }
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\alquran\resources\views/permissions/index.blade.php ENDPATH**/ ?>