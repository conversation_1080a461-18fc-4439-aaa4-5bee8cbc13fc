<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('quran.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item active">Permissions</li>
                    </ol>
                </div>
                <h4 class="page-title">Permission Management</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h4 class="header-title">
                                <i class="mdi mdi-key text-primary"></i> Permissions
                            </h4>
                        </div>
                        <div class="col-md-6 text-end">
                            <?php if(auth()->user()->hasPermission('permissions.create')): ?>
                                <a href="<?php echo e(route('permissions.create')); ?>" class="btn btn-primary">
                                    <i class="mdi mdi-plus"></i> Create Permission
                                </a>
                                <a href="<?php echo e(route('permissions.bulk-create')); ?>" class="btn btn-info">
                                    <i class="mdi mdi-plus-box-multiple"></i> Bulk Create
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- DataTable Filters -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <select id="moduleFilter" class="form-control">
                                <option value="">All Modules</option>
                                <?php $__currentLoopData = $modules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $module): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($module); ?>"><?php echo e(ucfirst($module)); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select id="actionFilter" class="form-control">
                                <option value="">All Actions</option>
                                <?php $__currentLoopData = $actions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $action): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($action); ?>"><?php echo e(ucfirst($action)); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select id="statusFilter" class="form-control">
                                <option value="">All Status</option>
                                <option value="Active">Active</option>
                                <option value="Inactive">Inactive</option>
                                <option value="System">System</option>
                                <option value="Custom">Custom</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-secondary" onclick="clearFilters()">
                                <i class="mdi mdi-refresh"></i> Clear Filters
                            </button>
                        </div>
                    </div>

                    <!-- Permissions DataTable -->
                    <div class="table-responsive">
                        <table id="permissionsTable" class="table table-striped table-bordered dt-responsive nowrap"
                            style="width:100%">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Display Name</th>
                                    <th>Module</th>
                                    <th>Action</th>
                                    <th>Roles</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $permissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <code><?php echo e($permission->name); ?></code>
                                        </td>
                                        <td><?php echo e($permission->display_name); ?></td>
                                        <td data-order="<?php echo e($permission->module); ?>">
                                            <?php echo $permission->module_badge; ?>

                                        </td>
                                        <td data-order="<?php echo e($permission->action); ?>">
                                            <?php echo $permission->action_badge; ?>

                                        </td>
                                        <td data-order="<?php echo e($permission->roles->count()); ?>">
                                            <?php $__currentLoopData = $permission->roles->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <span class="badge badge-secondary me-1"><?php echo e($role->display_name); ?></span>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php if($permission->roles->count() > 3): ?>
                                                <span class="text-muted">+<?php echo e($permission->roles->count() - 3); ?> more</span>
                                            <?php endif; ?>
                                        </td>
                                        <td data-order="<?php echo e($permission->is_system ? 'System' : 'Custom'); ?>">
                                            <?php if($permission->is_system): ?>
                                                <span class="badge badge-danger">System</span>
                                            <?php else: ?>
                                                <span class="badge badge-info">Custom</span>
                                            <?php endif; ?>
                                        </td>
                                        <td data-order="<?php echo e($permission->is_active ? 'Active' : 'Inactive'); ?>">
                                            <?php echo $permission->badge; ?>

                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <?php if(auth()->user()->hasPermission('permissions.read')): ?>
                                                    <a href="<?php echo e(route('permissions.show', $permission)); ?>"
                                                        class="btn btn-sm btn-info" title="View">
                                                        <i class="mdi mdi-eye"></i>
                                                    </a>
                                                <?php endif; ?>

                                                <?php if(auth()->user()->hasPermission('permissions.update')): ?>
                                                    <a href="<?php echo e(route('permissions.edit', $permission)); ?>"
                                                        class="btn btn-sm btn-warning" title="Edit">
                                                        <i class="mdi mdi-pencil"></i>
                                                    </a>
                                                <?php endif; ?>

                                                <?php if(auth()->user()->hasPermission('permissions.update')): ?>
                                                    <button type="button"
                                                        class="btn btn-sm btn-<?php echo e($permission->is_active ? 'secondary' : 'success'); ?>"
                                                        onclick="toggleStatus(<?php echo e($permission->id); ?>)"
                                                        title="<?php echo e($permission->is_active ? 'Deactivate' : 'Activate'); ?>">
                                                        <i
                                                            class="mdi mdi-<?php echo e($permission->is_active ? 'pause' : 'play'); ?>"></i>
                                                    </button>
                                                <?php endif; ?>

                                                <?php if(auth()->user()->hasPermission('permissions.delete') && !$permission->is_system): ?>
                                                    <button type="button" class="btn btn-sm btn-danger"
                                                        onclick="deletePermission(<?php echo e($permission->id); ?>)" title="Delete">
                                                        <i class="mdi mdi-delete"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
    <!-- DataTables CSS -->
    <link href="<?php echo e(asset('assets/libs/datatables.net-bs5/css/dataTables.bootstrap5.min.css')); ?>" rel="stylesheet"
        type="text/css" />
    <link href="<?php echo e(asset('assets/libs/datatables.net-responsive-bs5/css/responsive.bootstrap5.min.css')); ?>"
        rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/libs/datatables.net-buttons-bs5/css/buttons.bootstrap5.min.css')); ?>" rel="stylesheet"
        type="text/css" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <!-- DataTables JS -->
    <script src="<?php echo e(asset('assets/libs/datatables.net/js/jquery.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/datatables.net-bs5/js/dataTables.bootstrap5.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/datatables.net-responsive/js/dataTables.responsive.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/datatables.net-responsive-bs5/js/responsive.bootstrap5.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/datatables.net-buttons/js/dataTables.buttons.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/datatables.net-buttons-bs5/js/buttons.bootstrap5.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/datatables.net-buttons/js/buttons.html5.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/datatables.net-buttons/js/buttons.print.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/jszip/jszip.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/pdfmake/build/pdfmake.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/pdfmake/build/vfs_fonts.js')); ?>"></script>

    <script>
        $(document).ready(function() {
            // Initialize DataTable
            var table = $('#permissionsTable').DataTable({
                responsive: true,
                pageLength: 25,
                lengthMenu: [
                    [10, 25, 50, 100, -1],
                    [10, 25, 50, 100, "All"]
                ],
                order: [
                    [1, 'asc']
                ], // Sort by Display Name
                dom: 'Bfrtip',
                buttons: [{
                        extend: 'copy',
                        className: 'btn btn-secondary btn-sm'
                    },
                    {
                        extend: 'csv',
                        className: 'btn btn-secondary btn-sm'
                    },
                    {
                        extend: 'excel',
                        className: 'btn btn-secondary btn-sm'
                    },
                    {
                        extend: 'pdf',
                        className: 'btn btn-secondary btn-sm'
                    },
                    {
                        extend: 'print',
                        className: 'btn btn-secondary btn-sm'
                    }
                ],
                columnDefs: [{
                    targets: [7], // Actions column
                    orderable: false,
                    searchable: false
                }],
                language: {
                    search: "Search permissions:",
                    lengthMenu: "Show _MENU_ permissions per page",
                    info: "Showing _START_ to _END_ of _TOTAL_ permissions",
                    infoEmpty: "No permissions found",
                    infoFiltered: "(filtered from _MAX_ total permissions)",
                    emptyTable: "No permissions available",
                    zeroRecords: "No matching permissions found"
                }
            });

            // Custom filters
            $('#moduleFilter').on('change', function() {
                var value = this.value;
                table.column(2).search(value).draw();
            });

            $('#actionFilter').on('change', function() {
                var value = this.value;
                table.column(3).search(value).draw();
            });

            $('#statusFilter').on('change', function() {
                var value = this.value;
                table.column(6).search(value).draw();
            });

            // Clear filters function
            window.clearFilters = function() {
                $('#moduleFilter').val('');
                $('#actionFilter').val('');
                $('#statusFilter').val('');
                table.search('').columns().search('').draw();
            };
        });

        function toggleStatus(permissionId) {
            if (confirm('Are you sure you want to change the status of this permission?')) {
                fetch(`/permissions/${permissionId}/toggle-status`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            location.reload();
                        } else {
                            alert(data.error || 'An error occurred');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred while updating the permission status');
                    });
            }
        }

        function deletePermission(permissionId) {
            if (confirm('Are you sure you want to delete this permission? This action cannot be undone.')) {
                // Create a form and submit it
                var form = document.createElement('form');
                form.method = 'POST';
                form.action = `/permissions/${permissionId}`;

                var csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                var methodField = document.createElement('input');
                methodField.type = 'hidden';
                methodField.name = '_method';
                methodField.value = 'DELETE';

                form.appendChild(csrfToken);
                form.appendChild(methodField);
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\alquran\resources\views/permissions/index.blade.php ENDPATH**/ ?>