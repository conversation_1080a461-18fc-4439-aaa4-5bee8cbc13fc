@extends('layouts.master')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('quran.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item active">Audit Trails</li>
                </ol>
            </div>
            <h4 class="page-title">Audit Trails</h4>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row">
    <div class="col-md-3">
        <div class="card widget-flat">
            <div class="card-body">
                <div class="float-right">
                    <i class="mdi mdi-shield-check widget-icon bg-success-lighten text-success"></i>
                </div>
                <h5 class="text-muted font-weight-normal mt-0" title="Total Audits">Total Audits</h5>
                <h3 class="mt-3 mb-3">{{ number_format($stats['total']) }}</h3>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card widget-flat">
            <div class="card-body">
                <div class="float-right">
                    <i class="mdi mdi-calendar-today widget-icon bg-info-lighten text-info"></i>
                </div>
                <h5 class="text-muted font-weight-normal mt-0" title="Today">Today</h5>
                <h3 class="mt-3 mb-3">{{ number_format($stats['today']) }}</h3>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card widget-flat">
            <div class="card-body">
                <div class="float-right">
                    <i class="mdi mdi-calendar-week widget-icon bg-warning-lighten text-warning"></i>
                </div>
                <h5 class="text-muted font-weight-normal mt-0" title="This Week">This Week</h5>
                <h3 class="mt-3 mb-3">{{ number_format($stats['this_week']) }}</h3>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card widget-flat">
            <div class="card-body">
                <div class="float-right">
                    <i class="mdi mdi-calendar-month widget-icon bg-primary-lighten text-primary"></i>
                </div>
                <h5 class="text-muted font-weight-normal mt-0" title="This Month">This Month</h5>
                <h3 class="mt-3 mb-3">{{ number_format($stats['this_month']) }}</h3>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">Filter Audit Logs</h4>
                
                <form method="GET" action="{{ route('audits.index') }}" class="row">
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="user_id">User</label>
                            <select name="user_id" id="user_id" class="form-control">
                                <option value="">All Users</option>
                                @foreach($users as $user)
                                    <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                        {{ $user->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="event">Event</label>
                            <select name="event" id="event" class="form-control">
                                <option value="">All Events</option>
                                @foreach($events as $event)
                                    <option value="{{ $event }}" {{ request('event') == $event ? 'selected' : '' }}>
                                        {{ ucfirst($event) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="model_type">Model Type</label>
                            <select name="model_type" id="model_type" class="form-control">
                                <option value="">All Models</option>
                                @foreach($modelTypes as $modelType)
                                    <option value="{{ $modelType }}" {{ request('model_type') == $modelType ? 'selected' : '' }}>
                                        {{ class_basename($modelType) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_from">Date From</label>
                            <input type="date" name="date_from" id="date_from" class="form-control" value="{{ request('date_from') }}">
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_to">Date To</label>
                            <input type="date" name="date_to" id="date_to" class="form-control" value="{{ request('date_to') }}">
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="search">Search</label>
                            <input type="text" name="search" id="search" class="form-control" placeholder="URL, IP..." value="{{ request('search') }}">
                        </div>
                    </div>

                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">Filter</button>
                        <a href="{{ route('audits.index') }}" class="btn btn-secondary">Clear</a>
                        <a href="{{ route('audits.export') }}?{{ http_build_query(request()->all()) }}" class="btn btn-success">Export CSV</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Audit Logs Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title">Audit Logs</h4>
                <p class="text-muted font-13 mb-4">
                    Complete audit trail of all system changes and user actions.
                </p>

                <div class="table-responsive">
                    <table class="table table-striped table-centered mb-0">
                        <thead>
                            <tr>
                                <th>Date/Time</th>
                                <th>User</th>
                                <th>Event</th>
                                <th>Model</th>
                                <th>Changes</th>
                                <th>IP Address</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($audits as $audit)
                                <tr>
                                    <td>
                                        <small>{{ $audit->created_at->format('Y-m-d H:i:s') }}</small>
                                    </td>
                                    <td>
                                        @if($audit->user)
                                            <span class="badge badge-soft-primary">{{ $audit->user->name }}</span>
                                        @else
                                            <span class="badge badge-soft-secondary">System</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge badge-soft-{{ $audit->event == 'created' ? 'success' : ($audit->event == 'updated' ? 'warning' : 'danger') }}">
                                            {{ $audit->formatted_event }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ $audit->model_name }}</span>
                                        <small class="d-block">#{{ $audit->auditable_id }}</small>
                                    </td>
                                    <td>
                                        @if($audit->changes)
                                            <small class="text-muted">{{ count($audit->changes) }} field(s) changed</small>
                                        @else
                                            <small class="text-muted">-</small>
                                        @endif
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ $audit->ip_address }}</small>
                                    </td>
                                    <td>
                                        <a href="{{ route('audits.show', $audit) }}" class="btn btn-xs btn-outline-primary">
                                            <i class="mdi mdi-eye"></i> View
                                        </a>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="7" class="text-center">No audit logs found.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="mt-3">
                    {{ $audits->appends(request()->query())->links() }}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cleanup Modal -->
<div class="modal fade" id="cleanupModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cleanup Old Audit Logs</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form method="POST" action="{{ route('audits.cleanup') }}">
                @csrf
                @method('DELETE')
                <div class="modal-body">
                    <div class="form-group">
                        <label for="days">Delete audit logs older than (days):</label>
                        <input type="number" name="days" id="days" class="form-control" min="1" max="365" value="90" required>
                        <small class="form-text text-muted">This action cannot be undone.</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete Old Logs</button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Add cleanup button to page
    $('.page-title-right').prepend('<button type="button" class="btn btn-danger btn-sm mr-2" data-toggle="modal" data-target="#cleanupModal"><i class="mdi mdi-delete"></i> Cleanup</button>');
});
</script>
@endsection
