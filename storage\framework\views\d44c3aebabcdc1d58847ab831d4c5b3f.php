<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('quran.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('backups.index')); ?>">Backup Management</a></li>
                        <li class="breadcrumb-item active">Restore Backup</li>
                    </ol>
                </div>
                <h4 class="page-title">Restore from Backup</h4>
            </div>
        </div>
    </div>

    <!-- Warning Alert -->
    <div class="row">
        <div class="col-12">
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <i class="mdi mdi-alert-circle-outline me-2"></i>
                <strong>Important:</strong> Restoring from backup will overwrite current data. A backup of the current state
                will be created automatically before restore.
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    </div>

    <!-- Restore Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Select Backup to Restore</h4>

                    <?php if(count($restorableBackups) > 0): ?>
                        <form method="POST" action="<?php echo e(route('backups.restore')); ?>" id="restoreForm">
                            <?php echo csrf_field(); ?>

                            <!-- Backup Selection -->
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="form-group">
                                        <label for="backup_file">Select Backup File <span
                                                class="text-danger">*</span></label>
                                        <select name="backup_file" id="backup_file" class="form-control" required>
                                            <option value="">Choose a backup file...</option>
                                            <?php $__currentLoopData = $restorableBackups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $backup): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($backup['filename']); ?>" data-type="<?php echo e($backup['type']); ?>"
                                                    data-size="<?php echo e($backup['size']); ?>"
                                                    data-date="<?php echo e($backup['created_at']); ?>"
                                                    <?php echo e(request('file') == $backup['filename'] ? 'selected' : ''); ?>>
                                                    <?php echo e($backup['filename']); ?> (<?php echo e($backup['type']); ?> -
                                                    <?php echo e($backup['size']); ?>)
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>&nbsp;</label>
                                        <button type="button" id="previewBtn" class="btn btn-info btn-block" disabled>
                                            <i class="mdi mdi-eye"></i> Preview Backup Info
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Backup Info Preview -->
                            <div id="backupInfo" class="row" style="display: none;">
                                <div class="col-12">
                                    <div class="card border-info">
                                        <div class="card-header bg-info text-white">
                                            <h5 class="card-title mb-0">
                                                <i class="mdi mdi-information"></i> Backup Information
                                            </h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <table class="table table-sm">
                                                        <tr>
                                                            <td><strong>Filename:</strong></td>
                                                            <td id="info-filename">-</td>
                                                        </tr>
                                                        <tr>
                                                            <td><strong>Type:</strong></td>
                                                            <td id="info-type">-</td>
                                                        </tr>
                                                        <tr>
                                                            <td><strong>Size:</strong></td>
                                                            <td id="info-size">-</td>
                                                        </tr>
                                                        <tr>
                                                            <td><strong>Created:</strong></td>
                                                            <td id="info-created">-</td>
                                                        </tr>
                                                    </table>
                                                </div>
                                                <div class="col-md-6">
                                                    <h6>Components to be restored:</h6>
                                                    <ul id="info-components" class="list-unstyled">
                                                        <!-- Components will be populated by JavaScript -->
                                                    </ul>
                                                    <p id="info-description" class="text-muted">
                                                        <!-- Description will be populated by JavaScript -->
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Restore Options -->
                            <div class="row">
                                <div class="col-12">
                                    <h5 class="mt-3">Restore Options</h5>
                                    <div class="form-group">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="skip_current_backup"
                                                name="restore_options[skip_current_backup]" value="1">
                                            <label class="custom-control-label" for="skip_current_backup">
                                                Skip creating backup of current state
                                                <small class="d-block text-muted">Not recommended unless you have a recent
                                                    backup</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Buttons -->
                            <div class="row">
                                <div class="col-12">
                                    <hr>
                                    <button type="submit" class="btn btn-danger" id="restoreBtn" disabled>
                                        <i class="mdi mdi-restore"></i> Restore from Backup
                                    </button>
                                    <a href="<?php echo e(route('backups.index')); ?>" class="btn btn-secondary">
                                        <i class="mdi mdi-arrow-left"></i> Back to Backups
                                    </a>
                                </div>
                            </div>
                        </form>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="mdi mdi-backup-restore h1 text-muted"></i>
                            <h4 class="text-muted">No Restorable Backups Found</h4>
                            <p class="text-muted">Create some backups first before you can restore from them.</p>
                            <a href="<?php echo e(route('backups.index')); ?>" class="btn btn-primary">
                                <i class="mdi mdi-plus"></i> Create Backup
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Restore Process Information -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="mdi mdi-information text-info"></i> Restore Process
                    </h5>
                    <ol class="list-unstyled">
                        <li><strong>1.</strong> Current state backup is created automatically</li>
                        <li><strong>2.</strong> Selected backup is validated and extracted</li>
                        <li><strong>3.</strong> Data is restored according to backup type</li>
                        <li><strong>4.</strong> System is verified and ready for use</li>
                        <li><strong>5.</strong> Restore operation is logged for audit</li>
                    </ol>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="mdi mdi-shield-check text-success"></i> Safety Features
                    </h5>
                    <ul class="list-unstyled">
                        <li>• Automatic backup before restore</li>
                        <li>• Backup validation before processing</li>
                        <li>• Rollback capability if restore fails</li>
                        <li>• Complete activity logging</li>
                        <li>• File integrity verification</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script>
        $(document).ready(function() {
            // Check if a file is pre-selected and trigger preview
            const initialFile = $('#backup_file').val();
            if (initialFile) {
                $('#previewBtn').prop('disabled', false);
                $('#restoreBtn').prop('disabled', false);
                setTimeout(function() {
                    $('#previewBtn').trigger('click');
                }, 500);
            }

            $('#backup_file').on('change', function() {
                const selectedFile = $(this).val();
                const previewBtn = $('#previewBtn');
                const restoreBtn = $('#restoreBtn');

                if (selectedFile) {
                    previewBtn.prop('disabled', false);
                    restoreBtn.prop('disabled', false);
                } else {
                    previewBtn.prop('disabled', true);
                    restoreBtn.prop('disabled', true);
                    $('#backupInfo').hide();
                }
            });

            $('#previewBtn').on('click', function() {
                const selectedFile = $('#backup_file').val();
                if (!selectedFile) return;

                // Show loading state
                $(this).prop('disabled', true).html('<i class="mdi mdi-loading mdi-spin"></i> Loading...');

                $.get('<?php echo e(route('backups.backup.info')); ?>', {
                        filename: selectedFile
                    })
                    .done(function(data) {
                        // Populate backup info
                        $('#info-filename').text(data.filename);
                        $('#info-type').html('<span class="badge badge-primary">' + data.type.replace(
                            '_', ' ').toUpperCase() + '</span>');
                        $('#info-size').text(data.size);
                        $('#info-created').text(data.created_at + ' (' + data.created_ago + ')');
                        $('#info-description').text(data.description);

                        // Populate components
                        const componentsList = $('#info-components');
                        componentsList.empty();
                        if (data.components && data.components.length > 0) {
                            data.components.forEach(function(component) {
                                componentsList.append(
                                    '<li><i class="mdi mdi-check text-success"></i> ' +
                                    component + '</li>');
                            });
                        }

                        $('#backupInfo').show();
                    })
                    .fail(function() {
                        alert('Failed to load backup information');
                    })
                    .always(function() {
                        $('#previewBtn').prop('disabled', false).html(
                            '<i class="mdi mdi-eye"></i> Preview Backup Info');
                    });
            });

            $('#restoreForm').on('submit', function(e) {
                const confirmed = confirm(
                    'Are you sure you want to restore from this backup?\n\n' +
                    'This will:\n' +
                    '• Create a backup of current state\n' +
                    '• Overwrite current data with backup data\n' +
                    '• This action cannot be easily undone\n\n' +
                    'Continue with restore?'
                );

                if (!confirmed) {
                    e.preventDefault();
                    return false;
                }

                // Show loading state
                $('#restoreBtn').prop('disabled', true).html(
                    '<i class="mdi mdi-loading mdi-spin"></i> Restoring...');
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\alquran\resources\views/backups/restore.blade.php ENDPATH**/ ?>