<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('quran.dashboard')); ?>">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo e(route('audits.index')); ?>">Audit Trails</a></li>
                    <li class="breadcrumb-item active">Audit Details</li>
                </ol>
            </div>
            <h4 class="page-title">Audit Details</h4>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5 class="card-title">Basic Information</h5>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Date/Time:</strong></td>
                                <td><?php echo e($audit->created_at->format('Y-m-d H:i:s')); ?></td>
                            </tr>
                            <tr>
                                <td><strong>User:</strong></td>
                                <td>
                                    <?php if($audit->user): ?>
                                        <span class="badge badge-soft-primary"><?php echo e($audit->user->name); ?></span>
                                    <?php else: ?>
                                        <span class="badge badge-soft-secondary">System</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Event:</strong></td>
                                <td>
                                    <span class="badge badge-soft-<?php echo e($audit->event == 'created' ? 'success' : ($audit->event == 'updated' ? 'warning' : 'danger')); ?>">
                                        <?php echo e($audit->formatted_event); ?>

                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Model:</strong></td>
                                <td><?php echo e($audit->model_name); ?> #<?php echo e($audit->auditable_id); ?></td>
                            </tr>
                            <tr>
                                <td><strong>IP Address:</strong></td>
                                <td><?php echo e($audit->ip_address); ?></td>
                            </tr>
                            <tr>
                                <td><strong>URL:</strong></td>
                                <td><small><?php echo e($audit->url); ?></small></td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="col-md-6">
                        <h5 class="card-title">User Agent</h5>
                        <p class="text-muted small"><?php echo e($audit->user_agent); ?></p>
                        
                        <?php if($audit->tags): ?>
                            <h5 class="card-title mt-3">Tags</h5>
                            <?php $__currentLoopData = $audit->tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <span class="badge badge-soft-info mr-1"><?php echo e($tag); ?></span>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </div>
                </div>

                <?php if($audit->changes): ?>
                    <hr>
                    <h5 class="card-title">Changes Made</h5>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Field</th>
                                    <th>Old Value</th>
                                    <th>New Value</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $audit->changes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $field => $change): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><strong><?php echo e(ucfirst(str_replace('_', ' ', $field))); ?></strong></td>
                                        <td>
                                            <?php if(is_null($change['old'])): ?>
                                                <span class="text-muted">null</span>
                                            <?php else: ?>
                                                <code><?php echo e(is_array($change['old']) ? json_encode($change['old']) : $change['old']); ?></code>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if(is_null($change['new'])): ?>
                                                <span class="text-muted">null</span>
                                            <?php else: ?>
                                                <code><?php echo e(is_array($change['new']) ? json_encode($change['new']) : $change['new']); ?></code>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>

                <?php if($audit->old_values && $audit->event == 'deleted'): ?>
                    <hr>
                    <h5 class="card-title">Deleted Data</h5>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Field</th>
                                    <th>Value</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $audit->old_values; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $field => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><strong><?php echo e(ucfirst(str_replace('_', ' ', $field))); ?></strong></td>
                                        <td>
                                            <?php if(is_null($value)): ?>
                                                <span class="text-muted">null</span>
                                            <?php else: ?>
                                                <code><?php echo e(is_array($value) ? json_encode($value) : $value); ?></code>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>

                <?php if($audit->new_values && $audit->event == 'created'): ?>
                    <hr>
                    <h5 class="card-title">Created Data</h5>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Field</th>
                                    <th>Value</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $audit->new_values; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $field => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><strong><?php echo e(ucfirst(str_replace('_', ' ', $field))); ?></strong></td>
                                        <td>
                                            <?php if(is_null($value)): ?>
                                                <span class="text-muted">null</span>
                                            <?php else: ?>
                                                <code><?php echo e(is_array($value) ? json_encode($value) : $value); ?></code>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>

                <div class="mt-3">
                    <a href="<?php echo e(route('audits.index')); ?>" class="btn btn-secondary">
                        <i class="mdi mdi-arrow-left"></i> Back to Audit Logs
                    </a>
                    
                    <?php if($audit->auditable): ?>
                        <a href="#" class="btn btn-info" onclick="alert('Model view not implemented yet')">
                            <i class="mdi mdi-eye"></i> View <?php echo e($audit->model_name); ?>

                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\alquran\resources\views/audits/show.blade.php ENDPATH**/ ?>