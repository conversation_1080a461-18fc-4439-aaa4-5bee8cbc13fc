@extends('layouts.master')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('quran.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('users.index') }}">Users</a></li>
                    <li class="breadcrumb-item active">{{ $user->full_name }}</li>
                </ol>
            </div>
            <h4 class="page-title">User Profile</h4>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <!-- User Profile Card -->
        <div class="card">
            <div class="card-body text-center">
                <img src="{{ $user->avatar_url }}" alt="{{ $user->name }}" 
                     class="rounded-circle mb-3" width="120" height="120">
                
                <h4 class="mb-1">{{ $user->full_name }}</h4>
                @if($user->username)
                    <p class="text-muted mb-2">@{{ $user->username }}</p>
                @endif
                
                <div class="mb-3">
                    {!! $user->status_badge !!}
                    @if($user->is_verified)
                        <span class="badge badge-success ms-1">
                            <i class="mdi mdi-check-circle"></i> Verified
                        </span>
                    @endif
                </div>

                @if(auth()->user()->hasPermission('users.update'))
                    <div class="d-grid gap-2">
                        <a href="{{ route('users.edit', $user) }}" class="btn btn-primary">
                            <i class="mdi mdi-pencil"></i> Edit Profile
                        </a>
                        @if($user->id !== auth()->id())
                            <button type="button" class="btn btn-{{ $user->is_active ? 'warning' : 'success' }}" 
                                    onclick="toggleStatus({{ $user->id }})">
                                <i class="mdi mdi-{{ $user->is_active ? 'pause' : 'play' }}"></i>
                                {{ $user->is_active ? 'Deactivate' : 'Activate' }}
                            </button>
                        @endif
                    </div>
                @endif
            </div>
        </div>

        <!-- Contact Information -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="mdi mdi-contact-mail"></i> Contact Information
                </h5>
                
                <table class="table table-borderless table-sm">
                    <tr>
                        <td><i class="mdi mdi-email"></i></td>
                        <td>
                            <a href="mailto:{{ $user->email }}">{{ $user->email }}</a>
                        </td>
                    </tr>
                    @if($user->phone)
                        <tr>
                            <td><i class="mdi mdi-phone"></i></td>
                            <td>
                                <a href="tel:{{ $user->phone }}">{{ $user->phone }}</a>
                            </td>
                        </tr>
                    @endif
                    @if($user->address)
                        <tr>
                            <td><i class="mdi mdi-map-marker"></i></td>
                            <td>{{ $user->address }}</td>
                        </tr>
                    @endif
                    <tr>
                        <td><i class="mdi mdi-earth"></i></td>
                        <td>{{ $user->timezone }}</td>
                    </tr>
                    <tr>
                        <td><i class="mdi mdi-translate"></i></td>
                        <td>{{ $user->language }}</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Account Statistics -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="mdi mdi-chart-pie"></i> Account Statistics
                </h5>
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary">{{ $user->activeRoles->count() }}</h4>
                            <p class="text-muted mb-0">Active Roles</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ $user->getAllPermissions()->count() }}</h4>
                        <p class="text-muted mb-0">Permissions</p>
                    </div>
                </div>

                <hr>

                <table class="table table-borderless table-sm">
                    <tr>
                        <td><strong>Created:</strong></td>
                        <td>{{ $user->created_at->format('M j, Y') }}</td>
                    </tr>
                    <tr>
                        <td><strong>Last Login:</strong></td>
                        <td>
                            @if($user->last_login_at)
                                {{ $user->last_login_at->diffForHumans() }}
                                <br><small class="text-muted">{{ $user->last_login_at->format('M j, Y g:i A') }}</small>
                            @else
                                <span class="text-muted">Never</span>
                            @endif
                        </td>
                    </tr>
                    @if($user->last_login_ip)
                        <tr>
                            <td><strong>Last IP:</strong></td>
                            <td><code>{{ $user->last_login_ip }}</code></td>
                        </tr>
                    @endif
                    @if($user->password_changed_at)
                        <tr>
                            <td><strong>Password Changed:</strong></td>
                            <td>{{ $user->password_changed_at->diffForHumans() }}</td>
                        </tr>
                    @endif
                </table>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <!-- Roles and Permissions -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="mdi mdi-account-group"></i> Roles & Permissions
                    <span class="badge badge-primary ms-2">{{ $user->activeRoles->count() }} roles</span>
                </h5>

                @if($user->activeRoles->count() > 0)
                    <div class="row">
                        @foreach($user->activeRoles as $role)
                            <div class="col-md-6 mb-3">
                                <div class="card border">
                                    <div class="card-header py-2" style="background-color: {{ $role->color }}20;">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="badge" style="background-color: {{ $role->color }}; color: white;">
                                                {{ $role->display_name }}
                                            </span>
                                            <div>
                                                @if($role->is_system)
                                                    <span class="badge badge-danger">System</span>
                                                @endif
                                                @if(auth()->user()->hasPermission('roles.read'))
                                                    <a href="{{ route('roles.show', $role) }}" class="btn btn-xs btn-outline-primary">
                                                        <i class="mdi mdi-eye"></i>
                                                    </a>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body py-2">
                                        <small>
                                            <strong>Permissions:</strong> {{ $role->permissions->count() }}<br>
                                            <strong>Assigned:</strong> {{ $role->pivot->assigned_at->format('M j, Y') }}
                                            @if($role->pivot->expires_at)
                                                <br><strong>Expires:</strong> 
                                                <span class="text-warning">{{ $role->pivot->expires_at->format('M j, Y') }}</span>
                                            @else
                                                <br><span class="text-success">Permanent</span>
                                            @endif
                                        </small>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="mdi mdi-account-group-outline h2 text-muted"></i>
                        <h5 class="text-muted">No roles assigned</h5>
                        <p class="text-muted">This user has no active roles assigned.</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Permissions Breakdown -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="mdi mdi-key"></i> Permission Breakdown
                    <span class="badge badge-info ms-2">{{ $user->getAllPermissions()->count() }} permissions</span>
                </h5>

                @php
                    $permissionsByModule = $user->getAllPermissions()->groupBy('module');
                @endphp

                @if($permissionsByModule->count() > 0)
                    <div class="row">
                        @foreach($permissionsByModule as $module => $permissions)
                            <div class="col-md-6 mb-3">
                                <div class="card border">
                                    <div class="card-header bg-light py-2">
                                        <h6 class="mb-0">
                                            <span class="badge badge-secondary">{{ ucfirst($module) }}</span>
                                            <small class="text-muted">({{ $permissions->count() }} permissions)</small>
                                        </h6>
                                    </div>
                                    <div class="card-body py-2">
                                        <div class="row">
                                            @foreach($permissions as $permission)
                                                <div class="col-6">
                                                    <small>{!! $permission->action_badge !!}</small>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="mdi mdi-key-outline h2 text-muted"></i>
                        <h5 class="text-muted">No permissions</h5>
                        <p class="text-muted">This user has no permissions through their roles.</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="mdi mdi-history"></i> Recent Activity
                </h5>

                @if($user->recentActivities && $user->recentActivities->count() > 0)
                    <div class="timeline">
                        @foreach($user->recentActivities->take(10) as $activity)
                            <div class="timeline-item">
                                <div class="timeline-marker"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">{{ $activity->description }}</h6>
                                    <p class="timeline-text">
                                        <small class="text-muted">
                                            {{ $activity->created_at->diffForHumans() }} 
                                            ({{ $activity->created_at->format('M j, Y g:i A') }})
                                        </small>
                                    </p>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    @if(auth()->user()->hasPermission('audit.read'))
                        <div class="text-center mt-3">
                            <a href="{{ route('audit.index') }}?user_id={{ $user->id }}" class="btn btn-outline-primary">
                                View All Activity
                            </a>
                        </div>
                    @endif
                @else
                    <div class="text-center py-4">
                        <i class="mdi mdi-history h2 text-muted"></i>
                        <h5 class="text-muted">No recent activity</h5>
                        <p class="text-muted">No recent activity recorded for this user.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function toggleStatus(userId) {
    if (confirm('Are you sure you want to change the status of this user?')) {
        fetch(`/users/${userId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.error || 'An error occurred');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the user status');
        });
    }
}
</script>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #007bff;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #007bff;
}

.timeline-item:before {
    content: '';
    position: absolute;
    left: -31px;
    top: 15px;
    bottom: -20px;
    width: 2px;
    background-color: #e9ecef;
}

.timeline-item:last-child:before {
    display: none;
}

.timeline-title {
    margin-bottom: 5px;
    font-size: 14px;
}

.timeline-text {
    margin-bottom: 0;
    font-size: 12px;
}
</style>
@endsection
