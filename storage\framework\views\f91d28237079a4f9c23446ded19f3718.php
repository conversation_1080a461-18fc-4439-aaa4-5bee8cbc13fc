<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>AL QURAN - Quran analysis by topics of quran</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="A fully featured admin theme which can be used to build CRM, CMS, etc." name="description" />
    <meta content="Coderthemes" name="author" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!-- App favicon -->
    <link rel="shortcut icon" href="<?php echo e(asset('assets/images/favicon.ico')); ?>">

    <!-- third party css -->
    <link href="<?php echo e(asset('assets/libs/datatables/dataTables.bootstrap4.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/libs/datatables/buttons.bootstrap4.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/libs/datatables/responsive.bootstrap4.css')); ?>" rel="stylesheet" type="text/css" />

    <!-- App css -->
    <link href="<?php echo e(asset('assets/css/bootstrap.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/css/icons.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/css/app.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/libs/select2/select2.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/libs/bootstrap-select/bootstrap-select.min.css')); ?>" rel="stylesheet"
        type="text/css" />

    <!-- Custom CSS for Audit System -->
    <style>
        /* Soft Badge Styles */
        .badge-soft-primary {
            color: #6c5ce7 !important;
            background-color: rgba(108, 92, 231, 0.1) !important;
        }

        .badge-soft-secondary {
            color: #6c757d !important;
            background-color: rgba(108, 117, 125, 0.1) !important;
        }

        .badge-soft-success {
            color: #00b894 !important;
            background-color: rgba(0, 184, 148, 0.1) !important;
        }

        .badge-soft-info {
            color: #00cec9 !important;
            background-color: rgba(0, 206, 201, 0.1) !important;
        }

        .badge-soft-warning {
            color: #fdcb6e !important;
            background-color: rgba(253, 203, 110, 0.1) !important;
        }

        .badge-soft-danger {
            color: #e17055 !important;
            background-color: rgba(225, 112, 85, 0.1) !important;
        }

        /* Widget Icon Styles */
        .widget-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            font-size: 20px;
        }

        .bg-success-lighten {
            background-color: rgba(0, 184, 148, 0.1) !important;
        }

        .bg-info-lighten {
            background-color: rgba(0, 206, 201, 0.1) !important;
        }

        .bg-warning-lighten {
            background-color: rgba(253, 203, 110, 0.1) !important;
        }

        .bg-primary-lighten {
            background-color: rgba(108, 92, 231, 0.1) !important;
        }

        .text-success {
            color: #00b894 !important;
        }

        .text-info {
            color: #00cec9 !important;
        }

        .text-warning {
            color: #fdcb6e !important;
        }

        .text-primary {
            color: #6c5ce7 !important;
        }
    </style>

</head>

<body>

    <!-- Begin page -->
    <div id="wrapper">

        <?php echo $__env->make('include.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- =============================================== -->

        <?php echo $__env->make('include.sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- =============================================== -->

        <!-- ============================================================== -->
        <!-- Start Page Content here -->
        <!-- ============================================================== -->

        <div class="content-page">
            <div class="content">

                <!-- Start Content-->
                <div class="container-fluid">

                    <!-- start page title -->
                    
                    <!-- end page title -->
                    <?php echo $__env->yieldContent('content'); ?>


                </div> <!-- end container-fluid -->

            </div> <!-- end content -->

            <?php echo $__env->make('include.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- =============================================== -->

        </div>

        <!-- ============================================================== -->
        <!-- End Page content -->
        <!-- ============================================================== -->

    </div>
    <!-- END wrapper -->

    <!-- Right Sidebar -->
    <div class="right-bar">
        <div class="rightbar-title">
            <a href="javascript:void(0);" class="right-bar-toggle float-right">
                <i class="mdi mdi-close"></i>
            </a>
            <h5 class="m-0 text-white">Settings</h5>
        </div>
        <div class="slimscroll-menu">
            <hr class="mt-0">
            <h5 class="pl-3">Basic Settings</h5>
            <hr class="mb-0" />


            <div class="p-3">
                <div class="custom-control custom-checkbox mb-2">
                    <input type="checkbox" class="custom-control-input" id="customCheck1" checked>
                    <label class="custom-control-label" for="customCheck1">Notifications</label>
                </div>
                <div class="custom-control custom-checkbox mb-2">
                    <input type="checkbox" class="custom-control-input" id="customCheck2" checked>
                    <label class="custom-control-label" for="customCheck2">API Access</label>
                </div>
                <div class="custom-control custom-checkbox mb-2">
                    <input type="checkbox" class="custom-control-input" id="customCheck3">
                    <label class="custom-control-label" for="customCheck3">Auto Updates</label>
                </div>
                <div class="custom-control custom-checkbox mb-2">
                    <input type="checkbox" class="custom-control-input" id="customCheck4" checked>
                    <label class="custom-control-label" for="customCheck4">Online Status</label>
                </div>
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" id="customCheck5">
                    <label class="custom-control-label" for="customCheck5">Auto Payout</label>
                </div>
            </div>

            <!-- Messages -->
            <hr class="mt-0" />
            <h5 class="pl-3 pr-3">Messages <span class="float-right badge badge-pill badge-danger">24</span></h5>
            <hr class="mb-0" />
            <div class="p-3">
                <div class="inbox-widget">
                    <div class="inbox-item">
                        <div class="inbox-item-img"><img src="<?php echo e(asset('assets/images/users/avatar-1.jpg')); ?>"
                                class="rounded-circle" alt=""></div>
                        <p class="inbox-item-author"><a href="javascript: void(0);">Chadengle</a></p>
                        <p class="inbox-item-text">Hey! there I'm available...</p>
                        <p class="inbox-item-date">13:40 PM</p>
                    </div>
                    <div class="inbox-item">
                        <div class="inbox-item-img"><img src="<?php echo e(asset('assets/images/users/avatar-2.jpg')); ?>"
                                class="rounded-circle" alt=""></div>
                        <p class="inbox-item-author"><a href="javascript: void(0);">Tomaslau</a></p>
                        <p class="inbox-item-text">I've finished it! See you so...</p>
                        <p class="inbox-item-date">13:34 PM</p>
                    </div>
                    <div class="inbox-item">
                        <div class="inbox-item-img"><img src="<?php echo e(asset('assets/images/users/avatar-3.jpg')); ?>"
                                class="rounded-circle" alt=""></div>
                        <p class="inbox-item-author"><a href="javascript: void(0);">Stillnotdavid</a></p>
                        <p class="inbox-item-text">This theme is awesome!</p>
                        <p class="inbox-item-date">13:17 PM</p>
                    </div>

                    <div class="inbox-item">
                        <div class="inbox-item-img"><img src="<?php echo e(asset('assets/images/users/avatar-4.jpg')); ?>"
                                class="rounded-circle" alt=""></div>
                        <p class="inbox-item-author"><a href="javascript: void(0);">Kurafire</a></p>
                        <p class="inbox-item-text">Nice to meet you</p>
                        <p class="inbox-item-date">12:20 PM</p>

                    </div>
                    <div class="inbox-item">
                        <div class="inbox-item-img"><img src="<?php echo e(asset('assets/images/users/avatar-5.jpg')); ?>"
                                class="rounded-circle" alt=""></div>
                        <p class="inbox-item-author"><a href="javascript: void(0);">Shahedk</a></p>
                        <p class="inbox-item-text">Hey! there I'm available...</p>
                        <p class="inbox-item-date">10:15 AM</p>

                    </div>
                </div> <!-- end inbox-widget -->
            </div> <!-- end .p-3-->

        </div> <!-- end slimscroll-menu-->
    </div>
    <!-- /Right-bar -->

    <!-- Right bar overlay-->
    <div class="rightbar-overlay"></div>

    <!-- Vendor js -->
    <script src="<?php echo e(asset('assets/js/vendor.min.js')); ?>"></script>

    <!-- App js -->
    <script src="<?php echo e(asset('assets/js/app.min.js')); ?>"></script>

    <!-- Required datatable js -->
    <script src="<?php echo e(asset('assets/libs/datatables/jquery.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/datatables/dataTables.bootstrap4.min.js')); ?>"></script>
    <!-- Buttons examples -->
    <script src="<?php echo e(asset('assets/libs/datatables/dataTables.buttons.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/datatables/buttons.bootstrap4.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/jszip/jszip.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/pdfmake/pdfmake.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/pdfmake/vfs_fonts.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/datatables/buttons.html5.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/datatables/buttons.print.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/select2/select2.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/bootstrap-select/bootstrap-select.min.js')); ?>"></script>

    <!-- Responsive examples -->
    <script src="<?php echo e(asset('assets/libs/datatables/dataTables.responsive.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/datatables/responsive.bootstrap4.min.js')); ?>"></script>

    <!-- Datatables init -->
    <script src="<?php echo e(asset('assets/js/pages/datatables.init.js')); ?>"></script>

    <!-- App js -->
    <script src="<?php echo e(asset('assets/js/app.min.js')); ?>"></script>

    <!-- Plugin js-->
    <script src="<?php echo e(asset('assets/libs/parsleyjs/parsley.min.js')); ?>"></script>

    <!-- Validation init js-->
    <script src="<?php echo e(asset('assets/js/pages/form-validation.init.js')); ?>"></script>

    <!-- Tost-->
    <script src="<?php echo e(asset('assets/libs/jquery-toast/jquery.toast.min.js')); ?>"></script>

    <!-- toastr init js-->
    <script src="<?php echo e(asset('assets/js/pages/toastr.init.js')); ?>"></script>

    <!-- Chart JS -->
    <script src="<?php echo e(asset('assets/libs/chart-js/Chart.bundle.min.js')); ?>"></script>

    <!-- Init js -->
    <script src="<?php echo e(asset('assets/js/pages/dashboard.init.js')); ?>"></script>


    <script type="text/javascript">
        $('.delid').click(function() {
            /* your code here */
            var x = confirm("Are you sure to delete this record?");
            if (x)
                return true;
            else
                return false;
        })

        // Global function for scrolling to sections
        function scrollToSection(sectionId) {
            // If we're not on the backups page, navigate there first
            if (!window.location.pathname.includes('/backups')) {
                window.location.href = '<?php echo e(route('backups.index')); ?>#' + sectionId;
                return;
            }

            // If we're already on the page, scroll to the section
            const element = document.getElementById(sectionId);
            if (element) {
                element.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        }

        // Auto-scroll to section if hash is present in URL
        $(document).ready(function() {
            if (window.location.hash) {
                const sectionId = window.location.hash.substring(1);
                setTimeout(function() {
                    scrollToSection(sectionId);
                }, 500);
            }
        });
    </script>
    <?php echo $__env->yieldContent('scripts'); ?>


</body>

</html>
<?php /**PATH C:\laragon\www\alquran\resources\views/layouts/master.blade.php ENDPATH**/ ?>