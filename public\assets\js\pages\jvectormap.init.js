!function(a){"use strict";var n=function(){};n.prototype.init=function(){a("#world-map-markers").vectorMap({map:"world_mill_en",normalizeFunction:"polynomial",hoverOpacity:.7,hoverColor:!1,regionStyle:{initial:{fill:"#5d6dc3"}},markerStyle:{initial:{r:9,fill:"#45bbe0","fill-opacity":.9,stroke:"#fff","stroke-width":7,"stroke-opacity":.4},hover:{stroke:"#fff","fill-opacity":1,"stroke-width":1.5}},backgroundColor:"transparent",markers:[{latLng:[41.9,12.45],name:"Vatican City"},{latLng:[43.73,7.41],name:"Monaco"},{latLng:[-.52,166.93],name:"Na<PERSON>"},{latLng:[-8.51,179.21],name:"Tuvalu"},{latLng:[43.93,12.46],name:"San Marino"},{latLng:[47.14,9.52],name:"Liechtenstein"},{latLng:[7.11,171.06],name:"Marshall Islands"},{latLng:[17.3,-62.73],name:"<PERSON> <PERSON><PERSON> and <PERSON>ev<PERSON>"},{lat<PERSON>ng:[3.2,73.22],name:"<PERSON><PERSON>"},{latLng:[35.88,14.5],name:"Malta"},{latLng:[12.05,-61.75],name:"Grena<PERSON>"},{latLng:[13.16,-61.23],name:"<PERSON> <PERSON> and the <PERSON>renadines"},{latLng:[13.16,-59.55],name:"Barbados"},{latLng:[17.11,-61.85],name:"Antigua and Barbuda"},{latLng:[-4.61,55.45],name:"Seychelles"},{latLng:[7.35,134.46],name:"Palau"},{latLng:[42.5,1.51],name:"Andorra"},{latLng:[14.01,-60.98],name:"Saint Lucia"},{latLng:[6.91,158.18],name:"Federated States of Micronesia"},{latLng:[1.3,103.8],name:"Singapore"},{latLng:[1.46,173.03],name:"Kiribati"},{latLng:[-21.13,-175.2],name:"Tonga"},{latLng:[15.3,-61.38],name:"Dominica"},{latLng:[-20.2,57.5],name:"Mauritius"},{latLng:[26.02,50.55],name:"Bahrain"},{latLng:[.33,6.73],name:"São Tomé and Príncipe"}]}),a("#usa").vectorMap({map:"us_merc_en",backgroundColor:"transparent",regionStyle:{initial:{fill:"#4489e4"}}}),a("#india").vectorMap({map:"in_mill_en",backgroundColor:"transparent",regionStyle:{initial:{fill:"#3ec396"}}}),a("#uk").vectorMap({map:"uk_mill_en",backgroundColor:"transparent",regionStyle:{initial:{fill:"#4fbde9"}}}),a("#chicago").vectorMap({map:"us-il-chicago_mill_en",backgroundColor:"transparent",regionStyle:{initial:{fill:"#f9bc0b"}}}),a("#australia").vectorMap({map:"au_mill_en",backgroundColor:"transparent",regionStyle:{initial:{fill:"#f36270"}}}),a("#canada").vectorMap({map:"ca_lcc_en",backgroundColor:"transparent",regionStyle:{initial:{fill:"#9368f3"}}})},a.VectorMap=new n,a.VectorMap.Constructor=n}(window.jQuery),function(a){"use strict";window.jQuery.VectorMap.init()}();