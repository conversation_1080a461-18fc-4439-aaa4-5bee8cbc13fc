@extends('layouts.master')

@section('content')
    <!-- Row -->

    <div class="row">
        <div class="col-12">
            <div class="card-box table-responsive">
                <a href="{{ route('chapts.create') }}" class="btn btn-primary float-right">Add Quran Chapter</a>
                <h4 class="header-title"><b>Quran Chapters List</b></h4>
                <p class="sub-header">
                    List of Chapters of Quran.
                </p>

                <table id="datatable-buttons" class="table table-striped table-bordered dt-responsive nowrap"
                    style="border-collapse: collapse; border-spacing: 0; width: 100%;">
                    <thead>
                        <tr>
                            <th class="border-bottom-0">ID</th>
                            <th class="border-bottom-0">Chapter Name</th>
                            <th class="border-bottom-0">Status</th>
                            <th class="border-bottom-0">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($chapts as $chapt)
                            <tr>
                                <td>{{ $chapt->id }}</td>
                                <td>{{ $chapt->chaptername }}</td>
                                <td><span
                                        class="badge badge-{{ $chapt->isactive == 1 ? 'primary' : 'danger' }}">{{ $chapt->isactive == 1 ? 'Active' : 'Disable' }}</span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('chapts.edit', $chapt->id) }}" class="btn btn-sm btn-purple"><i
                                                class="far fa-edit"></i></a>
                                        <form action="{{ route('chapts.destroy', $chapt) }}" method="POST"
                                            style="display: inline-block">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit"
                                                onclick="return confirm('Are you sure to delete this record?')"
                                                class="btn btn-danger btn-sm"><i class="fas fa-trash-alt"></i></button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach

                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <!-- End Row -->
@endsection
