<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('quran.dashboard')); ?>">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo e(route('user-activities.index')); ?>">User Activities</a></li>
                    <li class="breadcrumb-item active">Activity Details</li>
                </ol>
            </div>
            <h4 class="page-title">Activity Details</h4>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5 class="card-title">Basic Information</h5>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Date/Time:</strong></td>
                                <td><?php echo e($activity->created_at->format('Y-m-d H:i:s')); ?></td>
                            </tr>
                            <tr>
                                <td><strong>User:</strong></td>
                                <td>
                                    <?php if($activity->user): ?>
                                        <span class="badge badge-soft-primary"><?php echo e($activity->user->name); ?></span>
                                    <?php else: ?>
                                        <span class="badge badge-soft-secondary">Guest</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Activity Type:</strong></td>
                                <td>
                                    <span class="badge badge-soft-<?php echo e($activity->activity_type == 'login' ? 'success' : 
                                        ($activity->activity_type == 'logout' ? 'warning' : 
                                        ($activity->activity_type == 'delete' ? 'danger' : 'info'))); ?>">
                                        <?php echo e($activity->formatted_activity_type); ?>

                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Description:</strong></td>
                                <td><?php echo e($activity->description); ?></td>
                            </tr>
                            <tr>
                                <td><strong>HTTP Method:</strong></td>
                                <td>
                                    <?php if($activity->method): ?>
                                        <span class="badge badge-soft-secondary"><?php echo e($activity->method); ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>IP Address:</strong></td>
                                <td><?php echo e($activity->ip_address); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Session ID:</strong></td>
                                <td><small><?php echo e($activity->session_id); ?></small></td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="col-md-6">
                        <h5 class="card-title">Technical Details</h5>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Browser:</strong></td>
                                <td><?php echo e($activity->browser); ?></td>
                            </tr>
                            <tr>
                                <td><strong>User Agent:</strong></td>
                                <td><small class="text-muted"><?php echo e($activity->user_agent); ?></small></td>
                            </tr>
                            <tr>
                                <td><strong>URL:</strong></td>
                                <td><small><?php echo e($activity->url); ?></small></td>
                            </tr>
                            <?php if($activity->subject_type): ?>
                                <tr>
                                    <td><strong>Subject:</strong></td>
                                    <td><?php echo e($activity->subject_name); ?> #<?php echo e($activity->subject_id); ?></td>
                                </tr>
                            <?php endif; ?>
                        </table>
                    </div>
                </div>

                <?php if($activity->properties): ?>
                    <hr>
                    <h5 class="card-title">Additional Properties</h5>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Property</th>
                                    <th>Value</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $activity->properties; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><strong><?php echo e(ucfirst(str_replace('_', ' ', $key))); ?></strong></td>
                                        <td>
                                            <?php if(is_null($value)): ?>
                                                <span class="text-muted">null</span>
                                            <?php elseif(is_bool($value)): ?>
                                                <span class="badge badge-soft-<?php echo e($value ? 'success' : 'danger'); ?>">
                                                    <?php echo e($value ? 'true' : 'false'); ?>

                                                </span>
                                            <?php elseif(is_array($value) || is_object($value)): ?>
                                                <pre class="bg-light p-2 rounded"><code><?php echo e(json_encode($value, JSON_PRETTY_PRINT)); ?></code></pre>
                                            <?php else: ?>
                                                <code><?php echo e($value); ?></code>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>

                <div class="mt-3">
                    <a href="<?php echo e(route('user-activities.index')); ?>" class="btn btn-secondary">
                        <i class="mdi mdi-arrow-left"></i> Back to Activities
                    </a>
                    
                    <?php if($activity->subject): ?>
                        <a href="#" class="btn btn-info" onclick="alert('Subject view not implemented yet')">
                            <i class="mdi mdi-eye"></i> View <?php echo e($activity->subject_name); ?>

                        </a>
                    <?php endif; ?>

                    <?php if($activity->user): ?>
                        <a href="#" class="btn btn-primary" onclick="alert('User profile not implemented yet')">
                            <i class="mdi mdi-account"></i> View User Profile
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\alquran\resources\views/user-activities/show.blade.php ENDPATH**/ ?>