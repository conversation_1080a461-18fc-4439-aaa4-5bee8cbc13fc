<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Surah;
use App\Models\Chapter;
use App\Models\Language;
use App\Models\Topics;
use App\Models\TopicDetail;
use Carbon\Carbon;

class QuranDashboardController extends Controller
{
    public function index()
    {
        // Get counts - force integer values
        $surahCount = (int)Surah::count();
        $chapterCount = (int)Chapter::count();
        $languageCount = (int)Language::count();
        $topicCount = (int)Topics::count();

        // Debug log to verify the count
        \Log::info('Dashboard Counts', [
            'surahCount' => $surahCount,
            'chapterCount' => $chapterCount,
            'languageCount' => $languageCount,
            'topicCount' => $topicCount
        ]);

        // Get new items counts (added this month)
        $startOfMonth = Carbon::now()->startOfMonth();
        $newChaptersCount = (int)Chapter::where('created_at', '>=', $startOfMonth)->count();
        $newLanguagesCount = (int)Language::where('created_at', '>=', $startOfMonth)->count();
        $newTopicsCount = (int)Topics::where('created_at', '>=', $startOfMonth)->count();

        // Debug information - you can remove this after fixing the issue
        \Log::info('Dashboard Counts', [
            'surahCount' => $surahCount,
            'chapterCount' => $chapterCount,
            'languageCount' => $languageCount,
            'topicCount' => $topicCount
        ]);

        // Get recent topics
        $recentTopics = Topics::orderBy('created_at', 'desc')
            ->take(5)
            ->get()
            ->map(function ($topic) {
                // Ensure created_at is not null
                if (!$topic->created_at) {
                    $topic->created_at = now();
                }
                return $topic;
            });

        // Get recent chapters
        $recentChapters = Chapter::orderBy('created_at', 'desc')
            ->take(5)
            ->get()
            ->map(function ($chapter) {
                // Ensure created_at is not null
                if (!$chapter->created_at) {
                    $chapter->created_at = now();
                }
                // Ensure language is set
                $chapter->language = Language::find($chapter->language_id)->name ?? 'Unknown';
                return $chapter;
            });

        // Get popular content (most viewed)
        // Note: Assuming you have a 'views' column in your tables
        // If not, you can modify this to use another metric or add placeholder data
        $popularSurahs = Surah::orderBy('id', 'desc')
            ->take(5)
            ->get()
            ->map(function ($surah) {
                // Add placeholder data if needed
                $surah->views = rand(100, 5000);
                $surah->verses_count = rand(3, 286);
                return $surah;
            });

        $popularTopics = Topics::orderBy('id', 'desc')
            ->take(5)
            ->get()
            ->map(function ($topic) {
                // Add placeholder data if needed
                $topic->views = rand(50, 2000);
                $topic->details_count = TopicDetail::where('topic_id', $topic->id)->count();
                $topic->category = ['Faith', 'Worship', 'Ethics', 'History', 'Guidance'][rand(0, 4)];
                return $topic;
            });

        $popularChapters = Chapter::orderBy('id', 'desc')
            ->take(5)
            ->get()
            ->map(function ($chapter) {
                // Add placeholder data if needed
                $chapter->views = rand(75, 3000);
                $chapter->content_size = rand(1, 50) . ' KB';
                $chapter->language = Language::find($chapter->language_id)->name ?? 'Unknown';
                return $chapter;
            });

        // Make sure all variables are being passed to the view
        return view('dashboard.quran', compact(
            'surahCount',
            'chapterCount',
            'languageCount',
            'topicCount',
            'newChaptersCount',
            'newLanguagesCount',
            'newTopicsCount',
            'recentTopics',
            'recentChapters',
            'popularSurahs',
            'popularTopics',
            'popularChapters'
        ));
    }
}
