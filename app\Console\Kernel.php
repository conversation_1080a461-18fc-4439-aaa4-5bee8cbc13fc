<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Commands\ImportQuran::class,
        Commands\ImportQuranTranslation::class,
        Commands\ImportTafsir::class,
        Commands\ImportAllQuranTranslations::class,
        Commands\CreateBackupCommand::class,
    ];

    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Create daily database backup at 2 AM
        $schedule->command('backup:create database --cleanup=30')
            ->dailyAt('02:00')
            ->name('daily-database-backup')
            ->withoutOverlapping();

        // Create weekly full backup on Sundays at 3 AM
        $schedule->command('backup:create full --cleanup=90')
            ->weeklyOn(0, '03:00')
            ->name('weekly-full-backup')
            ->withoutOverlapping();

        // Monthly cleanup of old backups
        $schedule->command('backup:create database --cleanup=7')
            ->monthlyOn(1, '04:00')
            ->name('monthly-backup-cleanup');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
