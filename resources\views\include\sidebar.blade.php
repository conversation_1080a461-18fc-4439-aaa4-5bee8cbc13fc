<!-- ========== Left Sidebar Start ========== -->
<div class="left-side-menu">

    <div class="slimscroll-menu">

        <!--- Sidemenu -->
        <div id="sidebar-menu">

            <ul class="metismenu" id="side-menu">

                <li class="menu-title">Navigation</li>

                <li>
                    <a href="{{ route('quran.dashboard') }}">
                        <i class="fe-grid"></i>
                        <span> Quran Dashboard </span>
                    </a>
                </li>

                <li>
                    <a href="javascript: void(0);">
                        <i class="mdi mdi-translate"></i>
                        <span>Languages</span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="{{ route('langs.index') }}">List of Languages</a></li>
                        <li><a href="{{ route('langs.create') }}">Add Language</a></li>
                    </ul>
                </li>

                <li>
                    <a href="javascript: void(0);">
                        <i class="mdi mdi-book-open-page-variant"></i>
                        <span>Chapters </span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="{{ route('chapts.index') }}">List of Chapter</a></li>
                        <li><a href="{{ route('chapts.create') }}">Add Chapter</a></li>
                    </ul>
                </li>

                <li>
                    <a href="javascript: void(0);">
                        <i class="fe-book"></i>
                        <span>Quran Surah</span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="{{ route('surahs.index') }}">List of Surah</a></li>
                        <li><a href="{{ route('surahs.create') }}">Add Surah</a></li>
                    </ul>
                </li>

                <li>
                    <a href="javascript: void(0);">
                        <i class="mdi mdi-sitemap"></i>
                        <span> Topics </span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="{{ route('topics.index') }}">List of Topics</a></li>
                        <li><a href="{{ route('topics.create') }}">Add Topic</a></li>
                    </ul>
                </li>

                <li>
                    <a href="javascript: void(0);">
                        <i class="fe-file-text"></i>
                        <span> Topic Details </span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="{{ route('topicdetails.index') }}">List of Topic Details</a></li>
                        <li><a href="{{ route('topicdetails.create') }}">Add Topic Details</a></li>
                    </ul>
                </li>

                {{-- <li>
                    <a href="javascript: void(0);">
                        <i class="mdi mdi-library-books"></i>
                        <span>Books</span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="{{ route('books.index') }}">List of Books</a></li>
                        <li><a href="{{ route('books.create') }}">Add Book</a></li>
                    </ul>
                </li> --}}

                <li>
                    <a href="javascript: void(0);">
                        <i class="mdi mdi-library"></i>
                        <span>Library</span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="{{ route('library.index') }}">All Library Items</a></li>
                        <li><a href="{{ route('library.create') }}">Add New Item</a></li>
                    </ul>
                </li>

                <li class="menu-title">System Monitoring</li>

                <li>
                    <a href="javascript: void(0);">
                        <i class="mdi mdi-shield-check"></i>
                        <span>Audit Trails</span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="{{ route('audits.index') }}">View Audit Logs</a></li>
                        <li><a href="{{ route('audits.export') }}">Export Audit Data</a></li>
                    </ul>
                </li>

                <li>
                    <a href="javascript: void(0);">
                        <i class="mdi mdi-account-clock"></i>
                        <span>User Activities</span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="{{ route('user-activities.index') }}">View Activities</a></li>
                        <li><a href="{{ route('user-activities.dashboard') }}">Activity Dashboard</a></li>
                        <li><a href="{{ route('user-activities.export-form') }}">Export Activity Data</a></li>
                    </ul>
                </li>

                <li>
                    <a href="javascript: void(0);">
                        <i class="mdi mdi-backup-restore"></i>
                        <span>Backup & Data</span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="{{ route('backups.index') }}"><i class="mdi mdi-folder-multiple"></i> Backup
                                Management</a></li>
                        <li><a href="javascript:void(0);" onclick="scrollToSection('create')"><i
                                    class="mdi mdi-plus-circle"></i> Create Backup</a></li>
                        <li><a href="javascript:void(0);" onclick="scrollToSection('export')"><i
                                    class="mdi mdi-export"></i> Export Data</a></li>
                        <li><a href="{{ route('backups.restore.form') }}"><i class="mdi mdi-restore"></i> Restore from
                                Backup</a></li>
                    </ul>
                </li>

                <!-- API Documentation & Testing -->
                <li class="has_sub">
                    <a href="javascript:void(0);" class="waves-effect">
                        <i class="mdi mdi-api"></i>
                        <span>API & Documentation</span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="{{ route('api.docs') }}"><i class="mdi mdi-book-open-page-variant"></i> API
                                Documentation</a></li>
                        <li><a href="{{ route('api.test') }}" target="_blank"><i class="mdi mdi-test-tube"></i> API
                                Tester</a></li>
                        <li><a href="{{ route('api.quick.reference') }}"><i class="mdi mdi-lightning-bolt"></i> Quick
                                Reference</a></li>
                        <li><a href="{{ route('api.postman.collection') }}"><i class="mdi mdi-download"></i> Postman
                                Collection</a></li>
                        <li><a href="{{ route('api.status') }}" target="_blank"><i class="mdi mdi-heart-pulse"></i> API
                                Status</a></li>
                        <li><a href="{{ route('api.overview') }}" target="_blank"><i class="mdi mdi-chart-line"></i>
                                API Overview</a></li>
                    </ul>
                </li>

                <!-- User & Rights Management -->
                @if (auth()->check() &&
                        auth()->user()->hasAnyPermission(['users.manage', 'roles.manage', 'permissions.manage']))
                    <li class="has_sub">
                        <a href="javascript:void(0);" class="waves-effect">
                            <i class="mdi mdi-account-multiple"></i>
                            <span>User Management</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="nav-second-level" aria-expanded="false">
                            @if (auth()->user()->hasPermission('users.manage'))
                                <li><a href="{{ route('users.index') }}"><i class="mdi mdi-account-multiple"></i>
                                        Users</a></li>
                            @endif
                            @if (auth()->user()->hasPermission('roles.manage'))
                                <li><a href="{{ route('roles.index') }}"><i class="mdi mdi-account-group"></i>
                                        Roles</a></li>
                            @endif
                            @if (auth()->user()->hasPermission('permissions.manage'))
                                <li><a href="{{ route('permissions.index') }}"><i class="mdi mdi-key"></i>
                                        Permissions</a></li>
                            @endif
                        </ul>
                    </li>
                @endif

            </ul>

        </div>
        <!-- End Sidebar -->

        <div class="clearfix"></div>

    </div>
    <!-- Sidebar -left -->

</div>
<!-- Left Sidebar End -->
