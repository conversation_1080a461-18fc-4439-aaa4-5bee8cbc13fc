<!-- ========== Left Sidebar Start ========== -->
<div class="left-side-menu">

    <div class="slimscroll-menu">

        <!--- Sidemenu -->
        <div id="sidebar-menu">

            <ul class="metismenu" id="side-menu">

                <li class="menu-title">Navigation</li>

                <li>
                    <a href="{{ route('quran.dashboard') }}">
                        <i class="fe-grid"></i>
                        <span> Quran Dashboard </span>
                    </a>
                </li>

                <li>
                    <a href="javascript: void(0);">
                        <i class="mdi mdi-translate"></i>
                        <span>Languages</span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="{{ route('langs.index') }}">List of Languages</a></li>
                        <li><a href="{{ route('langs.create') }}">Add Language</a></li>
                    </ul>
                </li>

                <li>
                    <a href="javascript: void(0);">
                        <i class="mdi mdi-book-open-page-variant"></i>
                        <span>Chapters </span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="{{ route('chapts.index') }}">List of Chapter</a></li>
                        <li><a href="{{ route('chapts.create') }}">Add Chapter</a></li>
                    </ul>
                </li>

                <li>
                    <a href="javascript: void(0);">
                        <i class="fe-book"></i>
                        <span>Quran Surah</span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="{{ route('surahs.index') }}">List of Surah</a></li>
                        <li><a href="{{ route('surahs.create') }}">Add Surah</a></li>
                    </ul>
                </li>

                <li>
                    <a href="javascript: void(0);">
                        <i class="mdi mdi-sitemap"></i>
                        <span> Topics </span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="{{ route('topics.index') }}">List of Topics</a></li>
                        <li><a href="{{ route('topics.create') }}">Add Topic</a></li>
                    </ul>
                </li>

                <li>
                    <a href="javascript: void(0);">
                        <i class="fe-file-text"></i>
                        <span> Topic Details </span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="{{ route('topicdetails.index') }}">List of Topic Details</a></li>
                        <li><a href="{{ route('topicdetails.create') }}">Add Topic Details</a></li>
                    </ul>
                </li>

                {{-- <li>
                    <a href="javascript: void(0);">
                        <i class="mdi mdi-library-books"></i>
                        <span>Books</span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="{{ route('books.index') }}">List of Books</a></li>
                        <li><a href="{{ route('books.create') }}">Add Book</a></li>
                    </ul>
                </li> --}}

                <li>
                    <a href="javascript: void(0);">
                        <i class="mdi mdi-library"></i>
                        <span>Library</span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="{{ route('library.index') }}">All Library Items</a></li>
                        <li><a href="{{ route('library.create') }}">Add New Item</a></li>
                    </ul>
                </li>

                <li class="menu-title">System Monitoring</li>

                <li>
                    <a href="javascript: void(0);">
                        <i class="mdi mdi-shield-check"></i>
                        <span>Audit Trails</span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="{{ route('audits.index') }}">View Audit Logs</a></li>
                        <li><a href="{{ route('audits.export') }}">Export Audit Data</a></li>
                    </ul>
                </li>

                <li>
                    <a href="javascript: void(0);">
                        <i class="mdi mdi-account-clock"></i>
                        <span>User Activities</span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="{{ route('user-activities.index') }}">View Activities</a></li>
                        <li><a href="{{ route('user-activities.dashboard') }}">Activity Dashboard</a></li>
                        <li><a href="{{ route('user-activities.export') }}">Export Activity Data</a></li>
                    </ul>
                </li>

                <li>
                    <a href="javascript: void(0);">
                        <i class="mdi mdi-backup-restore"></i>
                        <span>Backup & Data</span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="nav-second-level" aria-expanded="false">
                        <li><a href="{{ route('backups.index') }}">Backup Management</a></li>
                        <li><a href="javascript:void(0);" onclick="scrollToSection('create')">Create Backup</a></li>
                        <li><a href="javascript:void(0);" onclick="scrollToSection('export')">Export Data</a></li>
                    </ul>
                </li>

            </ul>

        </div>
        <!-- End Sidebar -->

        <div class="clearfix"></div>

    </div>
    <!-- Sidebar -left -->

</div>
<!-- Left Sidebar End -->
