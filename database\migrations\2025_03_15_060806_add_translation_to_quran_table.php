<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('quran', function (Blueprint $table) {
            $table->longText('translation_bn')->nullable()->after('ayah_text'); // Bengali
            $table->longText('translation_zh')->nullable()->after('translation_bn'); // Chinese
            $table->longText('translation_en')->nullable()->after('translation_zh'); // English
            $table->longText('translation_es')->nullable()->after('translation_en'); // Spanish
            $table->longText('translation_fr')->nullable()->after('translation_es'); // French
            $table->longText('translation_id')->nullable()->after('translation_fr'); // Indonesian
            $table->longText('translation_ru')->nullable()->after('translation_id'); // Russian
            $table->longText('translation_sv')->nullable()->after('translation_ru'); // Swedish
            $table->longText('translation_tr')->nullable()->after('translation_sv'); // Turkish
            $table->longText('translation_ur')->nullable()->after('translation_tr'); // Urdu
        });
    }

    public function down(): void
    {
        Schema::table('quran', function (Blueprint $table) {
            $table->dropColumn([
                'translation_bn',
                'translation_zh',
                'translation_en',
                'translation_es',
                'translation_fr',
                'translation_id',
                'translation_ru',
                'translation_sv',
                'translation_tr',
                'translation_ur'
            ]);
        });
    }
};
