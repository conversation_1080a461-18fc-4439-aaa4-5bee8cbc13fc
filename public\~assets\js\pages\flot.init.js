!function(d){"use strict";var o=function(){this.$body=d("body"),this.$realData=[]};o.prototype.createPlotGraph=function(o,t,a,e,r,l,i,s){d.plot(d(o),[{data:t,label:r[0],color:l[0]},{data:a,label:r[1],color:l[1]},{data:e,label:r[2],color:l[2]}],{series:{lines:{show:!0,fill:!0,lineWidth:2,fillColor:{colors:[{opacity:.3},{opacity:.3},{opacity:.3}]}},points:{show:!0},shadowSize:0},grid:{hoverable:!0,clickable:!0,borderColor:i,tickColor:"#f9f9f9",borderWidth:1,labelMargin:30,backgroundColor:s},legend:{position:"ne",margin:[0,-32],noColumns:0,labelBoxBorderColor:null,labelFormatter:function(o,t){return o+"&nbsp;&nbsp;"},width:30,height:2},yaxis:{axisLabel:"Daily Visits",tickColor:"#f5f5f5",font:{color:"#bdbdbd"}},xaxis:{axisLabel:"Last Days",tickColor:"#f5f5f5",font:{color:"#bdbdbd"}},tooltip:!0,tooltipOpts:{content:"%s: Value of %x is %y",shifts:{x:-60,y:25},defaultTheme:!1},splines:{show:!0,tension:.1,lineWidth:1}})},o.prototype.createPlotDotGraph=function(o,t,a,e,r,l,i){d.plot(d(o),[{data:t,label:e[0],color:r[0]},{data:a,label:e[1],color:r[1]}],{series:{lines:{show:!0,fill:!1,lineWidth:3,fillColor:{colors:[{opacity:.3},{opacity:.3}]}},curvedLines:{apply:!0,active:!0,monotonicFit:!0},splines:{show:!0,tension:.4,lineWidth:5,fill:.4}},grid:{hoverable:!0,clickable:!0,borderColor:l,tickColor:"#f9f9f9",borderWidth:1,labelMargin:10,backgroundColor:i},legend:{position:"ne",margin:[0,-32],noColumns:0,labelBoxBorderColor:null,labelFormatter:function(o,t){return o+"&nbsp;&nbsp;"},width:30,height:2},yaxis:{axisLabel:"Gold Price(USD)",tickColor:"#f5f5f5",font:{color:"#bdbdbd"}},xaxis:{axisLabel:"Numbers",tickColor:"#f5f5f5",font:{color:"#bdbdbd"}},tooltip:!1})},o.prototype.createPieGraph=function(o,t,a,e){var r=[{label:t[0],data:a[0]},{label:t[1],data:a[1]},{label:t[2],data:a[2]},{label:t[3],data:a[3]}],l={series:{pie:{show:!0,radius:1,label:{show:!0,radius:1,background:{opacity:.2}}}},legend:{show:!1},grid:{hoverable:!0,clickable:!0},colors:e,tooltip:!0,tooltipOpts:{content:"%s, %p.0%"}};d.plot(d(o),r,l)},o.prototype.randomData=function(){for(0<this.$realData.length&&(this.$realData=this.$realData.slice(1));this.$realData.length<300;){var o=(0<this.$realData.length?this.$realData[this.$realData.length-1]:50)+10*Math.random()-5;o<0?o=0:100<o&&(o=100),this.$realData.push(o)}for(var t=[],a=0;a<this.$realData.length;++a)t.push([a,this.$realData[a]]);return t},o.prototype.createRealTimeGraph=function(o,t,a){return d.plot(o,[t],{colors:a,series:{grow:{active:!1},shadowSize:0,lines:{show:!0,fill:!0,lineWidth:2,steps:!1}},grid:{show:!0,aboveData:!1,color:"#dcdcdc",labelMargin:15,axisMargin:0,borderWidth:0,borderColor:null,minBorderMargin:5,clickable:!0,hoverable:!0,autoHighlight:!1,mouseActiveRadius:20},tooltip:!0,tooltipOpts:{content:"Value is : %y.0%",shifts:{x:-30,y:-50}},yaxis:{axisLabel:"Response Time (ms)",min:0,max:100,tickColor:"#f5f5f5",color:"rgba(0,0,0,0.1)"},xaxis:{axisLabel:"Point Value (1000)",show:!0,tickColor:"#f5f5f5"}})},o.prototype.createDonutGraph=function(o,t,a,e){var r=[{label:t[0],data:a[0]},{label:t[1],data:a[1]},{label:t[2],data:a[2]},{label:t[3],data:a[3]}],l={series:{pie:{show:!0,innerRadius:.7}},legend:{show:!0,labelFormatter:function(o,t){return'<div style="font-size:14px;">&nbsp;'+o+"</div>"},labelBoxBorderColor:null,margin:50,width:20},grid:{hoverable:!0,clickable:!0},colors:e,tooltip:!0,tooltipOpts:{content:"%s, %p.0%"}};d.plot(d(o),r,l)},o.prototype.createStackBarGraph=function(o,t,a,e){var r={bars:{show:!0,barWidth:.2,fill:1},grid:{show:!0,aboveData:!1,labelMargin:5,axisMargin:0,borderWidth:1,minBorderMargin:5,clickable:!0,hoverable:!0,autoHighlight:!1,mouseActiveRadius:20,borderColor:"#f5f5f5"},series:{stack:0},legend:{position:"ne",margin:[0,-32],noColumns:0,labelBoxBorderColor:null,labelFormatter:function(o,t){return o+"&nbsp;&nbsp;"},width:30,height:2},yaxis:t.y,xaxis:t.x,colors:a,tooltip:!0,tooltipOpts:{content:"%s : %y.0",shifts:{x:-30,y:-50}}};d.plot(d(o),e,r)},o.prototype.createLineGraph=function(o,t,a,e){var r={series:{lines:{show:!0},points:{show:!0}},legend:{position:"ne",margin:[0,-32],noColumns:0,labelBoxBorderColor:null,labelFormatter:function(o,t){return o+"&nbsp;&nbsp;"},width:30,height:2},yaxis:t.y,xaxis:t.x,colors:a,grid:{hoverable:!0,borderColor:"#f5f5f5",borderWidth:1,backgroundColor:"#fff"},tooltip:!0,tooltipOpts:{content:"%s : %y.0",shifts:{x:-30,y:-50}}};return d.plot(d(o),e,r)},o.prototype.createCombineGraph=function(o,t,a,e){var r=[{label:a[0],data:e[0],lines:{show:!0,fill:!0},points:{show:!0}},{label:a[1],data:e[1],lines:{show:!0},points:{show:!0}},{label:a[2],data:e[2],bars:{show:!0}}],l={series:{shadowSize:0},grid:{hoverable:!0,clickable:!0,tickColor:"#f9f9f9",borderWidth:1,borderColor:"#eeeeee"},colors:["#9368f3","#3c86d8","#3ec396"],tooltip:!0,tooltipOpts:{defaultTheme:!1},legend:{position:"ne",margin:[0,-32],noColumns:0,labelBoxBorderColor:null,labelFormatter:function(o,t){return o+"&nbsp;&nbsp;"},width:30,height:2},yaxis:{axisLabel:"Point Value (1000)",tickColor:"#f5f5f5",font:{color:"#bdbdbd"}},xaxis:{axisLabel:"Daily Hours",ticks:t,tickColor:"#f5f5f5",font:{color:"#bdbdbd"}}};d.plot(d(o),r,l)},o.prototype.init=function(){this.createPlotGraph("#website-stats",[[0,13],[1,13],[2,14],[3,17],[4,13],[5,10],[6,12],[7,13],[8,12],[9,20],[10,18],[11,16],[12,14]],[[0,8],[1,10],[2,12],[3,14],[4,11],[5,7],[6,9],[7,10],[8,9],[9,17],[10,15],[11,13],[12,11]],[[0,3],[1,6],[2,8],[3,10],[4,7],[5,3],[6,5],[7,7],[8,6],[9,14],[10,12],[11,10],[12,8]],["Google","Yahoo","Facebook"],["#9368f3","#3c86d8","#3ec396"],"#f5f5f5","#fff");this.createPlotDotGraph("#website-stats1",[[0,2],[1,4],[2,7],[3,9],[4,6],[5,3],[6,10],[7,8],[8,5],[9,14],[10,10],[11,10],[12,8]],[[0,1],[1,3],[2,6],[3,7],[4,4],[5,2],[6,8],[7,6],[8,4],[9,10],[10,8],[11,14],[12,5]],["Visits","Page views"],["#9368f3","#3c86d8"],"#f5f5f5","#fff");this.createPieGraph("#pie-chart #pie-chart-container",["Mobile Phones","iPhone 7","iMac","Macbook"],[20,30,15,32],["#5d6dc3","#3ec396","#f36270","#313a46"]);var t=this.createRealTimeGraph("#flotRealTime",this.randomData(),["#3c86d8"]);t.draw();var a=this;!function o(){t.setData([a.randomData()]),t.draw(),setTimeout(o,(d("html").hasClass("mobile-device"),500))}();this.createDonutGraph("#donut-chart #donut-chart-container",["Mobile Phones","iPhone 7","iMac","Macbook"],[35,20,10,20],["#9368f3","#3c86d8","#3ec396","#4fbde9"]);var o=[[[0,201],[1,520],[2,337],[3,261],[4,157],[5,95],[6,200],[7,250],[8,320],[9,500],[10,152],[11,214],[12,364],[13,449],[14,558],[15,282],[16,379],[17,429],[18,518],[19,470],[20,330],[21,245],[22,358],[23,74]],[[0,311],[1,630],[2,447],[3,371],[4,267],[5,205],[6,310],[7,360],[8,430],[9,610],[10,262],[11,324],[12,474],[13,559],[14,668],[15,392],[16,489],[17,539],[18,628],[19,580],[20,440],[21,355],[22,468],[23,184]],[[23,727],[22,128],[21,110],[20,92],[19,172],[18,63],[17,150],[16,592],[15,12],[14,246],[13,52],[12,149],[11,123],[10,2],[9,325],[8,10],[7,15],[6,89],[5,65],[4,77],[3,600],[2,200],[1,385],[0,200]]];this.createCombineGraph("#combine-chart #combine-chart-container",[[0,"22h"],[1,""],[2,"00h"],[3,""],[4,"02h"],[5,""],[6,"04h"],[7,""],[8,"06h"],[9,""],[10,"08h"],[11,""],[12,"10h"],[13,""],[14,"12h"],[15,""],[16,"14h"],[17,""],[18,"16h"],[19,""],[20,"18h"],[21,""],[22,"20h"],[23,""]],["Last 24 Hours","Last 48 Hours","Difference"],o);for(var e=[],r=0;r<=10;r+=1)e.push([r,parseInt(30*Math.random())]);var l=[];for(r=0;r<=10;r+=1)l.push([r,parseInt(30*Math.random())]);var i=[];for(r=0;r<=10;r+=1)i.push([r,parseInt(30*Math.random())]);var s=new Array;s.push({label:"Series One",data:e,bars:{order:1}}),s.push({label:"Series Two",data:l,bars:{order:2}}),s.push({label:"Series Three",data:i,bars:{order:3}}),this.createStackBarGraph("#ordered-bars-chart",{y:{axisLabel:"Sales Value (USD)",tickColor:"#f5f5f5",font:{color:"#bdbdbd"}},x:{axisLabel:"Last 10 Days",tickColor:"#f5f5f5",font:{color:"#bdbdbd"}}},["#3c86d8","#f9bc0b","#ebeff2"],s);var n=[],h=[];for(r=0;r<12;r+=.2)n.push([r,Math.sin(r+0)]),h.push([r,Math.cos(r+0)]);var c=[{data:n,label:"Google"},{data:h,label:"Yahoo"}];this.createLineGraph("#line-chart-alt",{y:{min:-1.2,max:1.2,tickColor:"#f5f5f5",font:{color:"#bdbdbd"}},x:{tickColor:"#f5f5f5",font:{color:"#bdbdbd"}}},["#9368f3","#f9bc0b"],c)},d.FlotChart=new o,d.FlotChart.Constructor=o}(window.jQuery),function(o){"use strict";window.jQuery.FlotChart.init()}();