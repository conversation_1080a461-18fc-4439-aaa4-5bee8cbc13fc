<?php

namespace App\Http\Controllers;

use App\Services\BackupService;
use App\Services\ActivityLogger;
use App\Models\Audit;
use App\Models\UserActivity;
use App\Models\LibraryItem;
use App\Models\Book;
use App\Models\Surah;
use App\Models\Chapter;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

class BackupController extends Controller
{
    protected $backupService;

    public function __construct(BackupService $backupService)
    {
        $this->backupService = $backupService;
    }

    /**
     * Display backup management page
     */
    public function index()
    {
        $backups = $this->backupService->getAvailableBackups();

        // Get backup statistics
        $stats = [
            'total_backups' => count($backups),
            'total_size' => array_sum(array_column($backups, 'size')),
            'latest_backup' => $backups[0] ?? null,
            'backup_types' => array_count_values(array_column($backups, 'type')),
        ];

        return view('backups.index', compact('backups', 'stats'));
    }

    /**
     * Create full system backup
     */
    public function createFullBackup()
    {
        try {
            $result = $this->backupService->createFullBackup();

            if ($result['success']) {
                return redirect()->route('backups.index')
                    ->with('success', "Full backup created successfully: {$result['backup_name']}");
            } else {
                return redirect()->route('backups.index')
                    ->with('error', "Backup failed: {$result['error']}");
            }
        } catch (\Exception $e) {
            return redirect()->route('backups.index')
                ->with('error', "Backup failed: {$e->getMessage()}");
        }
    }

    /**
     * Create database backup
     */
    public function createDatabaseBackup()
    {
        try {
            $filePath = $this->backupService->createDatabaseBackup();
            $filename = basename($filePath);

            return redirect()->route('backups.index')
                ->with('success', "Database backup created successfully: {$filename}");
        } catch (\Exception $e) {
            return redirect()->route('backups.index')
                ->with('error', "Database backup failed: {$e->getMessage()}");
        }
    }

    /**
     * Create files backup
     */
    public function createFilesBackup()
    {
        try {
            $filePath = $this->backupService->createFilesBackup();
            $filename = basename($filePath);

            return redirect()->route('backups.index')
                ->with('success', "Files backup created successfully: {$filename}");
        } catch (\Exception $e) {
            return redirect()->route('backups.index')
                ->with('error', "Files backup failed: {$e->getMessage()}");
        }
    }

    /**
     * Create data export
     */
    public function createDataExport(Request $request)
    {
        $request->validate([
            'export_type' => 'required|in:all,audit_logs,user_activities,library_items,books,surahs,chapters,users',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
        ]);

        try {
            $exportType = $request->export_type;
            $dateFrom = $request->date_from;
            $dateTo = $request->date_to;

            if ($exportType === 'all') {
                $filePath = $this->exportAllData($dateFrom, $dateTo);
            } else {
                $filePath = $this->exportSpecificData($exportType, $dateFrom, $dateTo);
            }

            $filename = basename($filePath);

            return redirect()->route('backups.index')
                ->with('success', "Data export created successfully: {$filename}");
        } catch (\Exception $e) {
            return redirect()->route('backups.index')
                ->with('error', "Data export failed: {$e->getMessage()}");
        }
    }

    /**
     * Download backup file
     */
    public function download(Request $request)
    {
        $filename = $request->get('file');
        $filePath = storage_path("app/backups/{$filename}");

        if (!File::exists($filePath)) {
            return redirect()->route('backups.index')
                ->with('error', 'Backup file not found.');
        }

        // Log the download activity
        ActivityLogger::log('backup_download', "Downloaded backup file: {$filename}", null, [
            'filename' => $filename,
            'file_size' => File::size($filePath),
            'backup_type' => $this->backupService->getBackupType($filename),
        ]);

        return Response::download($filePath, $filename, [
            'Content-Type' => 'application/octet-stream',
        ]);
    }

    /**
     * Delete backup file
     */
    public function delete(Request $request)
    {
        $filename = $request->get('file');
        $filePath = storage_path("app/backups/{$filename}");

        if (!File::exists($filePath)) {
            return redirect()->route('backups.index')
                ->with('error', 'Backup file not found.');
        }

        File::delete($filePath);

        // Log the deletion activity
        ActivityLogger::log('backup_delete', "Deleted backup file: {$filename}");

        return redirect()->route('backups.index')
            ->with('success', "Backup file deleted successfully: {$filename}");
    }

    /**
     * Show restore form
     */
    public function restoreForm()
    {
        $backups = $this->backupService->getAvailableBackups();

        // Filter out data exports as they can't be restored
        $restorableBackups = array_filter($backups, function ($backup) {
            return !in_array($backup['type'], ['data_export', 'unknown']);
        });

        return view('backups.restore', compact('restorableBackups'));
    }

    /**
     * Restore from backup
     */
    public function restore(Request $request)
    {
        $request->validate([
            'backup_file' => 'required|string',
            'restore_options' => 'array',
        ]);

        $filename = $request->input('backup_file');
        $options = $request->input('restore_options', []);

        try {
            $result = $this->backupService->restoreFromBackup($filename, $options);

            if ($result['success']) {
                return redirect()->route('backups.index')
                    ->with('success', $result['message'] . ' from ' . $result['restored_from']);
            } else {
                return redirect()->back()
                    ->with('error', $result['error'])
                    ->withInput();
            }
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Restore failed: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Get backup info for restore preview
     */
    public function getBackupInfo(Request $request)
    {
        $filename = $request->input('filename');

        if (!$filename) {
            return response()->json(['error' => 'Filename is required'], 400);
        }

        $filePath = storage_path("app/backups/{$filename}");

        if (!File::exists($filePath)) {
            return response()->json(['error' => 'Backup file not found'], 404);
        }

        $backupType = $this->backupService->getBackupType($filename);
        $fileSize = File::size($filePath);
        $createdAt = File::lastModified($filePath);

        $info = [
            'filename' => $filename,
            'type' => $backupType,
            'size' => $this->formatBytes($fileSize),
            'size_bytes' => $fileSize,
            'created_at' => date('Y-m-d H:i:s', $createdAt),
            'created_ago' => $this->timeAgo($createdAt),
        ];

        // Add type-specific information
        switch ($backupType) {
            case 'database':
                $info['description'] = 'Database backup containing all tables and data';
                $info['components'] = ['Database'];
                break;
            case 'full_laravel':
                $info['description'] = 'Complete Laravel project backup';
                $info['components'] = ['Database', 'Application Code', 'Storage Files', 'Configuration', 'Public Assets'];
                break;
            case 'full':
                $info['description'] = 'Full system backup';
                $info['components'] = ['Database', 'Files', 'Configuration'];
                break;
            case 'files':
                $info['description'] = 'Files and storage backup';
                $info['components'] = ['Storage Files', 'Uploaded Content'];
                break;
            case 'config':
                $info['description'] = 'Configuration files backup';
                $info['components'] = ['Environment Files', 'Configuration', 'Dependencies'];
                break;
        }

        return response()->json($info);
    }

    /**
     * Cleanup old backups
     */
    public function cleanup(Request $request)
    {
        $request->validate([
            'keep_days' => 'required|integer|min:1|max:365'
        ]);

        try {
            $deletedCount = $this->backupService->cleanupOldBackups($request->keep_days);

            // Log the cleanup activity
            ActivityLogger::log('backup_cleanup', "Cleaned up {$deletedCount} old backup files", null, [
                'deleted_count' => $deletedCount,
                'keep_days' => $request->keep_days,
            ]);

            return redirect()->route('backups.index')
                ->with('success', "Cleaned up {$deletedCount} old backup files.");
        } catch (\Exception $e) {
            return redirect()->route('backups.index')
                ->with('error', "Cleanup failed: {$e->getMessage()}");
        }
    }

    /**
     * Export all data
     */
    private function exportAllData($dateFrom = null, $dateTo = null): string
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $filename = "complete_data_export_{$timestamp}.json";
        $filePath = storage_path("app/backups/{$filename}");

        // Ensure directory exists
        File::makeDirectory(dirname($filePath), 0755, true);

        $exportData = [];

        // Export all main models
        $models = [
            'users' => User::class,
            'library_items' => LibraryItem::class,
            'books' => Book::class,
            'surahs' => Surah::class,
            'chapters' => Chapter::class,
        ];

        foreach ($models as $key => $modelClass) {
            $query = $modelClass::query();

            if ($dateFrom) {
                $query->where('created_at', '>=', $dateFrom);
            }

            if ($dateTo) {
                $query->where('created_at', '<=', $dateTo);
            }

            $exportData[$key] = $query->get()->toArray();
        }

        // Export audit logs
        $auditQuery = Audit::query();
        if ($dateFrom) $auditQuery->where('created_at', '>=', $dateFrom);
        if ($dateTo) $auditQuery->where('created_at', '<=', $dateTo);
        $exportData['audit_logs'] = $auditQuery->get()->toArray();

        // Export user activities
        $activityQuery = UserActivity::query();
        if ($dateFrom) $activityQuery->where('created_at', '>=', $dateFrom);
        if ($dateTo) $activityQuery->where('created_at', '<=', $dateTo);
        $exportData['user_activities'] = $activityQuery->get()->toArray();

        // Add metadata
        $exportData['metadata'] = [
            'export_date' => now()->toISOString(),
            'export_type' => 'complete',
            'date_range' => [
                'from' => $dateFrom,
                'to' => $dateTo,
            ],
            'laravel_version' => app()->version(),
            'php_version' => PHP_VERSION,
        ];

        File::put($filePath, json_encode($exportData, JSON_PRETTY_PRINT));

        // Log the export
        ActivityLogger::logExport('complete_data', [
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'file_size' => File::size($filePath),
        ]);

        return $filePath;
    }

    /**
     * Export specific data type
     */
    private function exportSpecificData(string $type, $dateFrom = null, $dateTo = null): string
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $filename = "{$type}_export_{$timestamp}.json";
        $filePath = storage_path("app/backups/{$filename}");

        // Ensure directory exists
        File::makeDirectory(dirname($filePath), 0755, true);

        $exportData = [];

        switch ($type) {
            case 'audit_logs':
                $query = Audit::with(['user', 'auditable']);
                break;
            case 'user_activities':
                $query = UserActivity::with(['user', 'subject']);
                break;
            case 'library_items':
                $query = LibraryItem::query();
                break;
            case 'books':
                $query = Book::query();
                break;
            case 'surahs':
                $query = Surah::query();
                break;
            case 'chapters':
                $query = Chapter::query();
                break;
            case 'users':
                $query = User::query();
                break;
            default:
                throw new \Exception("Unknown export type: {$type}");
        }

        // Apply date filters
        if ($dateFrom) {
            $query->where('created_at', '>=', $dateFrom);
        }

        if ($dateTo) {
            $query->where('created_at', '<=', $dateTo);
        }

        $exportData['data'] = $query->get()->toArray();
        $exportData['metadata'] = [
            'export_date' => now()->toISOString(),
            'export_type' => $type,
            'date_range' => [
                'from' => $dateFrom,
                'to' => $dateTo,
            ],
            'record_count' => count($exportData['data']),
        ];

        File::put($filePath, json_encode($exportData, JSON_PRETTY_PRINT));

        // Log the export
        ActivityLogger::logExport($type, [
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'record_count' => count($exportData['data']),
            'file_size' => File::size($filePath),
        ]);

        return $filePath;
    }
}
