<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\AuditableTrait;
use App\Contracts\AuditableInterface;

class Surah extends Model implements AuditableInterface
{
    use HasFactory, AuditableTrait;

    protected $fillable = [
        'surahname',
        'surahcode',
        'chapter_id',
        'isactive'
    ];
}
