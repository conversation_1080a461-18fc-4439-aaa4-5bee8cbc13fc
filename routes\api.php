<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\QuranController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// quran fetch apiRoute::get('/quran', [QuranController::class, 'index']);
Route::get('/quran/surah/{number}', [QuranController::class, 'getSurah']);
Route::get('/quran/surah/{surah}/ayah/{ayah}', [QuranController::class, 'getAyah']);
// general search
Route::get('/quran/search', [QuranController::class, 'search']);


// with english tafsir
Route::get('/quran/tafsir/{surah}/{ayah}', [QuranController::class, 'getTafsir']);

// Topics API routes
Route::get('/topics', [App\Http\Controllers\TopicsController::class, 'apiIndex']);
Route::get('/topics/search', [App\Http\Controllers\TopicsController::class, 'apiSearch']);

// Chapters API routes
Route::get('/chapters', [App\Http\Controllers\ChapterController::class, 'apiIndex']);
Route::get('/chapters/search', [App\Http\Controllers\ChapterController::class, 'apiSearch']);

// with language option
Route::get('/quran/ayah/{surah}/{ayah}/{language?}', [QuranController::class, 'getAyahLng']);
Route::get('/quran/tafsir/{surah}/{ayah}/{language?}', [QuranController::class, 'getTafsirLng']);

// Get all translations for a specific ayah
Route::get('/quran/translations/{surah}/{ayah}', [QuranController::class, 'getTranslations']);

// Get translation in a specific language
Route::get('/quran/translation/{surah}/{ayah}/{language}', [QuranController::class, 'getTranslation']);

// List all available languages for translations
Route::get('/quran/languages', [QuranController::class, 'availableLanguages']);
