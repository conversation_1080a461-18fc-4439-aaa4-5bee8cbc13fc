<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Artisan;
use ZipArchive;
use Carbon\Carbon;

class BackupService
{
    protected $backupDisk = 'local';
    protected $backupPath = 'backups';

    /**
     * Create a full Laravel project backup
     */
    public function createFullBackup(): array
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $backupName = "full_laravel_backup_{$timestamp}";

        try {
            // Create backup directory
            $backupDir = storage_path("app/{$this->backupPath}/{$backupName}");
            File::makeDirectory($backupDir, 0755, true);

            $results = [];

            // 1. Database backup
            $dbBackup = $this->createDatabaseBackup($backupName);
            $results['database'] = $dbBackup;

            // 2. Application code backup (app, resources, routes, etc.)
            $appBackup = $this->createApplicationBackup($backupName);
            $results['application'] = $appBackup;

            // 3. Storage and uploaded files backup
            $storageBackup = $this->createStorageBackup($backupName);
            $results['storage'] = $storageBackup;

            // 4. Configuration and environment backup
            $configBackup = $this->createConfigurationBackup($backupName);
            $results['configuration'] = $configBackup;

            // 5. Public assets backup
            $publicBackup = $this->createPublicAssetsBackup($backupName);
            $results['public_assets'] = $publicBackup;

            // 6. Vendor dependencies info (composer files)
            $vendorBackup = $this->createVendorInfoBackup($backupName);
            $results['vendor_info'] = $vendorBackup;

            // 7. Create final ZIP archive
            $zipFile = $this->createFullProjectZipArchive($backupName);
            $results['archive'] = $zipFile;

            // 8. Clean up temporary files
            File::deleteDirectory($backupDir);

            // Log the backup activity
            ActivityLogger::logBackup('full_laravel', basename($zipFile), [
                'size' => File::size($zipFile),
                'components' => array_keys($results),
                'backup_type' => 'complete_laravel_project',
            ]);

            return [
                'success' => true,
                'backup_name' => $backupName,
                'file_path' => $zipFile,
                'file_size' => File::size($zipFile),
                'components' => $results,
                'backup_type' => 'complete_laravel_project',
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Create database backup
     */
    public function createDatabaseBackup(string $backupName = null): string
    {
        $isStandalone = $backupName === null;
        $backupName = $backupName ?? 'database_' . now()->format('Y-m-d_H-i-s');
        $filename = $isStandalone ? "{$backupName}.sql" : "{$backupName}_database.sql";

        // For standalone backups, save directly to backup directory
        // For full backups, save to subdirectory
        if ($isStandalone) {
            $filePath = storage_path("app/{$this->backupPath}/{$filename}");
        } else {
            $filePath = storage_path("app/{$this->backupPath}/{$backupName}/{$filename}");
        }

        // Ensure directory exists
        $directory = dirname($filePath);
        if (!File::exists($directory)) {
            File::makeDirectory($directory, 0755, true);
        }

        $database = config('database.default');
        $config = config("database.connections.{$database}");

        if ($database === 'mysql') {
            $this->createMySQLBackup($config, $filePath);
        } elseif ($database === 'sqlite') {
            $this->createSQLiteBackup($config, $filePath);
        } else {
            throw new \Exception("Unsupported database type: {$database}");
        }

        // Log the backup
        ActivityLogger::logBackup('database', $filename, [
            'database_type' => $database,
            'file_size' => File::size($filePath),
        ]);

        return $filePath;
    }

    /**
     * Create files backup
     */
    public function createFilesBackup(string $backupName = null): string
    {
        $isStandalone = $backupName === null;
        $backupName = $backupName ?? 'files_' . now()->format('Y-m-d_H-i-s');
        $filename = $isStandalone ? "{$backupName}.zip" : "{$backupName}_files.zip";

        // For standalone backups, save directly to backup directory
        // For full backups, save to subdirectory
        if ($isStandalone) {
            $filePath = storage_path("app/{$this->backupPath}/{$filename}");
        } else {
            $filePath = storage_path("app/{$this->backupPath}/{$backupName}/{$filename}");
        }

        // Ensure directory exists
        $directory = dirname($filePath);
        if (!File::exists($directory)) {
            File::makeDirectory($directory, 0755, true);
        }

        $zip = new ZipArchive();
        if ($zip->open($filePath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== TRUE) {
            throw new \Exception("Cannot create files backup archive");
        }

        // Add storage files
        $this->addDirectoryToZip($zip, storage_path('app/public'), 'storage/');

        // Add uploaded files if they exist in public storage
        if (File::exists(public_path('storage'))) {
            $this->addDirectoryToZip($zip, public_path('storage'), 'public_storage/');
        }

        $zip->close();

        // Log the backup
        ActivityLogger::logBackup('files', $filename, [
            'file_size' => File::size($filePath),
        ]);

        return $filePath;
    }

    /**
     * Create configuration backup
     */
    public function createConfigBackup(string $backupName = null): string
    {
        $isStandalone = $backupName === null;
        $backupName = $backupName ?? 'config_' . now()->format('Y-m-d_H-i-s');
        $filename = $isStandalone ? "{$backupName}.zip" : "{$backupName}_config.zip";

        // For standalone backups, save directly to backup directory
        // For full backups, save to subdirectory
        if ($isStandalone) {
            $filePath = storage_path("app/{$this->backupPath}/{$filename}");
        } else {
            $filePath = storage_path("app/{$this->backupPath}/{$backupName}/{$filename}");
        }

        // Ensure directory exists
        $directory = dirname($filePath);
        if (!File::exists($directory)) {
            File::makeDirectory($directory, 0755, true);
        }

        $zip = new ZipArchive();
        if ($zip->open($filePath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== TRUE) {
            throw new \Exception("Cannot create config backup archive");
        }

        // Add configuration files
        $configDirs = [
            base_path('config') => 'config/',
            base_path('.env') => '.env',
            base_path('composer.json') => 'composer.json',
            base_path('composer.lock') => 'composer.lock',
            base_path('package.json') => 'package.json',
        ];

        foreach ($configDirs as $source => $destination) {
            if (File::exists($source)) {
                if (File::isDirectory($source)) {
                    $this->addDirectoryToZip($zip, $source, $destination);
                } else {
                    $zip->addFile($source, $destination);
                }
            }
        }

        $zip->close();

        // Log the backup
        ActivityLogger::logBackup('config', $filename, [
            'file_size' => File::size($filePath),
        ]);

        return $filePath;
    }

    /**
     * Create data export (specific tables/models)
     */
    public function createDataExport(array $tables = [], array $models = []): string
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $filename = "data_export_{$timestamp}.json";
        $filePath = storage_path("app/{$this->backupPath}/{$filename}");

        // Ensure directory exists
        $directory = dirname($filePath);
        if (!File::exists($directory)) {
            File::makeDirectory($directory, 0755, true);
        }

        $exportData = [];

        // Export specific tables
        foreach ($tables as $table) {
            if (DB::getSchemaBuilder()->hasTable($table)) {
                $exportData['tables'][$table] = DB::table($table)->get()->toArray();
            }
        }

        // Export specific models
        foreach ($models as $modelClass) {
            if (class_exists($modelClass)) {
                $modelName = class_basename($modelClass);
                $exportData['models'][$modelName] = $modelClass::all()->toArray();
            }
        }

        // Add metadata
        $exportData['metadata'] = [
            'export_date' => now()->toISOString(),
            'laravel_version' => app()->version(),
            'php_version' => PHP_VERSION,
            'database_type' => config('database.default'),
        ];

        File::put($filePath, json_encode($exportData, JSON_PRETTY_PRINT));

        // Log the export
        ActivityLogger::logExport('data', [
            'tables' => $tables,
            'models' => $models,
            'file_size' => File::size($filePath),
        ]);

        return $filePath;
    }

    /**
     * Create application code backup (app, resources, routes, etc.)
     */
    public function createApplicationBackup(string $backupName): string
    {
        $filename = "{$backupName}_application.zip";
        $filePath = storage_path("app/{$this->backupPath}/{$backupName}/{$filename}");

        // Ensure directory exists
        $directory = dirname($filePath);
        if (!File::exists($directory)) {
            File::makeDirectory($directory, 0755, true);
        }

        $zip = new ZipArchive();
        if ($zip->open($filePath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== TRUE) {
            throw new \Exception("Cannot create application backup archive");
        }

        // Add application directories
        $appDirs = [
            base_path('app') => 'app/',
            base_path('resources') => 'resources/',
            base_path('routes') => 'routes/',
            base_path('database/migrations') => 'database/migrations/',
            base_path('database/seeders') => 'database/seeders/',
            base_path('database/factories') => 'database/factories/',
        ];

        foreach ($appDirs as $sourceDir => $zipPath) {
            if (File::exists($sourceDir)) {
                $this->addDirectoryToZip($zip, $sourceDir, $zipPath);
            }
        }

        // Add important root files
        $rootFiles = [
            base_path('artisan') => 'artisan',
            base_path('server.php') => 'server.php',
            base_path('webpack.mix.js') => 'webpack.mix.js',
            base_path('vite.config.js') => 'vite.config.js',
            base_path('.htaccess') => '.htaccess',
            base_path('web.config') => 'web.config',
        ];

        foreach ($rootFiles as $sourceFile => $zipPath) {
            if (File::exists($sourceFile)) {
                $zip->addFile($sourceFile, $zipPath);
            }
        }

        $zip->close();

        // Log the backup
        ActivityLogger::logBackup('application', $filename, [
            'file_size' => File::size($filePath),
        ]);

        return $filePath;
    }

    /**
     * Create storage backup (all storage files)
     */
    public function createStorageBackup(string $backupName): string
    {
        $filename = "{$backupName}_storage.zip";
        $filePath = storage_path("app/{$this->backupPath}/{$backupName}/{$filename}");

        // Ensure directory exists
        $directory = dirname($filePath);
        if (!File::exists($directory)) {
            File::makeDirectory($directory, 0755, true);
        }

        $zip = new ZipArchive();
        if ($zip->open($filePath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== TRUE) {
            throw new \Exception("Cannot create storage backup archive");
        }

        // Add storage directories (excluding backups to avoid recursion)
        $storageDirs = [
            storage_path('app/public') => 'app/public/',
            storage_path('logs') => 'logs/',
            storage_path('framework/cache') => 'framework/cache/',
            storage_path('framework/sessions') => 'framework/sessions/',
            storage_path('framework/views') => 'framework/views/',
        ];

        foreach ($storageDirs as $sourceDir => $zipPath) {
            if (File::exists($sourceDir)) {
                $this->addDirectoryToZip($zip, $sourceDir, $zipPath);
            }
        }

        // Add public storage link if it exists
        if (File::exists(public_path('storage'))) {
            $this->addDirectoryToZip($zip, public_path('storage'), 'public_storage/');
        }

        $zip->close();

        // Log the backup
        ActivityLogger::logBackup('storage', $filename, [
            'file_size' => File::size($filePath),
        ]);

        return $filePath;
    }

    /**
     * Create configuration backup (enhanced)
     */
    public function createConfigurationBackup(string $backupName): string
    {
        $filename = "{$backupName}_configuration.zip";
        $filePath = storage_path("app/{$this->backupPath}/{$backupName}/{$filename}");

        // Ensure directory exists
        $directory = dirname($filePath);
        if (!File::exists($directory)) {
            File::makeDirectory($directory, 0755, true);
        }

        $zip = new ZipArchive();
        if ($zip->open($filePath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== TRUE) {
            throw new \Exception("Cannot create configuration backup archive");
        }

        // Add configuration directories and files
        $configItems = [
            base_path('config') => 'config/',
            base_path('bootstrap') => 'bootstrap/',
            base_path('.env') => '.env',
            base_path('.env.example') => '.env.example',
            base_path('composer.json') => 'composer.json',
            base_path('composer.lock') => 'composer.lock',
            base_path('package.json') => 'package.json',
            base_path('package-lock.json') => 'package-lock.json',
            base_path('yarn.lock') => 'yarn.lock',
            base_path('phpunit.xml') => 'phpunit.xml',
            base_path('README.md') => 'README.md',
            base_path('.gitignore') => '.gitignore',
            base_path('.gitattributes') => '.gitattributes',
        ];

        foreach ($configItems as $source => $destination) {
            if (File::exists($source)) {
                if (File::isDirectory($source)) {
                    $this->addDirectoryToZip($zip, $source, $destination);
                } else {
                    $zip->addFile($source, $destination);
                }
            }
        }

        $zip->close();

        // Log the backup
        ActivityLogger::logBackup('configuration', $filename, [
            'file_size' => File::size($filePath),
        ]);

        return $filePath;
    }

    /**
     * Create public assets backup
     */
    public function createPublicAssetsBackup(string $backupName): string
    {
        $filename = "{$backupName}_public_assets.zip";
        $filePath = storage_path("app/{$this->backupPath}/{$backupName}/{$filename}");

        // Ensure directory exists
        $directory = dirname($filePath);
        if (!File::exists($directory)) {
            File::makeDirectory($directory, 0755, true);
        }

        $zip = new ZipArchive();
        if ($zip->open($filePath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== TRUE) {
            throw new \Exception("Cannot create public assets backup archive");
        }

        // Add public directory contents (excluding storage symlink to avoid duplication)
        $publicDirs = [
            public_path('css') => 'css/',
            public_path('js') => 'js/',
            public_path('images') => 'images/',
            public_path('assets') => 'assets/',
            public_path('build') => 'build/',
            public_path('vendor') => 'vendor/',
        ];

        foreach ($publicDirs as $sourceDir => $zipPath) {
            if (File::exists($sourceDir)) {
                $this->addDirectoryToZip($zip, $sourceDir, $zipPath);
            }
        }

        // Add important public files
        $publicFiles = [
            public_path('index.php') => 'index.php',
            public_path('.htaccess') => '.htaccess',
            public_path('web.config') => 'web.config',
            public_path('robots.txt') => 'robots.txt',
            public_path('favicon.ico') => 'favicon.ico',
            public_path('mix-manifest.json') => 'mix-manifest.json',
        ];

        foreach ($publicFiles as $sourceFile => $zipPath) {
            if (File::exists($sourceFile)) {
                $zip->addFile($sourceFile, $zipPath);
            }
        }

        $zip->close();

        // Log the backup
        ActivityLogger::logBackup('public_assets', $filename, [
            'file_size' => File::size($filePath),
        ]);

        return $filePath;
    }

    /**
     * Create vendor info backup (composer and package files)
     */
    public function createVendorInfoBackup(string $backupName): string
    {
        $filename = "{$backupName}_vendor_info.zip";
        $filePath = storage_path("app/{$this->backupPath}/{$backupName}/{$filename}");

        // Ensure directory exists
        $directory = dirname($filePath);
        if (!File::exists($directory)) {
            File::makeDirectory($directory, 0755, true);
        }

        $zip = new ZipArchive();
        if ($zip->open($filePath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== TRUE) {
            throw new \Exception("Cannot create vendor info backup archive");
        }

        // Add dependency files
        $vendorFiles = [
            base_path('composer.json') => 'composer.json',
            base_path('composer.lock') => 'composer.lock',
            base_path('package.json') => 'package.json',
            base_path('package-lock.json') => 'package-lock.json',
            base_path('yarn.lock') => 'yarn.lock',
            base_path('pnpm-lock.yaml') => 'pnpm-lock.yaml',
        ];

        foreach ($vendorFiles as $sourceFile => $zipPath) {
            if (File::exists($sourceFile)) {
                $zip->addFile($sourceFile, $zipPath);
            }
        }

        // Create a vendor info summary
        $vendorInfo = [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'composer_packages' => [],
            'npm_packages' => [],
            'backup_date' => now()->toISOString(),
        ];

        // Get composer packages if composer.lock exists
        if (File::exists(base_path('composer.lock'))) {
            $composerLock = json_decode(File::get(base_path('composer.lock')), true);
            if (isset($composerLock['packages'])) {
                foreach ($composerLock['packages'] as $package) {
                    $vendorInfo['composer_packages'][$package['name']] = $package['version'];
                }
            }
        }

        // Get npm packages if package.json exists
        if (File::exists(base_path('package.json'))) {
            $packageJson = json_decode(File::get(base_path('package.json')), true);
            if (isset($packageJson['dependencies'])) {
                $vendorInfo['npm_packages'] = $packageJson['dependencies'];
            }
        }

        // Add vendor info as JSON file
        $zip->addFromString('vendor_info.json', json_encode($vendorInfo, JSON_PRETTY_PRINT));

        $zip->close();

        // Log the backup
        ActivityLogger::logBackup('vendor_info', $filename, [
            'file_size' => File::size($filePath),
        ]);

        return $filePath;
    }

    /**
     * Create full project ZIP archive
     */
    public function createFullProjectZipArchive(string $backupName): string
    {
        $sourceDir = storage_path("app/{$this->backupPath}/{$backupName}");
        $zipPath = storage_path("app/{$this->backupPath}/{$backupName}.zip");

        $zip = new ZipArchive();
        if ($zip->open($zipPath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== TRUE) {
            throw new \Exception("Cannot create full project backup archive");
        }

        $this->addDirectoryToZip($zip, $sourceDir, '');
        $zip->close();

        return $zipPath;
    }

    /**
     * Get list of available backups
     */
    public function getAvailableBackups(): array
    {
        $backupPath = storage_path("app/{$this->backupPath}");

        if (!File::exists($backupPath)) {
            return [];
        }

        $backups = [];
        $files = File::files($backupPath);

        foreach ($files as $file) {
            $backups[] = [
                'name' => $file->getFilename(),
                'path' => $file->getPathname(),
                'size' => $file->getSize(),
                'created_at' => Carbon::createFromTimestamp($file->getMTime()),
                'type' => $this->getBackupType($file->getFilename()),
            ];
        }

        // Sort by creation date (newest first)
        usort($backups, function ($a, $b) {
            return $b['created_at']->timestamp - $a['created_at']->timestamp;
        });

        return $backups;
    }

    /**
     * Delete old backups
     */
    public function cleanupOldBackups(int $keepDays = 30): int
    {
        $backupPath = storage_path("app/{$this->backupPath}");
        $cutoffDate = now()->subDays($keepDays);
        $deletedCount = 0;

        if (!File::exists($backupPath)) {
            return 0;
        }

        $files = File::files($backupPath);

        foreach ($files as $file) {
            $fileDate = Carbon::createFromTimestamp($file->getMTime());

            if ($fileDate->lt($cutoffDate)) {
                File::delete($file->getPathname());
                $deletedCount++;
            }
        }

        return $deletedCount;
    }

    /**
     * Helper methods
     */
    private function createMySQLBackup(array $config, string $filePath): void
    {
        $command = sprintf(
            'mysqldump --user=%s --password=%s --host=%s --port=%s %s > %s',
            escapeshellarg($config['username']),
            escapeshellarg($config['password']),
            escapeshellarg($config['host']),
            escapeshellarg($config['port']),
            escapeshellarg($config['database']),
            escapeshellarg($filePath)
        );

        exec($command, $output, $returnCode);

        if ($returnCode !== 0) {
            // Fallback to Laravel's database export
            $this->createLaravelDatabaseBackup($filePath);
        }
    }

    private function createSQLiteBackup(array $config, string $filePath): void
    {
        $databasePath = $config['database'];

        if (File::exists($databasePath)) {
            File::copy($databasePath, $filePath);
        } else {
            throw new \Exception("SQLite database file not found: {$databasePath}");
        }
    }

    private function createLaravelDatabaseBackup(string $filePath): void
    {
        $tables = DB::select('SHOW TABLES');
        $sql = '';

        foreach ($tables as $table) {
            $tableName = array_values((array) $table)[0];

            // Get table structure
            $createTable = DB::select("SHOW CREATE TABLE `{$tableName}`")[0];
            $sql .= $createTable->{'Create Table'} . ";\n\n";

            // Get table data
            $rows = DB::table($tableName)->get();

            foreach ($rows as $row) {
                $values = array_map(function ($value) {
                    return is_null($value) ? 'NULL' : "'" . addslashes($value) . "'";
                }, (array) $row);

                $sql .= "INSERT INTO `{$tableName}` VALUES (" . implode(', ', $values) . ");\n";
            }

            $sql .= "\n";
        }

        File::put($filePath, $sql);
    }

    private function addDirectoryToZip(ZipArchive $zip, string $dir, string $zipPath): void
    {
        if (!File::exists($dir)) {
            return;
        }

        $files = File::allFiles($dir);

        foreach ($files as $file) {
            $relativePath = $zipPath . $file->getRelativePathname();
            $zip->addFile($file->getPathname(), $relativePath);
        }
    }

    private function createZipArchive(string $backupName): string
    {
        $sourceDir = storage_path("app/{$this->backupPath}/{$backupName}");
        $zipPath = storage_path("app/{$this->backupPath}/{$backupName}.zip");

        $zip = new ZipArchive();
        if ($zip->open($zipPath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== TRUE) {
            throw new \Exception("Cannot create backup archive");
        }

        $this->addDirectoryToZip($zip, $sourceDir, '');
        $zip->close();

        return $zipPath;
    }

    public function getBackupType(string $filename): string
    {
        if (str_contains($filename, 'full_backup')) {
            return 'full';
        } elseif (str_contains($filename, 'database')) {
            return 'database';
        } elseif (str_contains($filename, 'files')) {
            return 'files';
        } elseif (str_contains($filename, 'config')) {
            return 'config';
        } elseif (str_contains($filename, 'data_export')) {
            return 'data_export';
        }

        return 'unknown';
    }

    /**
     * Restore database from backup
     */
    public function restoreDatabase(string $backupFile): bool
    {
        $backupPath = storage_path("app/{$this->backupPath}/{$backupFile}");

        if (!File::exists($backupPath)) {
            throw new \Exception("Backup file not found: {$backupFile}");
        }

        $database = config('database.default');
        $config = config("database.connections.{$database}");

        try {
            if ($database === 'mysql') {
                $this->restoreMySQLDatabase($config, $backupPath);
            } elseif ($database === 'sqlite') {
                $this->restoreSQLiteDatabase($config, $backupPath);
            } else {
                throw new \Exception("Unsupported database type for restore: {$database}");
            }

            // Log the restore activity
            ActivityLogger::logRestore($backupFile, [
                'database_type' => $database,
                'file_size' => File::size($backupPath),
            ]);

            return true;
        } catch (\Exception $e) {
            throw new \Exception("Database restore failed: " . $e->getMessage());
        }
    }

    /**
     * Restore MySQL database
     */
    private function restoreMySQLDatabase(array $config, string $backupPath): void
    {
        $command = sprintf(
            'mysql --user=%s --password=%s --host=%s --port=%s %s < %s',
            escapeshellarg($config['username']),
            escapeshellarg($config['password']),
            escapeshellarg($config['host']),
            escapeshellarg($config['port']),
            escapeshellarg($config['database']),
            escapeshellarg($backupPath)
        );

        exec($command, $output, $returnCode);

        if ($returnCode !== 0) {
            throw new \Exception("MySQL restore command failed with return code: {$returnCode}");
        }
    }

    /**
     * Restore SQLite database
     */
    private function restoreSQLiteDatabase(array $config, string $backupPath): void
    {
        $databasePath = $config['database'];

        // Backup current database
        if (File::exists($databasePath)) {
            $backupCurrent = $databasePath . '.backup.' . now()->format('Y-m-d_H-i-s');
            File::copy($databasePath, $backupCurrent);
        }

        // Restore from backup
        File::copy($backupPath, $databasePath);
    }
}
