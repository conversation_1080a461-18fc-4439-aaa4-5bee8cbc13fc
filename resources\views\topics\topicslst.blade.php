@extends('layouts.master')

@section('content')
    <!-- Row -->

    <div class="row">
        <div class="col-12">
            <div class="card-box table-responsive">
                <a href="{{ route('topics.create') }}" class="btn btn-primary float-right">Add Topic</a>
                <h4 class="header-title"><b>Quran Topics List</b></h4>
                <p class="sub-header">
                    List of Quran Topics.
                </p>

                <table id="datatable-buttons" class="table table-striped table-bordered dt-responsive nowrap"
                    style="border-collapse: collapse; border-spacing: 0; width: 100%;">
                    <thead>
                        <tr>
                            <th class="border-bottom-0">ID</th>
                            <th class="border-bottom-0">Topic Name</th>
                            <th class="border-bottom-0">Topic code</th>
                            <th class="border-bottom-0">Langugae</th>
                            <th class="border-bottom-0">Status</th>
                            <th class="border-bottom-0">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($topics as $topic)
                            <tr>
                                <td>{{ $topic->id }}</td>
                                <td>{{ $topic->topicname }}</td>
                                <td>{{ $topic->topiccode }}</td>
                                <td>{{ $topic->language->name }}</td>

                                <td><span
                                        class="badge badge-{{ $topic->isactive == 1 ? 'primary' : 'danger' }}">{{ $topic->isactive == 1 ? 'Active' : 'Disable' }}</span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('topics.edit', $topic->id) }}" class="btn btn-sm btn-purple"><i
                                                class="far fa-edit"></i></a>
                                        <form action="{{ route('topics.destroy', $topic) }}" method="POST"
                                            style="display: inline-block">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit"
                                                onclick="return confirm('Are you sure to delete this record?')"
                                                class="btn btn-danger btn-sm"><i class="fas fa-trash-alt"></i></button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach

                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <!-- End Row -->
@endsection
