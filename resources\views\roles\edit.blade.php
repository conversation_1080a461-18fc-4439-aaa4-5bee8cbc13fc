@extends('layouts.master')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('quran.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('roles.index') }}">Roles</a></li>
                    <li class="breadcrumb-item active">Edit</li>
                </ol>
            </div>
            <h4 class="page-title">Edit Role</h4>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title">
                    <i class="mdi mdi-account-group text-primary"></i> Edit Role: {{ $role->display_name }}
                </h4>

                @if($role->is_system)
                    <div class="alert alert-warning">
                        <i class="mdi mdi-alert"></i>
                        <strong>System Role:</strong> Some fields are restricted for system roles.
                    </div>
                @endif

                <form method="POST" action="{{ route('roles.update', $role) }}">
                    @csrf
                    @method('PUT')

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Role Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name', $role->name) }}" 
                                       {{ $role->is_system ? 'readonly' : '' }} required
                                       placeholder="e.g., content_manager">
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                @if($role->is_system)
                                    <small class="form-text text-muted">System role names cannot be changed</small>
                                @else
                                    <small class="form-text text-muted">Lowercase letters and underscores only</small>
                                @endif
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="display_name" class="form-label">Display Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('display_name') is-invalid @enderror" 
                                       id="display_name" name="display_name" value="{{ old('display_name', $role->display_name) }}" 
                                       {{ $role->is_system ? 'readonly' : '' }} required
                                       placeholder="e.g., Content Manager">
                                @error('display_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="3" 
                                  placeholder="Describe what this role can do...">{{ old('description', $role->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="color" class="form-label">Color <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="color" class="form-control form-control-color @error('color') is-invalid @enderror" 
                                           id="color" name="color" value="{{ old('color', $role->color) }}" required>
                                    <select class="form-select" onchange="setColor(this.value)">
                                        <option value="">Choose preset...</option>
                                        @foreach($colors as $hex => $name)
                                            <option value="{{ $hex }}">{{ $name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                @error('color')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">Sort Order</label>
                                <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                       id="sort_order" name="sort_order" value="{{ old('sort_order', $role->sort_order) }}" min="0">
                                @error('sort_order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1" 
                                   {{ old('is_active', $role->is_active) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                Active
                            </label>
                        </div>
                    </div>

                    <!-- Current Role Info -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <h6>Current Role Preview:</h6>
                                    <span class="badge me-2" style="background-color: {{ $role->color }}; color: white;">
                                        {{ $role->display_name }}
                                    </span>
                                    @if($role->is_system)
                                        <span class="badge badge-danger">System</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <h6>Role Statistics:</h6>
                                    <small>
                                        <i class="mdi mdi-key"></i> {{ $role->permissions->count() }} permissions |
                                        <i class="mdi mdi-account"></i> {{ $role->activeUsers->count() }} users
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Permissions -->
                    <div class="mb-4">
                        <h5>
                            <i class="mdi mdi-key"></i> Permissions
                            <button type="button" class="btn btn-sm btn-secondary ms-2" onclick="selectAllPermissions()">Select All</button>
                            <button type="button" class="btn btn-sm btn-light" onclick="selectNoPermissions()">Select None</button>
                        </h5>

                        @if($permissions->count() > 0)
                            <div class="row">
                                @foreach($permissions as $module => $modulePermissions)
                                    <div class="col-md-6 mb-3">
                                        <div class="card border">
                                            <div class="card-header bg-light py-2">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <h6 class="mb-0">
                                                        <span class="badge badge-secondary">{{ ucfirst($module) }}</span>
                                                        <small class="text-muted">({{ $modulePermissions->count() }})</small>
                                                    </h6>
                                                    <div>
                                                        <button type="button" class="btn btn-xs btn-outline-primary" 
                                                                onclick="selectModulePermissions('{{ $module }}', true)">All</button>
                                                        <button type="button" class="btn btn-xs btn-outline-secondary" 
                                                                onclick="selectModulePermissions('{{ $module }}', false)">None</button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="card-body py-2">
                                                @foreach($modulePermissions as $permission)
                                                    <div class="form-check">
                                                        <input type="checkbox" class="form-check-input permission-checkbox" 
                                                               id="permission_{{ $permission->id }}" name="permissions[]" 
                                                               value="{{ $permission->id }}" data-module="{{ $module }}"
                                                               {{ in_array($permission->id, old('permissions', $role->permissions->pluck('id')->toArray())) ? 'checked' : '' }}>
                                                        <label class="form-check-label" for="permission_{{ $permission->id }}">
                                                            <small>
                                                                {{ $permission->display_name }}
                                                                @if(!$permission->is_active)
                                                                    <i class="mdi mdi-pause-circle text-muted" title="Inactive"></i>
                                                                @endif
                                                            </small>
                                                        </label>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="alert alert-warning">
                                <i class="mdi mdi-alert"></i>
                                No permissions available. Please create permissions first.
                            </div>
                        @endif
                    </div>

                    <div class="text-end">
                        <a href="{{ route('roles.index') }}" class="btn btn-secondary me-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">Update Role</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function setColor(color) {
    if (color) {
        document.getElementById('color').value = color;
    }
}

function selectAllPermissions() {
    const checkboxes = document.querySelectorAll('.permission-checkbox');
    checkboxes.forEach(cb => cb.checked = true);
}

function selectNoPermissions() {
    const checkboxes = document.querySelectorAll('.permission-checkbox');
    checkboxes.forEach(cb => cb.checked = false);
}

function selectModulePermissions(module, select) {
    const checkboxes = document.querySelectorAll(`.permission-checkbox[data-module="${module}"]`);
    checkboxes.forEach(cb => cb.checked = select);
}
</script>
@endsection
