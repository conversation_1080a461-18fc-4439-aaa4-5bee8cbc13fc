<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\AuditableTrait;
use App\Contracts\AuditableInterface;

class Book extends Model implements AuditableInterface
{
    use HasFactory, AuditableTrait;

    protected $fillable = [
        'title',
        'author',
        'description',
        'file_path',
        'isactive',
        'created_by',
        'updated_by'
    ];
}
