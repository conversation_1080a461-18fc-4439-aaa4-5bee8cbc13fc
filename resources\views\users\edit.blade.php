@extends('layouts.master')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('quran.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('users.index') }}">Users</a></li>
                    <li class="breadcrumb-item active">Edit</li>
                </ol>
            </div>
            <h4 class="page-title">Edit User</h4>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <img src="{{ $user->avatar_url }}" alt="{{ $user->name }}" 
                         class="rounded-circle me-3" width="60" height="60">
                    <div>
                        <h4 class="header-title mb-1">
                            <i class="mdi mdi-account-edit text-primary"></i> Edit User: {{ $user->full_name }}
                        </h4>
                        <p class="text-muted mb-0">{{ $user->email }}</p>
                    </div>
                </div>

                <form method="POST" action="{{ route('users.update', $user) }}" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')

                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-8">
                            <h5><i class="mdi mdi-account"></i> Basic Information</h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="first_name" class="form-label">First Name</label>
                                        <input type="text" class="form-control @error('first_name') is-invalid @enderror" 
                                               id="first_name" name="first_name" value="{{ old('first_name', $user->first_name) }}">
                                        @error('first_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="last_name" class="form-label">Last Name</label>
                                        <input type="text" class="form-control @error('last_name') is-invalid @enderror" 
                                               id="last_name" name="last_name" value="{{ old('last_name', $user->last_name) }}">
                                        @error('last_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="name" class="form-label">Display Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                               id="name" name="name" value="{{ old('name', $user->name) }}" required>
                                        @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="username" class="form-label">Username</label>
                                        <input type="text" class="form-control @error('username') is-invalid @enderror" 
                                               id="username" name="username" value="{{ old('username', $user->username) }}">
                                        @error('username')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                               id="email" name="email" value="{{ old('email', $user->email) }}" required>
                                        @error('email')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">Phone</label>
                                        <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                                               id="phone" name="phone" value="{{ old('phone', $user->phone) }}">
                                        @error('phone')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="address" class="form-label">Address</label>
                                <textarea class="form-control @error('address') is-invalid @enderror" 
                                          id="address" name="address" rows="2">{{ old('address', $user->address) }}</textarea>
                                @error('address')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Password Section -->
                            <h5 class="mt-4"><i class="mdi mdi-lock"></i> Password</h5>
                            <div class="alert alert-info">
                                <i class="mdi mdi-information"></i>
                                Leave password fields empty to keep current password.
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="password" class="form-label">New Password</label>
                                        <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                               id="password" name="password">
                                        @error('password')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="password_confirmation" class="form-label">Confirm Password</label>
                                        <input type="password" class="form-control" 
                                               id="password_confirmation" name="password_confirmation">
                                    </div>
                                </div>
                            </div>

                            <!-- Preferences -->
                            <h5 class="mt-4"><i class="mdi mdi-cog"></i> Preferences</h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="timezone" class="form-label">Timezone <span class="text-danger">*</span></label>
                                        <select class="form-control @error('timezone') is-invalid @enderror" id="timezone" name="timezone" required>
                                            @foreach($timezones as $key => $value)
                                                <option value="{{ $key }}" {{ old('timezone', $user->timezone) == $key ? 'selected' : '' }}>
                                                    {{ $value }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('timezone')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="language" class="form-label">Language <span class="text-danger">*</span></label>
                                        <select class="form-control @error('language') is-invalid @enderror" id="language" name="language" required>
                                            @foreach($languages as $key => $value)
                                                <option value="{{ $key }}" {{ old('language', $user->language) == $key ? 'selected' : '' }}>
                                                    {{ $value }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('language')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Status Options -->
                            <h5 class="mt-4"><i class="mdi mdi-account-check"></i> Status & Options</h5>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1" 
                                               {{ old('is_active', $user->is_active) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            Active
                                        </label>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="is_verified" name="is_verified" value="1" 
                                               {{ old('is_verified', $user->is_verified) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_verified">
                                            Verified
                                        </label>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="force_password_change" name="force_password_change" value="1" 
                                               {{ old('force_password_change', $user->force_password_change) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="force_password_change">
                                            Force Password Change
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Avatar and Roles -->
                        <div class="col-md-4">
                            <!-- Avatar -->
                            <h5><i class="mdi mdi-image"></i> Avatar</h5>
                            <div class="text-center mb-3">
                                <img src="{{ $user->avatar_url }}" alt="{{ $user->name }}" 
                                     class="rounded-circle mb-2" width="120" height="120" id="avatar-preview">
                                <div>
                                    <input type="file" class="form-control @error('avatar') is-invalid @enderror" 
                                           id="avatar" name="avatar" accept="image/*" onchange="previewAvatar(this)">
                                    @error('avatar')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Max 2MB, JPG/PNG only</small>
                                </div>
                            </div>

                            <!-- Current Roles -->
                            <h5><i class="mdi mdi-account-group"></i> Current Roles</h5>
                            <div class="mb-3">
                                @if($user->activeRoles->count() > 0)
                                    @foreach($user->activeRoles as $role)
                                        <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                                            <span class="badge" style="background-color: {{ $role->color }}; color: white;">
                                                {{ $role->display_name }}
                                            </span>
                                            <small class="text-muted">
                                                @if($role->pivot->expires_at)
                                                    Expires: {{ $role->pivot->expires_at->format('M j, Y') }}
                                                @else
                                                    Permanent
                                                @endif
                                            </small>
                                        </div>
                                    @endforeach
                                @else
                                    <p class="text-muted">No roles assigned</p>
                                @endif
                            </div>

                            <!-- Role Assignment -->
                            <h5><i class="mdi mdi-account-plus"></i> Assign Roles</h5>
                            <div class="mb-3">
                                @foreach($roles as $role)
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" 
                                               id="role_{{ $role->id }}" name="roles[]" value="{{ $role->id }}"
                                               {{ in_array($role->id, old('roles', $user->activeRoles->pluck('id')->toArray())) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="role_{{ $role->id }}">
                                            <span class="badge me-1" style="background-color: {{ $role->color }}; color: white;">
                                                {{ $role->display_name }}
                                            </span>
                                            @if($role->is_system)
                                                <span class="badge badge-danger">System</span>
                                            @endif
                                        </label>
                                    </div>
                                @endforeach
                            </div>

                            <!-- User Statistics -->
                            <h5><i class="mdi mdi-chart-line"></i> Statistics</h5>
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <small>
                                        <strong>Created:</strong> {{ $user->created_at->format('M j, Y') }}<br>
                                        <strong>Last Login:</strong> 
                                        @if($user->last_login_at)
                                            {{ $user->last_login_at->diffForHumans() }}
                                        @else
                                            Never
                                        @endif
                                        <br>
                                        <strong>Roles:</strong> {{ $user->activeRoles->count() }}<br>
                                        <strong>Permissions:</strong> {{ $user->getAllPermissions()->count() }}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-end mt-4">
                        <a href="{{ route('users.index') }}" class="btn btn-secondary me-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">Update User</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function previewAvatar(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('avatar-preview').src = e.target.result;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// Auto-generate display name from first and last name
document.addEventListener('DOMContentLoaded', function() {
    const firstNameInput = document.getElementById('first_name');
    const lastNameInput = document.getElementById('last_name');
    const nameInput = document.getElementById('name');

    function updateDisplayName() {
        const firstName = firstNameInput.value.trim();
        const lastName = lastNameInput.value.trim();
        
        if (firstName || lastName) {
            const fullName = [firstName, lastName].filter(n => n).join(' ');
            if (nameInput.dataset.autoGenerated !== 'false') {
                nameInput.value = fullName;
                nameInput.dataset.autoGenerated = 'true';
            }
        }
    }

    firstNameInput.addEventListener('input', updateDisplayName);
    lastNameInput.addEventListener('input', updateDisplayName);
    
    nameInput.addEventListener('input', function() {
        this.dataset.autoGenerated = 'false';
    });
});
</script>
@endsection
