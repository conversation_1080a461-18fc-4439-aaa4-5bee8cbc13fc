/*
Template Name: Abstack - Responsive Bootstrap 4 Admin Dashboard
Author: CoderThemes
Version: 2.0.0
Website: https://coderthemes.com/
Contact: <EMAIL>
File: Main Css File
*/
@import url("https://fonts.googleapis.com/css?family=Nunito+Sans:400,600,700|Roboto:300,400,500,700&display=swap");
html {
  position: relative;
  min-height: 100%; }

body {
  padding-bottom: 60px;
  overflow-x: hidden; }

.metismenu {
  padding: 0; }
  .metismenu li {
    list-style: none; }
  .metismenu ul {
    padding: 0; }
    .metismenu ul li {
      width: 100%; }
  .metismenu .mm-collapse:not(.mm-show) {
    display: none; }
  .metismenu .mm-collapsing {
    position: relative;
    height: 0;
    overflow: hidden;
    -webkit-transition-timing-function: ease;
            transition-timing-function: ease;
    -webkit-transition-duration: .35s;
            transition-duration: .35s;
    -webkit-transition-property: height, visibility;
    transition-property: height, visibility; }

.nav-second-level li a,
.nav-thrid-level li a {
  padding: 8px 20px;
  color: #6e768e;
  display: block;
  position: relative;
  -webkit-transition: all 0.4s;
  transition: all 0.4s; }
  .nav-second-level li a:focus, .nav-second-level li a:hover,
  .nav-thrid-level li a:focus,
  .nav-thrid-level li a:hover {
    color: #348cd4; }

.nav-second-level li.mm-active > a,
.nav-third-level li.mm-active > a {
  color: #348cd4; }

#wrapper {
  height: 100%;
  overflow: hidden;
  width: 100%; }

.content-page {
  margin-left: 240px;
  overflow: hidden;
  padding: 0 15px 5px 15px;
  min-height: 80vh;
  margin-top: 70px; }

.left-side-menu {
  width: 240px;
  background: #fff;
  bottom: 0;
  padding: 20px 0;
  position: fixed;
  -webkit-transition: all .2s ease-out;
  transition: all .2s ease-out;
  top: 70px;
  -webkit-box-shadow: 0px 0px 35px 0px rgba(154, 161, 171, 0.15);
          box-shadow: 0px 0px 35px 0px rgba(154, 161, 171, 0.15);
  z-index: 10; }

#sidebar-menu > ul > li > a {
  color: #6e768e;
  display: block;
  padding: 13px 20px;
  position: relative;
  -webkit-transition: all 0.4s;
  transition: all 0.4s;
  font-family: "Roboto", sans-serif;
  font-size: 15px; }
  #sidebar-menu > ul > li > a:hover, #sidebar-menu > ul > li > a:focus, #sidebar-menu > ul > li > a:active {
    color: #348cd4;
    text-decoration: none;
    background: #f7f7f7; }
  #sidebar-menu > ul > li > a > span {
    vertical-align: middle; }
  #sidebar-menu > ul > li > a i {
    display: inline-block;
    line-height: 1.0625rem;
    margin: 0 10px 0 3px;
    text-align: center;
    vertical-align: middle;
    width: 20px;
    font-size: 16px; }
  #sidebar-menu > ul > li > a .drop-arrow {
    float: right; }
    #sidebar-menu > ul > li > a .drop-arrow i {
      margin-right: 0; }

#sidebar-menu > ul > li > a.active {
  color: #348cd4;
  background: #f7f7f7; }

#sidebar-menu > ul > li > ul {
  padding-left: 37px; }
  #sidebar-menu > ul > li > ul ul {
    padding-left: 20px; }

#sidebar-menu .menu-arrow {
  -webkit-transition: -webkit-transform .15s;
  transition: -webkit-transform .15s;
  transition: transform .15s;
  transition: transform .15s, -webkit-transform .15s;
  position: absolute;
  right: 20px;
  display: inline-block;
  font-family: 'Material Design Icons';
  text-rendering: auto;
  line-height: 1.5rem;
  font-size: 1.1rem;
  -webkit-transform: translate(0, 0);
          transform: translate(0, 0); }
  #sidebar-menu .menu-arrow:before {
    content: "\F142"; }

#sidebar-menu .badge {
  margin-top: 4px; }

#sidebar-menu li.mm-active > a > span.menu-arrow {
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg); }

#sidebar-menu .menu-title {
  padding: 10px 20px;
  letter-spacing: .05em;
  pointer-events: none;
  cursor: default;
  font-size: 0.6875rem;
  text-transform: uppercase;
  color: #6e768e;
  font-weight: 600; }

.enlarged .logo-box {
  width: 70px !important; }

.enlarged .logo span.logo-lg {
  display: none; }

.enlarged .logo span.logo-sm {
  display: block; }

.enlarged .left-side-menu {
  position: absolute;
  padding-top: 0;
  width: 70px !important;
  z-index: 5; }
  .enlarged .left-side-menu .slimScrollDiv,
  .enlarged .left-side-menu .slimscroll-menu {
    overflow: inherit !important;
    height: auto !important; }
  .enlarged .left-side-menu .slimScrollBar {
    visibility: hidden; }
  .enlarged .left-side-menu #sidebar-menu .menu-title,
  .enlarged .left-side-menu #sidebar-menu .menu-arrow,
  .enlarged .left-side-menu #sidebar-menu .label,
  .enlarged .left-side-menu #sidebar-menu .badge {
    display: none !important; }
  .enlarged .left-side-menu #sidebar-menu > ul > li {
    position: relative;
    white-space: nowrap; }
    .enlarged .left-side-menu #sidebar-menu > ul > li > a {
      padding: 15px 20px;
      min-height: 54px;
      -webkit-transition: none;
      transition: none; }
      .enlarged .left-side-menu #sidebar-menu > ul > li > a:hover, .enlarged .left-side-menu #sidebar-menu > ul > li > a:active, .enlarged .left-side-menu #sidebar-menu > ul > li > a:focus {
        color: #348cd4; }
      .enlarged .left-side-menu #sidebar-menu > ul > li > a i {
        font-size: 1.125rem;
        margin-right: 20px; }
      .enlarged .left-side-menu #sidebar-menu > ul > li > a span {
        display: none;
        padding-left: 25px; }
    .enlarged .left-side-menu #sidebar-menu > ul > li:hover > a {
      position: relative;
      width: calc(190px + 70px);
      color: #348cd4;
      background-color: #f7f7f7;
      -webkit-transition: none;
      transition: none; }
      .enlarged .left-side-menu #sidebar-menu > ul > li:hover > a span {
        display: inline; }
    .enlarged .left-side-menu #sidebar-menu > ul > li:hover a.open :after, .enlarged .left-side-menu #sidebar-menu > ul > li:hover a.mm-active :after {
      display: none; }
    .enlarged .left-side-menu #sidebar-menu > ul > li:hover > ul {
      display: block;
      left: 70px;
      position: absolute;
      width: 190px;
      height: auto !important;
      -webkit-box-shadow: 3px 5px 10px 0 rgba(154, 161, 171, 0.2);
              box-shadow: 3px 5px 10px 0 rgba(154, 161, 171, 0.2); }
      .enlarged .left-side-menu #sidebar-menu > ul > li:hover > ul ul {
        -webkit-box-shadow: 3px 5px 10px 0 rgba(154, 161, 171, 0.2);
                box-shadow: 3px 5px 10px 0 rgba(154, 161, 171, 0.2); }
      .enlarged .left-side-menu #sidebar-menu > ul > li:hover > ul a {
        -webkit-box-shadow: none;
                box-shadow: none;
        padding: 8px 20px;
        position: relative;
        width: 190px;
        z-index: 6; }
        .enlarged .left-side-menu #sidebar-menu > ul > li:hover > ul a:hover {
          color: #348cd4; }
  .enlarged .left-side-menu #sidebar-menu > ul ul {
    padding: 5px 0;
    z-index: 9999;
    display: none;
    background-color: #fff; }
    .enlarged .left-side-menu #sidebar-menu > ul ul li:hover > ul {
      display: block;
      left: 190px;
      margin-top: -36px;
      height: auto !important;
      position: absolute;
      width: 190px; }
    .enlarged .left-side-menu #sidebar-menu > ul ul li > a span.pull-right {
      position: absolute;
      right: 20px;
      top: 12px;
      -webkit-transform: rotate(270deg);
              transform: rotate(270deg); }
    .enlarged .left-side-menu #sidebar-menu > ul ul li.active a {
      color: #348cd4; }

.enlarged .content-page {
  margin-left: 70px !important; }

.enlarged .footer {
  left: 70px !important; }

.enlarged .user-box {
  display: none; }

body.enlarged {
  min-height: 1200px; }

@media (max-width: 767.98px) {
  body {
    overflow-x: hidden;
    padding-bottom: 80px; }
  .left-side-menu {
    display: none;
    z-index: 999 !important; }
  .sidebar-enable .left-side-menu {
    display: block; }
  .content-page, .enlarged .content-page {
    margin-left: 0 !important;
    padding: 0 10px; }
  .pro-user-name {
    display: none; }
  .logo-box {
    display: none; } }

.logo {
  display: block;
  line-height: 70px; }
  .logo span.logo-lg {
    display: block; }
  .logo span.logo-sm {
    display: none; }
  .logo .logo-lg-text-dark {
    color: #323a46;
    font-weight: 700;
    font-size: 22px;
    text-transform: uppercase; }
  .logo .logo-lg-text-light {
    color: #fff;
    font-weight: 700;
    font-size: 22px;
    text-transform: uppercase; }

.logo-box {
  height: 70px;
  width: 240px;
  float: left; }

.navbar-custom {
  background: #348cd4;
  -webkit-box-shadow: 0px 0px 35px 0px rgba(154, 161, 171, 0.15);
          box-shadow: 0px 0px 35px 0px rgba(154, 161, 171, 0.15);
  padding: 0 10px 0 0;
  position: fixed;
  left: 0;
  right: 0;
  height: 70px;
  z-index: 100;
  /* Search */ }
  .navbar-custom .topnav-menu > li {
    float: left; }
  .navbar-custom .topnav-menu .nav-link {
    padding: 0 15px;
    color: rgba(255, 255, 255, 0.6);
    min-width: 32px;
    display: block;
    line-height: 70px;
    text-align: center;
    max-height: 70px; }
  .navbar-custom .dropdown.show .nav-link {
    background-color: rgba(255, 255, 255, 0.05); }
  .navbar-custom .app-search {
    overflow: hidden;
    height: 70px;
    display: table;
    max-width: 200px;
    margin-right: 20px; }
    .navbar-custom .app-search .app-search-box {
      display: table-cell;
      vertical-align: middle; }
      .navbar-custom .app-search .app-search-box input::-webkit-input-placeholder {
        font-size: 0.8125rem;
        color: rgba(255, 255, 255, 0.7); }
    .navbar-custom .app-search .form-control {
      border: none;
      height: 38px;
      padding-left: 20px;
      padding-right: 0;
      color: #fff;
      background-color: rgba(255, 255, 255, 0.07);
      -webkit-box-shadow: none;
              box-shadow: none;
      border-radius: 30px 0 0 30px; }
    .navbar-custom .app-search .input-group-append {
      margin-left: 0;
      z-index: 4; }
    .navbar-custom .app-search .btn {
      background-color: rgba(255, 255, 255, 0.07);
      color: #fff;
      border-color: transparent;
      border-radius: 0 30px 30px 0;
      -webkit-box-shadow: none !important;
              box-shadow: none !important; }
  .navbar-custom .button-menu-mobile {
    border: none;
    color: #fff;
    display: inline-block;
    height: 70px;
    line-height: 70px;
    width: 60px;
    background-color: transparent;
    font-size: 24px;
    cursor: pointer; }
  .navbar-custom .button-menu-mobile.disable-btn {
    display: none; }

/* Notification */
.noti-scroll {
  max-height: 230px; }

.notification-list {
  margin-left: 0; }
  .notification-list .noti-title {
    background-color: #fff;
    padding: 15px 20px; }
  .notification-list .noti-icon {
    font-size: 21px;
    vertical-align: middle; }
  .notification-list .noti-icon-badge {
    display: inline-block;
    position: absolute;
    top: 16px;
    right: 10px; }
  .notification-list .notify-item {
    padding: 12px 20px; }
    .notification-list .notify-item .notify-icon {
      float: left;
      height: 36px;
      width: 36px;
      font-size: 18px;
      line-height: 36px;
      text-align: center;
      margin-top: 4px;
      margin-right: 10px;
      border-radius: 50%;
      color: #fff; }
    .notification-list .notify-item .notify-details {
      margin-bottom: 5px;
      overflow: hidden;
      margin-left: 45px;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #414d5f;
      font-weight: 500; }
      .notification-list .notify-item .notify-details b {
        font-weight: 500; }
      .notification-list .notify-item .notify-details small {
        display: block; }
      .notification-list .notify-item .notify-details span {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 13px; }
    .notification-list .notify-item .user-msg {
      margin-left: 45px;
      white-space: normal;
      line-height: 16px; }
  .notification-list .profile-dropdown .notify-item {
    padding: 7px 20px; }

.profile-dropdown {
  width: 170px; }
  .profile-dropdown i {
    vertical-align: middle;
    margin-right: 5px; }

.nav-user {
  padding: 0 12px !important; }
  .nav-user img {
    height: 32px;
    width: 32px; }

.page-title-box {
  padding: 0px 20px;
  margin: 0 -27px 30px -27px;
  background-color: #fff;
  -webkit-box-shadow: 0px 0px 35px 0px rgba(154, 161, 171, 0.15);
          box-shadow: 0px 0px 35px 0px rgba(154, 161, 171, 0.15); }
  .page-title-box .page-title {
    font-size: 18px;
    margin: 0;
    line-height: 50px;
    font-weight: 700; }
  .page-title-box .page-title-right {
    float: right; }
  .page-title-box .breadcrumb {
    margin-bottom: 0;
    padding: 14px 0; }

@media (max-width: 767.98px) {
  .page-title-box .page-title {
    display: block;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden; }
  .page-title-box .breadcrumb {
    display: none; } }

@media (max-width: 640px) {
  .page-title-box .page-title-right {
    display: none; } }

@media (max-width: 419px) {
  .page-title-box .breadcrumb, .nav-user span {
    display: none; } }

.footer {
  bottom: 0;
  padding: 19px 15px 20px;
  position: absolute;
  right: 0;
  color: #98a6ad;
  left: 240px;
  border-top: 1px solid #dee2e6;
  text-align: center; }

@media (max-width: 767.98px) {
  .footer {
    left: 0 !important;
    text-align: center; } }

.right-bar {
  background-color: #fff;
  -webkit-box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);
          box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);
  display: block;
  position: fixed;
  -webkit-transition: all 200ms ease-out;
  transition: all 200ms ease-out;
  width: 260px;
  z-index: 9999;
  float: right !important;
  right: -270px;
  top: 0;
  bottom: 0; }
  .right-bar .rightbar-title {
    background: #348cd4;
    padding: 27px 25px;
    color: #fff; }
  .right-bar .right-bar-toggle {
    background-color: #414b5b;
    height: 24px;
    width: 24px;
    line-height: 24px;
    color: #fff;
    text-align: center;
    border-radius: 50%;
    margin-top: -4px; }
    .right-bar .right-bar-toggle:hover {
      background-color: #475364; }
  .right-bar .user-box {
    padding: 25px;
    text-align: center; }
    .right-bar .user-box .user-img {
      position: relative;
      height: 64px;
      width: 64px;
      margin: 0 auto 15px auto; }
      .right-bar .user-box .user-img .user-edit {
        position: absolute;
        right: -5px;
        bottom: 0px;
        height: 24px;
        width: 24px;
        background-color: #fff;
        line-height: 24px;
        border-radius: 50%;
        -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);
                box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12); }
    .right-bar .user-box h5 {
      margin-bottom: 2px; }
      .right-bar .user-box h5 a {
        color: #323a46; }

.rightbar-overlay {
  background-color: rgba(50, 58, 70, 0.55);
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: none;
  z-index: 9998;
  -webkit-transition: all .2s ease-out;
  transition: all .2s ease-out; }

.right-bar-enabled .right-bar {
  right: 0; }

.right-bar-enabled .rightbar-overlay {
  display: block; }

@media (max-width: 767.98px) {
  .right-bar {
    overflow: auto; }
    .right-bar .slimscroll-menu {
      height: auto !important; } }

.activity-widget .activity-list {
  position: relative;
  border-left: 2px dashed #ced4da;
  padding-left: 24px;
  padding-bottom: 2px; }
  .activity-widget .activity-list::after {
    content: "";
    position: absolute;
    left: -7px;
    top: 6px;
    width: 12px;
    height: 12px;
    background-color: #fff;
    border: 2px solid #348cd4;
    border-radius: 50%; }

.inbox-widget .inbox-item {
  overflow: hidden;
  padding: 0.625rem 0;
  position: relative; }
  .inbox-widget .inbox-item .inbox-item-img {
    display: block;
    float: left;
    margin-right: 15px;
    margin-top: 4px; }
    .inbox-widget .inbox-item .inbox-item-img img {
      width: 40px; }
  .inbox-widget .inbox-item .inbox-item-author {
    display: block;
    margin-bottom: 0px;
    font-weight: 600; }
    .inbox-widget .inbox-item .inbox-item-author a {
      color: #6c757d; }
  .inbox-widget .inbox-item .inbox-item-text {
    color: #98a6ad;
    display: block;
    margin: 0;
    overflow: hidden; }
  .inbox-widget .inbox-item .inbox-item-date {
    color: #98a6ad;
    font-size: 0.6875rem;
    position: absolute;
    right: 5px;
    top: 10px; }

.width-xs {
  min-width: 80px; }

.width-sm {
  min-width: 95px; }

.width-md {
  min-width: 110px; }

.width-lg {
  min-width: 140px; }

.width-xl {
  min-width: 160px; }

.font-family-secondary {
  font-family: "Roboto", sans-serif; }

.avatar-xs {
  height: 1.5rem;
  width: 1.5rem; }

.avatar-sm {
  height: 2.25rem;
  width: 2.25rem; }

.avatar-md {
  height: 3.5rem;
  width: 3.5rem; }

.avatar-lg {
  height: 4.5rem;
  width: 4.5rem; }

.avatar-xl {
  height: 6rem;
  width: 6rem; }

.avatar-xxl {
  height: 7.5rem;
  width: 7.5rem; }

.avatar-title {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  color: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%; }

.avatar-group {
  padding-left: 12px; }
  .avatar-group .avatar-group-item {
    margin: 0 0 10px -12px;
    display: inline-block;
    border: 2px solid #fff;
    border-radius: 50%; }

.font-weight-medium {
  font-weight: 500; }

.font-weight-semibold {
  font-weight: 600; }

.sp-line-1,
.sp-line-2,
.sp-line-3,
.sp-line-4 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical; }

.sp-line-1 {
  -webkit-line-clamp: 1; }

.sp-line-2 {
  -webkit-line-clamp: 2; }

.sp-line-3 {
  -webkit-line-clamp: 3; }

.sp-line-4 {
  -webkit-line-clamp: 4; }

.pull-in {
  margin-left: -1.5rem;
  margin-right: -1.5rem; }

.social-list-item {
  height: 2rem;
  width: 2rem;
  line-height: calc(2rem - 4px);
  display: block;
  border: 2px solid #adb5bd;
  border-radius: 50%;
  color: #adb5bd; }

.checkbox label {
  display: inline-block;
  padding-left: 8px;
  position: relative;
  font-weight: normal; }
  .checkbox label::before {
    -o-transition: 0.3s ease-in-out;
    -webkit-transition: 0.3s ease-in-out;
    background-color: #fff;
    border-radius: 3px;
    border: 2px solid #98a6ad;
    content: "";
    display: inline-block;
    height: 18px;
    left: 0;
    margin-left: -18px;
    position: absolute;
    transition: 0.3s ease-in-out;
    width: 18px;
    outline: none !important;
    top: 2px; }
  .checkbox label::after {
    color: #6c757d;
    display: inline-block;
    font-size: 11px;
    height: 18px;
    left: 0;
    margin-left: -18px;
    padding-left: 3px;
    padding-top: 2px;
    position: absolute;
    top: 0;
    width: 18px; }

.checkbox input[type="checkbox"] {
  cursor: pointer;
  opacity: 0;
  z-index: 1;
  outline: none !important; }
  .checkbox input[type="checkbox"]:disabled + label {
    opacity: 0.65; }

.checkbox input[type="checkbox"]:focus + label::before {
  outline-offset: -2px;
  outline: none; }

.checkbox input[type="checkbox"]:checked + label::after {
  content: "";
  position: absolute;
  top: 6px;
  left: 7px;
  display: table;
  width: 4px;
  height: 8px;
  border: 2px solid #6c757d;
  border-top-width: 0;
  border-left-width: 0;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg); }

.checkbox input[type="checkbox"]:disabled + label::before {
  background-color: #f7f7f7;
  cursor: not-allowed; }

.checkbox.checkbox-circle label::before {
  border-radius: 50%; }

.checkbox.checkbox-inline {
  margin-top: 0; }

.checkbox.checkbox-single input {
  height: 18px;
  width: 18px;
  position: absolute; }

.checkbox.checkbox-single label {
  height: 18px;
  width: 18px; }
  .checkbox.checkbox-single label:before {
    margin-left: 0; }
  .checkbox.checkbox-single label:after {
    margin-left: 0; }

.checkbox-primary input[type="checkbox"]:checked + label::before {
  background-color: #348cd4;
  border-color: #348cd4; }

.checkbox-primary input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-secondary input[type="checkbox"]:checked + label::before {
  background-color: #6c757d;
  border-color: #6c757d; }

.checkbox-secondary input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-success input[type="checkbox"]:checked + label::before {
  background-color: #3ec396;
  border-color: #3ec396; }

.checkbox-success input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-info input[type="checkbox"]:checked + label::before {
  background-color: #4fbde9;
  border-color: #4fbde9; }

.checkbox-info input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-warning input[type="checkbox"]:checked + label::before {
  background-color: #f9bc0b;
  border-color: #f9bc0b; }

.checkbox-warning input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-danger input[type="checkbox"]:checked + label::before {
  background-color: #f36270;
  border-color: #f36270; }

.checkbox-danger input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-light input[type="checkbox"]:checked + label::before {
  background-color: #f7f7f7;
  border-color: #f7f7f7; }

.checkbox-light input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-dark input[type="checkbox"]:checked + label::before {
  background-color: #323a46;
  border-color: #323a46; }

.checkbox-dark input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-pink input[type="checkbox"]:checked + label::before {
  background-color: #e061c9;
  border-color: #e061c9; }

.checkbox-pink input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-purple input[type="checkbox"]:checked + label::before {
  background-color: #9368f3;
  border-color: #9368f3; }

.checkbox-purple input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.radio label {
  display: inline-block;
  padding-left: 8px;
  position: relative;
  font-weight: normal; }
  .radio label::before {
    -o-transition: border 0.5s ease-in-out;
    -webkit-transition: border 0.5s ease-in-out;
    background-color: #fff;
    border-radius: 50%;
    border: 2px solid #98a6ad;
    content: "";
    display: inline-block;
    height: 18px;
    left: 0;
    margin-left: -18px;
    position: absolute;
    transition: border 0.5s ease-in-out;
    width: 18px;
    outline: none !important; }
  .radio label::after {
    -moz-transition: -moz-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
    -ms-transform: scale(0, 0);
    -o-transform: scale(0, 0);
    -o-transition: -o-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
    -webkit-transform: scale(0, 0);
    -webkit-transition: -webkit-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
    background-color: #6c757d;
    border-radius: 50%;
    content: " ";
    display: inline-block;
    height: 10px;
    left: 6px;
    margin-left: -20px;
    position: absolute;
    top: 4px;
    transform: scale(0, 0);
    transition: -webkit-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
    transition: transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
    transition: transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33), -webkit-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
    width: 10px; }

.radio input[type="radio"] {
  cursor: pointer;
  opacity: 0;
  z-index: 1;
  outline: none !important; }
  .radio input[type="radio"]:disabled + label {
    opacity: 0.65; }

.radio input[type="radio"]:focus + label::before {
  outline-offset: -2px;
  outline: 5px auto -webkit-focus-ring-color;
  outline: thin dotted; }

.radio input[type="radio"]:checked + label::after {
  -webkit-transform: scale(1, 1);
  transform: scale(1, 1); }

.radio input[type="radio"]:disabled + label::before {
  cursor: not-allowed; }

.radio.radio-inline {
  margin-top: 0; }

.radio.radio-single label {
  height: 17px; }

.radio-primary input[type="radio"] + label::after {
  background-color: #348cd4; }

.radio-primary input[type="radio"]:checked + label::before {
  border-color: #348cd4; }

.radio-primary input[type="radio"]:checked + label::after {
  background-color: #348cd4; }

.radio-secondary input[type="radio"] + label::after {
  background-color: #6c757d; }

.radio-secondary input[type="radio"]:checked + label::before {
  border-color: #6c757d; }

.radio-secondary input[type="radio"]:checked + label::after {
  background-color: #6c757d; }

.radio-success input[type="radio"] + label::after {
  background-color: #3ec396; }

.radio-success input[type="radio"]:checked + label::before {
  border-color: #3ec396; }

.radio-success input[type="radio"]:checked + label::after {
  background-color: #3ec396; }

.radio-info input[type="radio"] + label::after {
  background-color: #4fbde9; }

.radio-info input[type="radio"]:checked + label::before {
  border-color: #4fbde9; }

.radio-info input[type="radio"]:checked + label::after {
  background-color: #4fbde9; }

.radio-warning input[type="radio"] + label::after {
  background-color: #f9bc0b; }

.radio-warning input[type="radio"]:checked + label::before {
  border-color: #f9bc0b; }

.radio-warning input[type="radio"]:checked + label::after {
  background-color: #f9bc0b; }

.radio-danger input[type="radio"] + label::after {
  background-color: #f36270; }

.radio-danger input[type="radio"]:checked + label::before {
  border-color: #f36270; }

.radio-danger input[type="radio"]:checked + label::after {
  background-color: #f36270; }

.radio-light input[type="radio"] + label::after {
  background-color: #f7f7f7; }

.radio-light input[type="radio"]:checked + label::before {
  border-color: #f7f7f7; }

.radio-light input[type="radio"]:checked + label::after {
  background-color: #f7f7f7; }

.radio-dark input[type="radio"] + label::after {
  background-color: #323a46; }

.radio-dark input[type="radio"]:checked + label::before {
  border-color: #323a46; }

.radio-dark input[type="radio"]:checked + label::after {
  background-color: #323a46; }

.radio-pink input[type="radio"] + label::after {
  background-color: #e061c9; }

.radio-pink input[type="radio"]:checked + label::before {
  border-color: #e061c9; }

.radio-pink input[type="radio"]:checked + label::after {
  background-color: #e061c9; }

.radio-purple input[type="radio"] + label::after {
  background-color: #9368f3; }

.radio-purple input[type="radio"]:checked + label::before {
  border-color: #9368f3; }

.radio-purple input[type="radio"]:checked + label::after {
  background-color: #9368f3; }

@media print {
  .left-side-menu,
  .right-bar,
  .page-title-box,
  .navbar-custom,
  .footer {
    display: none; }
  .card-body,
  .content-page,
  .right-bar,
  .content,
  body {
    padding: 0;
    margin: 0; } }

/* =============
   Widgets
============= */
.tilebox-one {
  background: url("../images/bg-1.png");
  background-size: cover;
  border: 4px solid #fff; }
  .tilebox-one i {
    background: #348cd4;
    background: #348cd4;
    font-size: 24px;
    height: 50px;
    line-height: 50px;
    width: 50px;
    text-align: center;
    color: #fff !important;
    border-radius: 50%;
    -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);
            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12); }

/* Inbox-widget */
.inbox-widget .inbox-item {
  border-bottom: 1px solid #edeff1;
  overflow: hidden;
  padding: 10px 0;
  position: relative; }
  .inbox-widget .inbox-item .inbox-item-img {
    display: block;
    float: left;
    margin-right: 15px;
    width: 40px; }
  .inbox-widget .inbox-item img {
    width: 40px; }
  .inbox-widget .inbox-item .inbox-item-author {
    color: #323a46;
    display: block;
    margin: 0; }
  .inbox-widget .inbox-item .inbox-item-text {
    color: #98a6ad;
    display: block;
    font-size: 14px;
    margin: 0; }
  .inbox-widget .inbox-item .inbox-item-date {
    color: #98a6ad;
    font-size: 11px;
    position: absolute;
    right: 7px;
    top: 7px; }

/* Comment List */
.comment-list .comment-box-item {
  position: relative; }
  .comment-list .comment-box-item .commnet-item-date {
    color: #98a6ad;
    font-size: 11px;
    position: absolute;
    right: 7px;
    top: 2px; }
  .comment-list .comment-box-item .commnet-item-msg {
    color: #323a46;
    display: block;
    margin: 10px 0;
    font-weight: normal;
    font-size: 15px;
    line-height: 24px; }
  .comment-list .comment-box-item .commnet-item-user {
    color: #98a6ad;
    display: block;
    font-size: 14px;
    margin: 0; }

.comment-list a + a {
  margin-top: 15px;
  display: block; }

/* Transaction */
.transaction-list li {
  padding: 7px 0;
  border-bottom: 1px solid #edeff1;
  clear: both;
  position: relative; }

.transaction-list i {
  width: 20px;
  position: absolute;
  top: 10px;
  font-size: 12px; }

.transaction-list .tran-text {
  padding-left: 25px;
  white-space: nowrap;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 150px; }

.transaction-list .tran-price {
  margin-left: 30px; }

/*!
 * Waves v0.7.6
 * http://fian.my.id/Waves 
 * 
 * Copyright 2014-2018 Alfiana E. Sibuea and other contributors 
 * Released under the MIT license 
 * https://github.com/fians/Waves/blob/master/LICENSE */
.waves-effect {
  position: relative;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent; }

.waves-effect .waves-ripple {
  position: absolute;
  border-radius: 50%;
  width: 100px;
  height: 100px;
  margin-top: -50px;
  margin-left: -50px;
  opacity: 0;
  background: rgba(0, 0, 0, 0.2);
  background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
  -webkit-transition: all 0.5s ease-out;
  transition: all 0.5s ease-out;
  -webkit-transition-property: -webkit-transform, opacity;
  -webkit-transition-property: opacity, -webkit-transform;
  transition-property: opacity, -webkit-transform;
  transition-property: transform, opacity;
  transition-property: transform, opacity, -webkit-transform;
  -webkit-transform: scale(0) translate(0, 0);
  transform: scale(0) translate(0, 0);
  pointer-events: none; }

.waves-effect.waves-light .waves-ripple {
  background: rgba(255, 255, 255, 0.4);
  background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%); }

.waves-effect.waves-classic .waves-ripple {
  background: rgba(0, 0, 0, 0.2); }

.waves-effect.waves-classic.waves-light .waves-ripple {
  background: rgba(255, 255, 255, 0.4); }

.waves-notransition {
  -webkit-transition: none !important;
  transition: none !important; }

.waves-button,
.waves-circle {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%); }

.waves-button,
.waves-button:hover,
.waves-button:visited,
.waves-button-input {
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  border: none;
  outline: none;
  color: inherit;
  background-color: rgba(0, 0, 0, 0);
  font-size: 1em;
  line-height: 1em;
  text-align: center;
  text-decoration: none;
  z-index: 1; }

.waves-button {
  padding: 0.85em 1.1em;
  border-radius: 0.2em; }

.waves-button-input {
  margin: 0;
  padding: 0.85em 1.1em; }

.waves-input-wrapper {
  border-radius: 0.2em;
  vertical-align: bottom; }

.waves-input-wrapper.waves-button {
  padding: 0; }

.waves-input-wrapper .waves-button-input {
  position: relative;
  top: 0;
  left: 0;
  z-index: 1; }

.waves-circle {
  text-align: center;
  width: 2.5em;
  height: 2.5em;
  line-height: 2.5em;
  border-radius: 50%; }

.waves-float {
  -webkit-mask-image: none;
  -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);
  box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);
  -webkit-transition: all 300ms;
  transition: all 300ms; }

.waves-float:active {
  -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);
  box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3); }

.waves-block {
  display: block; }

.slimScrollDiv {
  height: auto !important; }

.jq-toast-single {
  padding: 15px;
  font-family: "Nunito Sans", sans-serif;
  background-color: #348cd4;
  font-size: 13px;
  line-height: 22px; }
  .jq-toast-single h2 {
    font-family: "Nunito Sans", sans-serif; }
  .jq-toast-single a {
    font-size: 0.875rem; }
    .jq-toast-single a:hover {
      color: #fff; }

.jq-has-icon {
  padding: 10px 10px 10px 50px; }

.close-jq-toast-single {
  position: absolute;
  top: -12px;
  right: -12px;
  font-size: 20px;
  cursor: pointer;
  height: 32px;
  width: 32px;
  background-color: #323a46;
  border-radius: 50%;
  text-align: center;
  line-height: 32px; }

.jq-toast-loader {
  height: 3px;
  top: 0;
  border-radius: 0; }

.jq-icon-primary {
  background-color: #348cd4;
  color: #fff;
  border-color: #348cd4; }

.jq-icon-secondary {
  background-color: #6c757d;
  color: #fff;
  border-color: #6c757d; }

.jq-icon-success {
  background-color: #3ec396;
  color: #fff;
  border-color: #3ec396; }

.jq-icon-info {
  background-color: #4fbde9;
  color: #fff;
  border-color: #4fbde9; }

.jq-icon-warning {
  background-color: #f9bc0b;
  color: #fff;
  border-color: #f9bc0b; }

.jq-icon-danger {
  background-color: #f36270;
  color: #fff;
  border-color: #f36270; }

.jq-icon-light {
  background-color: #f7f7f7;
  color: #fff;
  border-color: #f7f7f7; }

.jq-icon-dark {
  background-color: #323a46;
  color: #fff;
  border-color: #323a46; }

.jq-icon-pink {
  background-color: #e061c9;
  color: #fff;
  border-color: #e061c9; }

.jq-icon-purple {
  background-color: #9368f3;
  color: #fff;
  border-color: #9368f3; }

.jq-icon-error {
  background-color: #f36270;
  color: #fff;
  border-color: #f36270; }

.swal2-modal {
  font-family: "Nunito Sans", sans-serif;
  -webkit-box-shadow: 0 10px 33px rgba(0, 0, 0, 0.1);
          box-shadow: 0 10px 33px rgba(0, 0, 0, 0.1);
  border: 2px solid #348cd4; }
  .swal2-modal .swal2-title {
    font-size: 24px; }
  .swal2-modal .swal2-content {
    font-size: 16px; }
  .swal2-modal .swal2-spacer {
    margin: 10px 0; }
  .swal2-modal .swal2-file, .swal2-modal .swal2-input, .swal2-modal .swal2-textarea {
    border: 2px solid #dee2e6;
    font-size: 16px;
    -webkit-box-shadow: none;
            box-shadow: none; }
  .swal2-modal .swal2-confirm.btn-confirm {
    background-color: #348cd4 !important;
    font-size: 0.875rem; }
  .swal2-modal .swal2-cancel.btn-cancel {
    background-color: #f36270 !important;
    font-size: 0.875rem; }
  .swal2-modal .swal2-styled:focus {
    -webkit-box-shadow: none !important;
            box-shadow: none !important; }

.swal2-icon.swal2-question {
  color: #348cd4;
  border-color: #348cd4; }

.swal2-icon.swal2-success {
  border-color: #3ec396; }
  .swal2-icon.swal2-success .line, .swal2-icon.swal2-success [class^=swal2-success-line][class$=long],
  .swal2-icon.swal2-success [class^=swal2-success-line] {
    background-color: #3ec396; }
  .swal2-icon.swal2-success .placeholder, .swal2-icon.swal2-success .swal2-success-ring {
    border-color: #3ec396; }

.swal2-icon.swal2-warning {
  color: #f9bc0b;
  border-color: #f9bc0b; }

.swal2-icon.swal2-error {
  border-color: #f36270; }
  .swal2-icon.swal2-error .line {
    background-color: #f36270; }

.swal2-modal .swal2-file:focus, .swal2-modal .swal2-input:focus, .swal2-modal .swal2-textarea:focus {
  outline: 0;
  border: 2px solid #348cd4; }

.swal2-container.swal2-shown {
  background-color: rgba(255, 255, 255, 0.9); }

.flotTip {
  padding: 8px 12px;
  background-color: #fff;
  z-index: 99;
  color: #323a46;
  -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);
          box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);
  opacity: 1;
  border-radius: 3px; }

.legend tr {
  height: 30px;
  font-family: "Roboto", sans-serif; }

.legendLabel {
  padding-left: 5px !important;
  line-height: 10px;
  padding-right: 20px;
  font-size: 13px;
  font-weight: 500;
  color: #98a6ad;
  text-transform: uppercase; }

.legendColorBox div div {
  border-radius: 50%; }

@media (max-width: 767.98px) {
  .legendLabel {
    display: none; } }

.morris-chart text {
  font-family: "Roboto", sans-serif !important; }

.morris-hover {
  position: absolute;
  z-index: 10; }
  .morris-hover.morris-default-style {
    font-size: 12px;
    text-align: center;
    border-radius: 5px;
    padding: 10px 12px;
    background: #fff;
    color: #323a46;
    -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);
            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);
    font-family: "Nunito Sans", sans-serif; }
    .morris-hover.morris-default-style .morris-hover-row-label {
      font-weight: bold;
      margin: 0.25em 0;
      font-family: "Roboto", sans-serif; }
    .morris-hover.morris-default-style .morris-hover-point {
      white-space: nowrap;
      margin: 0.1em 0;
      color: #fff; }

.chartjs-chart {
  margin: auto;
  position: relative;
  width: 100%; }

.jqstooltip {
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  width: auto !important;
  height: auto !important;
  background-color: #fff !important;
  -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);
          box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);
  padding: 5px 10px !important;
  border-radius: 3px;
  border-color: #fff !important; }

.jqsfield {
  color: #323a46 !important;
  font-size: 12px !important;
  line-height: 18px !important;
  font-family: "Nunito Sans", sans-serif !important;
  font-weight: 700 !important; }

/* =============
   Maps
============= */
.gmaps,
.gmaps-panaroma {
  height: 300px;
  background: #eeeeee;
  border-radius: 3px; }

.gmaps-overlay {
  display: block;
  text-align: center;
  color: #fff;
  font-size: 16px;
  line-height: 40px;
  background: #348cd4;
  border-radius: 4px;
  padding: 10px 20px; }

.gmaps-overlay_arrow {
  left: 50%;
  margin-left: -16px;
  width: 0;
  height: 0;
  position: absolute; }

.gmaps-overlay_arrow.above {
  bottom: -15px;
  border-left: 16px solid transparent;
  border-right: 16px solid transparent;
  border-top: 16px solid #348cd4; }

.gmaps-overlay_arrow.below {
  top: -15px;
  border-left: 16px solid transparent;
  border-right: 16px solid transparent;
  border-bottom: 16px solid #348cd4; }

.gmaps-full {
  z-index: 99;
  margin: 0 -20px -10px -20px; }
  .gmaps-full .gmaps-full1 {
    height: 80vh;
    width: 100%; }

.jvectormap-label {
  border: none;
  background: #323a46;
  color: #fff;
  font-family: "Roboto", sans-serif;
  font-size: 0.875rem;
  padding: 5px 8px; }

/* Mapael Map */
.mapael .map {
  position: relative; }
  .mapael .map .zoomIn {
    top: 25px; }
  .mapael .map .zoomOut {
    top: 50px; }

.mapael .mapTooltip {
  position: absolute;
  background-color: #348cd4;
  opacity: 0.95;
  border-radius: 3px;
  padding: 2px 10px;
  z-index: 1000;
  max-width: 200px;
  display: none;
  color: #fff;
  font-family: "Roboto", sans-serif; }

.mapael .zoomIn,
.mapael .zoomOut,
.mapael .zoomReset {
  display: inline-block;
  text-align: center;
  vertical-align: middle;
  border-radius: 2px;
  font-weight: 500;
  cursor: pointer;
  background-color: #348cd4;
  text-decoration: none;
  color: #fff;
  font-size: 14px;
  position: absolute;
  top: 0;
  left: 10px;
  width: 24px;
  height: 24px;
  line-height: 24px; }

.mapael .plotLegend text {
  font-family: "Nunito Sans", sans-serif !important; }

.calendar {
  float: left;
  margin-bottom: 0; }

.fc-view {
  margin-top: 30px; }

.none-border .modal-footer {
  border-top: none; }

.fc-toolbar {
  margin: 15px 0 5px 0; }
  .fc-toolbar h2 {
    font-size: 1.25rem;
    line-height: 1.875rem;
    text-transform: uppercase; }

.fc-day-grid-event .fc-time {
  font-weight: 700; }

.fc-day {
  background: #fff; }

.fc-toolbar .fc-state-active,
.fc-toolbar .ui-state-active,
.fc-toolbar button:focus,
.fc-toolbar button:hover,
.fc-toolbar .ui-state-hover {
  z-index: 0; }

.fc th.fc-widget-header {
  background: #f1f5f7;
  font-size: 13px;
  line-height: 20px;
  padding: 10px 0;
  text-transform: uppercase; }

.fc-unthemed th,
.fc-unthemed td,
.fc-unthemed thead,
.fc-unthemed tbody,
.fc-unthemed .fc-divider,
.fc-unthemed .fc-row,
.fc-unthemed .fc-popover {
  border-color: #dee2e6; }

.fc-ltr .fc-basic-view .fc-day-top .fc-day-number {
  float: right;
  margin: 5px;
  font-family: "Roboto", sans-serif;
  font-size: 12px; }

.fc-button {
  background: #f1f5f7;
  border: none;
  color: #6c757d;
  text-transform: capitalize;
  -webkit-box-shadow: none;
          box-shadow: none;
  border-radius: 3px;
  margin: 0 3px;
  padding: 6px 12px;
  height: auto; }

.fc-text-arrow {
  font-family: inherit;
  font-size: 1rem; }

.fc-state-hover {
  background: #f1f5f7; }

.fc-state-highlight {
  background: #dee2e6; }

.fc-state-down,
.fc-state-active,
.fc-state-disabled {
  background: #348cd4;
  color: #fff;
  text-shadow: none; }

.fc-cell-overlay {
  background: #dee2e6; }

.fc-unthemed .fc-today {
  background: #fff; }

.fc-event {
  border-radius: 2px;
  border: none;
  cursor: move;
  font-size: 0.8125rem;
  margin: 5px 7px;
  padding: 5px 5px;
  text-align: center; }

.external-event {
  cursor: move;
  margin: 10px 0;
  padding: 8px 10px;
  color: #fff; }

.fc-basic-view td.fc-week-number span {
  padding-right: 8px; }

.fc-basic-view td.fc-day-number {
  padding-right: 8px; }

.fc-basic-view .fc-content {
  color: #fff; }

.fc-time-grid-event .fc-content {
  color: #fff; }

@media (max-width: 767.98px) {
  .fc-toolbar .fc-left, .fc-toolbar .fc-right, .fc-toolbar .fc-center {
    float: none;
    display: block;
    clear: both;
    margin: 10px 0; }
  .fc .fc-toolbar > * > * {
    float: none; }
  .fc-today-button {
    display: none; } }

/* =============
   Summernote
============= */
@font-face {
  font-family: "summernote";
  font-style: normal;
  font-weight: normal;
  src: url("../fonts/summernote.eot");
  src: url("../fonts/summernote.eot?#iefix") format("embedded-opentype"), url("../fonts/summernote.woff?") format("woff"), url("../fonts/summernote.ttf?") format("truetype"); }

.note-editor.note-frame {
  border: 1px solid #ced4da;
  -webkit-box-shadow: none;
          box-shadow: none;
  margin: 0; }
  .note-editor.note-frame .note-statusbar {
    background-color: #fcfcfc;
    border-top: 1px solid #f7f7f7; }
  .note-editor.note-frame .note-editable {
    border: none; }

.note-status-output {
  display: none; }

.note-editable {
  border: none;
  border-radius: 0.2rem;
  padding: 0.45rem 0.9rem; }
  .note-editable p:last-of-type {
    margin-bottom: 0; }

.note-popover .popover-content .note-color .dropdown-menu,
.card-header.note-toolbar .note-color .dropdown-menu {
  min-width: 344px; }

.note-toolbar {
  z-index: 1; }

.select2-container .select2-selection--single {
  border: 1px solid #ced4da;
  height: 38px;
  outline: none; }
  .select2-container .select2-selection--single .select2-selection__rendered {
    line-height: 36px;
    padding-left: 12px; }
  .select2-container .select2-selection--single .select2-selection__arrow {
    height: 34px;
    width: 34px;
    right: 3px; }
    .select2-container .select2-selection--single .select2-selection__arrow b {
      border-color: #d1d1d1 transparent transparent transparent;
      border-width: 6px 6px 0 6px; }

.select2-container--open .select2-selection--single .select2-selection__arrow b {
  border-color: transparent transparent #d1d1d1 transparent !important;
  border-width: 0 6px 6px 6px !important; }

.select2-results__option {
  padding: 6px 12px; }

.select2-dropdown {
  border: 1px solid #eaeaea;
  -webkit-box-shadow: 0px 0px 35px 0px rgba(154, 161, 171, 0.15);
          box-shadow: 0px 0px 35px 0px rgba(154, 161, 171, 0.15); }

.select2-container--default .select2-search--dropdown {
  padding: 10px;
  background-color: white; }
  .select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #eaeaea;
    outline: none; }

.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background: #348cd4; }

.select2-container--default .select2-results__option[aria-selected=true] {
  background-color: #f7f7f7;
  color: #323a46; }
  .select2-container--default .select2-results__option[aria-selected=true]:hover {
    background: #348cd4;
    color: #fff; }

.select2-container .select2-selection--multiple {
  min-height: 38px;
  border: 1px solid #ced4da !important; }
  .select2-container .select2-selection--multiple .select2-selection__rendered {
    padding: 1px 10px; }
  .select2-container .select2-selection--multiple .select2-search__field {
    border: 0; }
  .select2-container .select2-selection--multiple .select2-selection__choice {
    background: #348cd4;
    border: none;
    color: #fff;
    border-radius: 3px;
    padding: 0 7px;
    margin-top: 7px; }
  .select2-container .select2-selection--multiple .select2-selection__choice__remove {
    color: #fff;
    margin-right: 5px; }
    .select2-container .select2-selection--multiple .select2-selection__choice__remove:hover {
      color: #fff; }

.autocomplete-suggestions {
  border: 1px solid #f9f9f9;
  background: #fff;
  cursor: default;
  overflow: auto;
  max-height: 200px !important;
  -webkit-box-shadow: 0 2px 2px rgba(0, 0, 0, 0.15);
          box-shadow: 0 2px 2px rgba(0, 0, 0, 0.15); }
  .autocomplete-suggestions strong {
    font-weight: bold;
    color: #323a46; }

.autocomplete-suggestion {
  padding: 5px 10px;
  white-space: nowrap;
  overflow: hidden; }

.autocomplete-no-suggestion {
  padding: 5px; }

.autocomplete-selected {
  background: #f7f7f7;
  cursor: pointer; }

.autocomplete-group {
  padding: 5px;
  font-weight: 500;
  font-family: "Roboto", sans-serif; }
  .autocomplete-group strong {
    font-weight: bold;
    font-size: 16px;
    color: #323a46;
    display: block; }

/* Bootstrap tagsinput */
.bootstrap-tagsinput {
  -webkit-box-shadow: none;
          box-shadow: none;
  padding: 4px 7px 4px;
  border: 1px solid #efefef;
  width: 100%; }
  .bootstrap-tagsinput .label-info {
    background: #348cd4;
    display: inline-block;
    font-size: 11px;
    margin: 3px 1px;
    padding: 0 5px;
    border-radius: 3px; }

.bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
  width: 100% !important; }

.bootstrap-select .dropdown-menu .dropdown-menu li a {
  display: block;
  width: 100%;
  clear: both;
  font-weight: 400;
  color: #323a46;
  text-align: inherit;
  white-space: nowrap;
  background: 0 0;
  border: 0; }
  .bootstrap-select .dropdown-menu .dropdown-menu li a:hover {
    background: #348cd4;
    color: #fff; }

.bootstrap-select .dropdown-toggle:after {
  content: "\F140";
  display: inline-block;
  font-family: "Material Design Icons"; }

.bootstrap-select .dropdown-toggle:focus {
  outline: none !important;
  outline-offset: 0; }

.bootstrap-select a {
  outline: none !important; }

.bootstrap-select .inner {
  overflow-y: inherit !important; }

.bootstrap-select > .btn-pink.bs-placeholder {
  color: #fff !important; }

.parsley-errors-list {
  margin: 0;
  padding: 0; }
  .parsley-errors-list > li {
    list-style: none;
    color: #f36270;
    margin-top: 5px;
    padding-left: 20px;
    position: relative; }
    .parsley-errors-list > li:before {
      content: "\F159";
      font-family: "Material Design Icons";
      position: absolute;
      left: 2px;
      top: -1px; }

.parsley-error {
  border-color: #f36270; }

.parsley-success {
  border-color: #3ec396; }

.bootstrap-timepicker-widget table td input {
  width: 35px;
  border: 0px; }

.bootstrap-timepicker-widget table td a:hover {
  background-color: transparent;
  border: 1px solid transparent; }

/* Daterange Picker */
.daterangepicker td.active, .daterangepicker td.active:hover {
  background: #348cd4;
  background: #348cd4; }

.daterangepicker .input-mini.active {
  border: 1px solid rgba(50, 58, 70, 0.3); }

.daterangepicker .ranges li {
  border-radius: 2px;
  color: #323a46;
  font-weight: 600;
  font-size: 12px; }

.daterangepicker select.hourselect, .daterangepicker select.minuteselect,
.daterangepicker select.secondselect, .daterangepicker select.ampmselect {
  border: 1px solid rgba(50, 58, 70, 0.3);
  padding: 2px;
  width: 60px; }

.daterangepicker .ranges li.active, .daterangepicker .ranges li:hover {
  background: #348cd4;
  background: #348cd4;
  border: 1px solid #348cd4;
  color: #fff; }

.daterangepicker select.monthselect, .daterangepicker select.yearselect {
  border-color: #98a6ad; }

/* Clock picker */
.clockpicker-canvas line {
  stroke: #73b0e1; }

.clockpicker-canvas-bearing, .clockpicker-canvas-fg, .clockpicker-canvas-bg {
  fill: #73b0e1; }

.clockpicker-popover .btn-default {
  background-color: #348cd4;
  color: #fff; }

.wizard > .steps {
  position: relative;
  display: block;
  width: 100%; }
  .wizard > .steps > ul > li {
    width: 25%; }
  .wizard > .steps a {
    font-size: 16px;
    margin: 0 0.5em 0.5em; }
  .wizard > .steps .number {
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.3);
    display: inline-block;
    line-height: 30px;
    margin-right: 10px;
    width: 30px;
    text-align: center;
    font-size: 1.429em; }
  .wizard > .steps a, .wizard > .steps a:hover, .wizard > .steps a:active {
    display: block;
    width: auto;
    padding: 1em 1em;
    text-decoration: none;
    border-radius: 2px; }
  .wizard > .steps .disabled a {
    border: 1px solid #ededed;
    color: #323a46;
    cursor: default; }
  .wizard > .steps .current a, .wizard > .steps .current a:hover, .wizard > .steps .current a:active {
    background: #348cd4;
    color: #fff;
    cursor: default; }
    .wizard > .steps .current a .number, .wizard > .steps .current a:hover .number, .wizard > .steps .current a:active .number {
      color: #fff; }
  .wizard > .steps .done a, .wizard > .steps .done a:hover, .wizard > .steps .done a:active {
    background: #88bce6;
    color: #fff; }
  .wizard > .steps .error a, .wizard > .steps .error a:hover, .wizard > .steps .error a:active {
    background: snow;
    color: #fff;
    border-color: #fde7e9; }

.wizard > .steps > ul > li, .wizard > .actions > ul > li {
  float: left;
  position: relative; }

.wizard > .content {
  display: block;
  min-height: 240px;
  overflow: hidden;
  position: relative;
  width: auto;
  padding: 20px; }
  .wizard > .content > .body {
    padding: 0;
    position: relative;
    width: 95%; }
    .wizard > .content > .body ul {
      list-style: disc !important; }
      .wizard > .content > .body ul > li {
        display: block;
        line-height: 30px; }
    .wizard > .content > .body > iframe {
      border: 0 none;
      width: 100%;
      height: 100%; }
    .wizard > .content > .body input {
      display: block;
      border-color: #dee2e6; }
      .wizard > .content > .body input:focus {
        border-color: #dee2e6; }
      .wizard > .content > .body input[type="checkbox"] {
        display: inline-block; }
      .wizard > .content > .body input.error {
        background: white;
        border: 1px solid snow;
        color: #f36270; }
    .wizard > .content > .body label {
      display: inline-block;
      margin-bottom: 0.5em;
      margin-top: 10px; }
      .wizard > .content > .body label.error {
        color: #f36270;
        font-size: 12px; }

.wizard > .actions {
  position: relative;
  display: block;
  text-align: right;
  width: 100%;
  margin-top: 15px; }
  .wizard > .actions > ul {
    display: inline-block;
    text-align: right; }
    .wizard > .actions > ul > li {
      margin: 0 0.5em; }
  .wizard > .actions a, .wizard > .actions a:hover, .wizard > .actions a:active {
    background: #348cd4;
    color: #fff;
    display: block;
    padding: 0.5em 1em;
    text-decoration: none;
    border-radius: 2px; }
  .wizard > .actions .disabled a, .wizard > .actions .disabled a:hover, .wizard > .actions .disabled a:active {
    background: #fff;
    color: #323a46;
    cursor: default;
    border: 1px solid #f7f7f7; }

.wizard.vertical > .steps {
  display: inline;
  float: left;
  width: 30%; }
  .wizard.vertical > .steps > ul > li {
    float: none;
    width: 100%; }

.wizard.vertical > .content {
  width: 65%;
  margin: 0 2.5% 0.5em;
  display: inline;
  float: left; }

.wizard.vertical > .actions {
  display: inline;
  float: right;
  width: 95%;
  margin: 0 2.5%;
  margin-top: 15px !important; }
  .wizard.vertical > .actions > ul > li {
    margin: 0 0 0 1em; }

/*
  Common 
*/
.wizard, .tabcontrol {
  display: block;
  width: 100%;
  overflow: hidden;
  /* Accessibility */ }
  .wizard a, .tabcontrol a {
    outline: 0; }
  .wizard ul, .tabcontrol ul {
    list-style: none !important;
    padding: 0;
    margin: 0; }
    .wizard ul > li, .tabcontrol ul > li {
      display: block;
      padding: 0; }
  .wizard > .steps .current-info, .tabcontrol > .steps .current-info {
    position: absolute;
    left: -999em; }
  .wizard > .content > .title, .tabcontrol > .content > .title {
    position: absolute;
    left: -999em; }

@media (max-width: 767.98px) {
  .wizard > .steps > ul > li, .wizard.vertical > .steps, .wizard.vertical > .content {
    width: 100%; } }

.editable-clear-x {
  background: url("../images/clear.png") center center no-repeat; }

.editableform-loading {
  background: url("../images/loading.gif") center center no-repeat; }

.editable-checklist label {
  display: block; }

.dropzone {
  border: 2px dashed rgba(50, 58, 70, 0.3);
  background: #fff;
  border-radius: 6px; }

.dataTables_wrapper.container-fluid {
  padding: 0; }

table.dataTable {
  border-collapse: collapse !important;
  margin-bottom: 15px !important; }
  table.dataTable tbody > tr.selected, table.dataTable tbody > tr > .selected {
    background-color: #348cd4; }
    table.dataTable tbody > tr.selected td, table.dataTable tbody > tr > .selected td {
      border-color: #348cd4; }
  table.dataTable tbody td:focus {
    outline: none !important; }
  table.dataTable tbody th.focus, table.dataTable tbody td.focus {
    outline: 2px solid #348cd4 !important;
    outline-offset: -1px;
    color: #348cd4;
    background-color: rgba(52, 140, 212, 0.15); }

.dataTables_info {
  font-weight: 600; }

table.dataTable.dtr-inline.collapsed > tbody > tr[role=row] > td:first-child:before, table.dataTable.dtr-inline.collapsed > tbody > tr[role=row] > th:first-child:before {
  -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);
          box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);
  background-color: #348cd4;
  top: 0.85rem; }

table.dataTable.dtr-inline.collapsed > tbody > tr.parent > td:first-child:before, table.dataTable.dtr-inline.collapsed > tbody > tr.parent > th:first-child:before {
  background-color: #f36270;
  top: 0.85rem; }

div.dt-button-info {
  background-color: #348cd4;
  border: none;
  color: #fff;
  -webkit-box-shadow: none;
          box-shadow: none;
  border-radius: 3px;
  text-align: center;
  z-index: 21; }
  div.dt-button-info h2 {
    border-bottom: none;
    background-color: rgba(255, 255, 255, 0.2);
    color: #fff; }

@media (max-width: 767.98px) {
  li.paginate_button.previous, li.paginate_button.next {
    display: inline-block; }
  li.paginate_button {
    display: none; }
  .dataTables_paginate ul {
    text-align: center;
    display: block;
    margin: 1.5rem 0 0 !important; }
  div.dt-buttons {
    display: inline-table;
    margin-bottom: 1.5rem; } }

.activate-select .sorting_1 {
  background-color: #f1f5f7; }

.table-rep-plugin .dropdown-menu li.checkbox-row {
  padding: 7px 15px; }

.table-rep-plugin .table-responsive {
  border: none; }

.table-rep-plugin tbody th {
  font-size: 14px;
  font-weight: normal; }

.table-rep-plugin .checkbox-row {
  padding-left: 40px; }
  .table-rep-plugin .checkbox-row label {
    display: inline-block;
    padding-left: 5px;
    position: relative;
    margin-bottom: 0; }
    .table-rep-plugin .checkbox-row label::before {
      -o-transition: 0.3s ease-in-out;
      -webkit-transition: 0.3s ease-in-out;
      background-color: #fff;
      border-radius: 3px;
      border: 1px solid #98a6ad;
      content: "";
      display: inline-block;
      height: 17px;
      left: 0;
      margin-left: -20px;
      position: absolute;
      transition: 0.3s ease-in-out;
      width: 17px;
      outline: none; }
    .table-rep-plugin .checkbox-row label::after {
      color: #dee2e6;
      display: inline-block;
      font-size: 11px;
      height: 16px;
      left: 0;
      margin-left: -20px;
      padding-left: 3px;
      padding-top: 1px;
      position: absolute;
      top: -1px;
      width: 16px; }
  .table-rep-plugin .checkbox-row input[type="checkbox"] {
    cursor: pointer;
    opacity: 0;
    z-index: 1;
    outline: none; }
    .table-rep-plugin .checkbox-row input[type="checkbox"]:disabled + label {
      opacity: 0.65; }
  .table-rep-plugin .checkbox-row input[type="checkbox"]:focus + label::before {
    outline-offset: -2px;
    outline: none; }
  .table-rep-plugin .checkbox-row input[type="checkbox"]:checked + label::after {
    content: "\f00c";
    font-family: 'Font Awesome 5 Free';
    font-weight: 900; }
  .table-rep-plugin .checkbox-row input[type="checkbox"]:disabled + label::before {
    background-color: #f7f7f7;
    cursor: not-allowed; }
  .table-rep-plugin .checkbox-row input[type="checkbox"]:checked + label::before {
    background-color: #fff;
    border-color: #348cd4; }
  .table-rep-plugin .checkbox-row input[type="checkbox"]:checked + label::after {
    color: #348cd4; }

.table-rep-plugin table.focus-on tbody tr.focused th, .table-rep-plugin table.focus-on tbody tr.focused td,
.table-rep-plugin .sticky-table-header {
  background: #348cd4;
  background: #348cd4;
  color: #fff;
  border-color: #348cd4; }
  .table-rep-plugin table.focus-on tbody tr.focused th table, .table-rep-plugin table.focus-on tbody tr.focused td table,
  .table-rep-plugin .sticky-table-header table {
    color: #fff; }

.table-rep-plugin .fixed-solution .sticky-table-header {
  top: 70px !important; }

.table-rep-plugin .btn-default {
  background-color: #fff;
  border: 1px solid rgba(50, 58, 70, 0.3);
  color: #323a46; }

.table-rep-plugin .btn-primary {
  background: #348cd4;
  border-color: #348cd4;
  color: #fff; }

.table-rep-plugin .btn-group.pull-right {
  float: right; }
  .table-rep-plugin .btn-group.pull-right .dropdown-menu {
    left: auto;
    right: 0; }

.table-rep-plugin .btn-toolbar {
  display: block; }

.tablesaw thead {
  background: #f1f5f7;
  background-image: none;
  border: none; }
  .tablesaw thead th {
    text-shadow: none; }
  .tablesaw thead tr:first-child th {
    border: none;
    font-weight: 500;
    font-family: "Roboto", sans-serif; }

.tablesaw td {
  border-top: 1px solid #f1f5f7 !important; }

.tablesaw td,
.tablesaw tbody th {
  font-size: inherit;
  line-height: inherit;
  padding: 10px !important; }

.tablesaw-stack tbody tr,
.tablesaw tbody tr {
  border-bottom: none; }

.tablesaw-bar .btn-select.btn-small:after,
.tablesaw-bar .btn-select.btn-micro:after {
  font-size: 8px;
  padding-right: 10px; }

.tablesaw-swipe .tablesaw-cell-persist {
  -webkit-box-shadow: none;
          box-shadow: none;
  border-color: #f1f5f7; }

.tablesaw-enhanced .tablesaw-bar .btn {
  text-shadow: none;
  background-image: none;
  text-transform: none;
  border: 1px solid #dee2e6;
  padding: 3px 10px;
  color: #323a46; }
  .tablesaw-enhanced .tablesaw-bar .btn:after {
    display: none; }

.tablesaw-enhanced .tablesaw-bar .btn.btn-select:hover {
  background: #fff; }

.tablesaw-enhanced .tablesaw-bar .btn:hover,
.tablesaw-enhanced .tablesaw-bar .btn:focus,
.tablesaw-enhanced .tablesaw-bar .btn:active {
  color: #348cd4 !important;
  background-color: #f1f5f7;
  outline: none !important;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
  background-image: none; }

.tablesaw-columntoggle-popup .btn-group {
  display: block; }

.tablesaw-swipe .tablesaw-swipe-cellpersist {
  border-right: 2px solid #f1f5f7; }

.tablesaw-sortable-btn {
  cursor: pointer; }

.tablesaw-swipe-cellpersist {
  width: auto !important; }

.button-list {
  margin-left: -8px;
  margin-bottom: -12px; }
  .button-list .btn {
    margin-bottom: 12px;
    margin-left: 8px; }

.icons-list-demo div {
  cursor: pointer;
  line-height: 45px;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: block;
  overflow: hidden; }
  .icons-list-demo div p {
    margin-bottom: 0;
    line-height: inherit; }

.icons-list-demo i {
  text-align: center;
  vertical-align: middle;
  font-size: 22px;
  width: 50px;
  height: 50px;
  line-height: 50px;
  margin-right: 12px;
  color: rgba(50, 58, 70, 0.7);
  border-radius: 3px;
  display: inline-block;
  -webkit-transition: all 0.2s;
  transition: all 0.2s; }

.icons-list-demo .col-lg-4 {
  background-clip: padding-box;
  margin-top: 10px; }
  .icons-list-demo .col-lg-4:hover i {
    color: #fff;
    background: #348cd4; }

.grid-structure .grid-container {
  background-color: #f1f5f7;
  margin-top: 10px;
  font-size: .8rem;
  font-weight: 500;
  padding: 10px 20px; }

.home-btn {
  position: absolute;
  top: 15px;
  right: 25px; }

.checkmark {
  width: 100px;
  margin: 0 auto;
  padding: 20px 0; }

.path {
  stroke-dasharray: 1000;
  stroke-dashoffset: 0;
  animation: dash 2s ease-in-out;
  -webkit-animation: dash 2s ease-in-out; }

.spin {
  animation: spin 2s;
  -webkit-animation: spin 2s;
  transform-origin: 50% 50%;
  -webkit-transform-origin: 50% 50%; }

@keyframes dash {
  0% {
    stroke-dashoffset: 1000; }
  100% {
    stroke-dashoffset: 0; } }

@keyframes spin {
  0% {
    -webkit-transform: rotate(0deg); }
  100% {
    -webkit-transform: rotate(360deg); } }

/* =============
   Email
============= */
.inbox-leftbar {
  width: 240px;
  float: left;
  padding: 0 20px 20px 10px; }

.inbox-rightbar {
  margin-left: 250px; }

.message-list {
  display: block;
  padding-left: 0; }
  .message-list li {
    position: relative;
    display: block;
    height: 50px;
    line-height: 50px;
    cursor: default;
    -webkit-transition-duration: .3s;
            transition-duration: .3s; }
    .message-list li a {
      color: #6c757d; }
    .message-list li:hover {
      background: rgba(152, 166, 173, 0.15);
      -webkit-transition-duration: .05s;
              transition-duration: .05s; }
    .message-list li .col-mail {
      float: left;
      position: relative; }
    .message-list li .col-mail-1 {
      width: 320px; }
      .message-list li .col-mail-1 .star-toggle,
      .message-list li .col-mail-1 .checkbox-wrapper-mail,
      .message-list li .col-mail-1 .dot {
        display: block;
        float: left; }
      .message-list li .col-mail-1 .dot {
        border: 4px solid transparent;
        border-radius: 100px;
        margin: 22px 26px 0;
        height: 0;
        width: 0;
        line-height: 0;
        font-size: 0; }
      .message-list li .col-mail-1 .checkbox-wrapper-mail {
        margin: 15px 10px 0 20px; }
      .message-list li .col-mail-1 .star-toggle {
        margin-top: 18px;
        margin-left: 5px; }
      .message-list li .col-mail-1 .title {
        position: absolute;
        top: 15px;
        left: 110px;
        right: 0;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap; }
    .message-list li .col-mail-2 {
      position: absolute;
      top: 0;
      left: 320px;
      right: 0;
      bottom: 0; }
      .message-list li .col-mail-2 .subject,
      .message-list li .col-mail-2 .date {
        position: absolute;
        top: 0; }
      .message-list li .col-mail-2 .subject {
        left: 0;
        right: 200px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap; }
      .message-list li .col-mail-2 .date {
        right: 0;
        width: 170px;
        padding-left: 80px; }
  .message-list li.active, .message-list li.selected {
    background: rgba(152, 166, 173, 0.15);
    -webkit-transition-duration: .05s;
            transition-duration: .05s; }
  .message-list li.active,
  .message-list li.active:hover {
    -webkit-box-shadow: inset 3px 0 0 #348cd4;
            box-shadow: inset 3px 0 0 #348cd4; }
  .message-list li.unread a {
    font-weight: 600;
    color: #272e37; }
  .message-list li.blue-dot .col-mail-1 .dot {
    border-color: #348cd4; }
  .message-list li.orange-dot .col-mail-1 .dot {
    border-color: #f9bc0b; }
  .message-list li.green-dot .col-mail-1 .dot {
    border-color: #3ec396; }
  .message-list .checkbox-wrapper-mail {
    cursor: pointer;
    height: 20px;
    width: 20px;
    position: relative;
    display: inline-block;
    -webkit-box-shadow: inset 0 0 0 1px #98a6ad;
            box-shadow: inset 0 0 0 1px #98a6ad;
    border-radius: 1px; }
    .message-list .checkbox-wrapper-mail input {
      opacity: 0;
      cursor: pointer; }
    .message-list .checkbox-wrapper-mail input:checked ~ label {
      opacity: 1; }
    .message-list .checkbox-wrapper-mail label {
      position: absolute;
      top: 3px;
      left: 3px;
      right: 3px;
      bottom: 3px;
      cursor: pointer;
      background: #98a6ad;
      opacity: 0;
      margin-bottom: 0 !important;
      -webkit-transition-duration: .05s;
              transition-duration: .05s; }
    .message-list .checkbox-wrapper-mail label:active {
      background: #87949b; }

.mail-list a {
  font-family: "Roboto", sans-serif;
  vertical-align: middle;
  color: #6c757d;
  padding: 10px 12px;
  display: block; }

@media (max-width: 648px) {
  .inbox-leftbar {
    width: 100%; }
  .inbox-rightbar {
    margin-left: 0; } }

@media (max-width: 520px) {
  .message-list li .col-mail-1 {
    width: 150px; }
    .message-list li .col-mail-1 .title {
      left: 80px; }
  .message-list li .col-mail-2 {
    left: 160px; }
    .message-list li .col-mail-2 .date {
      text-align: right;
      padding-right: 10px;
      padding-left: 20px; } }

/* =============
   Timeline
============= */
.timeline {
  border-collapse: collapse;
  border-spacing: 0;
  display: table;
  margin-bottom: 50px;
  position: relative;
  table-layout: fixed;
  width: 100%; }
  .timeline .time-show {
    margin-bottom: 30px;
    margin-right: -75px;
    margin-top: 30px;
    position: relative; }
    .timeline .time-show a {
      color: #fff; }
  .timeline:before {
    background-color: rgba(65, 77, 95, 0.3);
    bottom: 0;
    content: "";
    left: 50%;
    position: absolute;
    top: 30px;
    width: 2px;
    z-index: 0; }
  .timeline .timeline-icon {
    -webkit-border-radius: 50%;
    background: #414d5f;
    border-radius: 50%;
    color: #fff;
    display: block;
    height: 20px;
    left: -54px;
    margin-top: -10px;
    position: absolute;
    text-align: center;
    top: 50%;
    width: 20px; }
    .timeline .timeline-icon i {
      color: #fff;
      font-size: 13px;
      margin-top: 1px;
      position: absolute;
      left: 4px; }
  .timeline .time-icon:before {
    font-size: 16px;
    margin-top: 5px; }

h3.timeline-title {
  color: #414d5f;
  font-size: 20px;
  font-weight: 400;
  margin: 0 0 5px;
  text-transform: uppercase; }

.timeline-item {
  display: table-row; }
  .timeline-item:before {
    content: "";
    display: block;
    width: 50%; }
  .timeline-item .timeline-desk .arrow {
    border-bottom: 12px solid transparent;
    border-right: 12px solid rgba(247, 247, 247, 0.3) !important;
    border-top: 12px solid transparent;
    display: block;
    height: 0;
    left: -12px;
    margin-top: -12px;
    position: absolute;
    top: 50%;
    width: 0; }
  .timeline-item .timeline-desk .timeline-box {
    padding: 20px; }
  .timeline-item .timeline-date {
    margin-bottom: 10px; }

.timeline-item.alt:after {
  content: "";
  display: block;
  width: 50%; }

.timeline-item.alt .timeline-desk .arrow-alt {
  border-bottom: 12px solid transparent;
  border-left: 12px solid rgba(247, 247, 247, 0.9) !important;
  border-top: 12px solid transparent;
  display: block;
  height: 0;
  left: auto;
  margin-top: -12px;
  position: absolute;
  right: -12px;
  top: 50%;
  width: 0; }

.timeline-item.alt .timeline-desk .album {
  float: right;
  margin-top: 20px; }
  .timeline-item.alt .timeline-desk .album a {
    float: right;
    margin-left: 5px; }

.timeline-item.alt .timeline-icon {
  left: auto;
  right: -56px; }

.timeline-item.alt:before {
  display: none; }

.timeline-item.alt .panel {
  margin-left: 0;
  margin-right: 45px; }

.timeline-item.alt h4 {
  text-align: right; }

.timeline-item.alt p {
  text-align: right; }

.timeline-item.alt .timeline-date {
  text-align: right; }

.timeline-desk {
  display: table-cell;
  vertical-align: top;
  width: 50%; }
  .timeline-desk h4 {
    font-size: 16px;
    margin: 0; }
  .timeline-desk .panel {
    background: rgba(247, 247, 247, 0.9);
    display: block;
    margin-bottom: 5px;
    margin-left: 45px;
    position: relative;
    text-align: left;
    border: 0; }
  .timeline-desk h5 span {
    color: #414d5f;
    display: block;
    font-size: 12px;
    margin-bottom: 4px; }
  .timeline-desk p {
    color: #999999;
    font-size: 14px;
    margin-bottom: 0; }
  .timeline-desk .album {
    margin-top: 12px; }
    .timeline-desk .album a {
      float: left;
      margin-right: 5px; }
    .timeline-desk .album img {
      height: 36px;
      width: auto;
      border-radius: 3px; }
  .timeline-desk .notification {
    background: none repeat scroll 0 0 #fff;
    margin-top: 20px;
    padding: 8px; }

/* =============
   Account Pages
============= */
.home-wrapper {
  margin: 10% 0; }

.bg-accpunt-pages {
  background-color: #348cd4;
  background: #348cd4;
  padding-bottom: 0;
  min-height: 100px; }

.wrapper-page {
  display: table;
  height: 100vh;
  width: 100%; }

.account-box {
  position: relative;
  max-width: 460px;
  margin: 20px auto;
  background-color: #fff;
  border-radius: 5px; }
  .account-box .account-content {
    padding: 30px; }
  .account-box .account-btn {
    position: absolute;
    left: 0;
    right: 0; }

.account-logo-box {
  padding: 30px 30px 0 30px; }

.text-error {
  color: #348cd4;
  text-shadow: rgba(52, 140, 212, 0.3) 5px 1px, rgba(52, 140, 212, 0.2) 10px 3px;
  font-size: 84px;
  font-weight: 700;
  line-height: 90px; }

.checkmark {
  width: 100px;
  margin: 0 auto;
  padding: 20px 0; }

.path {
  stroke-dasharray: 1000;
  stroke-dashoffset: 0;
  animation: dash 2s ease-in-out;
  -webkit-animation: dash 2s ease-in-out; }

.spin {
  animation: spin 2s;
  -webkit-animation: spin 2s;
  transform-origin: 50% 50%;
  -webkit-transform-origin: 50% 50%; }

@-webkit-keyframes dash {
  0% {
    stroke-dashoffset: 1000; }
  100% {
    stroke-dashoffset: 0; } }

@keyframes dash {
  0% {
    stroke-dashoffset: 1000; }
  100% {
    stroke-dashoffset: 0; } }

@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg); }
  100% {
    -webkit-transform: rotate(360deg); } }

@keyframes spin {
  0% {
    -webkit-transform: rotate(0deg); }
  100% {
    -webkit-transform: rotate(360deg); } }

@-webkit-keyframes text {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }

@keyframes text {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }

/* =============
   Pricing
============= */
.pricing-column .ribbon-pricing {
  width: 160px;
  margin: -15px auto -10px;
  padding-bottom: 2px;
  line-height: 22px;
  text-align: center;
  z-index: 1;
  position: relative; }

.pricing-column .plan-title {
  font-family: "Roboto", sans-serif;
  letter-spacing: 1px; }

.pricing-column .plan-price {
  font-size: 48px;
  font-family: "Roboto", sans-serif; }

.pricing-column .plan-duration {
  font-size: 15px;
  color: rgba(255, 255, 255, 0.7); }

.pricing-column .plan-stats {
  padding: 30px 20px 15px; }
  .pricing-column .plan-stats li {
    margin-bottom: 15px;
    line-height: 24px; }

html {
  direction: rtl; }

body {
  text-align: right; }

.dropdown-menu.show {
  text-align: right;
  left: auto !important;
  right: 0;
  bottom: auto; }

.dropdown-menu-right {
  right: auto !important;
  left: 0 !important; }
  .dropdown-menu-right.show {
    left: 0 !important; }

.btn-group, .btn-group-vertical {
  direction: ltr; }

ul {
  padding-right: 0; }

.pagination .page-item:first-child .page-link {
  margin-right: 0;
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem; }

.pagination .page-item:last-child .page-link {
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem; }

.blockquote-reverse {
  text-align: left !important; }

dd {
  margin-right: 0; }

.modal-header .close {
  margin: -1rem auto -1rem -1rem;
  left: 0px; }

.modal-footer > :not(:first-child) {
  margin-right: .25rem;
  margin-left: 0; }

.modal-footer > :not(:last-child) {
  margin-left: .25rem;
  margin-right: 0; }

.alert-dismissible {
  padding-left: 3.9rem;
  padding-right: 1.25rem; }
  .alert-dismissible .close {
    left: 0;
    right: auto; }

.breadcrumb-item + .breadcrumb-item {
  padding-right: 0.5rem;
  padding-left: 0px; }
  .breadcrumb-item + .breadcrumb-item::before {
    padding-left: 0.5rem;
    content: "\F141";
    padding-right: 0px; }

.form-check-inline {
  margin-left: .75rem;
  margin-right: 0; }

.custom-control {
  padding-right: 1.5rem;
  padding-left: 0; }

.custom-control-label::before {
  left: auto;
  right: -1.5rem; }

.custom-control-label::after {
  left: auto;
  right: -1.5rem; }

.custom-switch {
  padding-right: 2.25rem;
  padding-left: 0; }
  .custom-switch .custom-control-label::before {
    right: -2.25rem;
    left: auto; }
  .custom-switch .custom-control-label::after {
    right: calc(-2.25rem + 2px);
    left: auto; }
  .custom-switch .custom-control-input:checked ~ .custom-control-label::after {
    -webkit-transform: translateX(-0.75rem);
            transform: translateX(-0.75rem); }

.custom-file-label::after {
  right: auto;
  left: 0;
  border-right: inherit; }

.input-group-prepend {
  margin-left: -1px;
  margin-right: 0; }

.input-group-append {
  margin-right: -1px;
  margin-left: 0; }

.input-group > .input-group-prepend > .btn,
.input-group > .input-group-prepend > .input-group-text,
.input-group > .input-group-append:not(:last-child) > .btn,
.input-group > .input-group-append:not(:last-child) > .input-group-text,
.input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle),
.input-group > .input-group-append:last-child > .input-group-text:not(:last-child),
.input-group > .custom-select:not(:last-child),
.input-group > .form-control:not(:last-child) {
  border-top-right-radius: 0.2rem;
  border-bottom-right-radius: 0.2rem;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0; }

.input-group > .input-group-append > .btn,
.input-group > .input-group-append > .input-group-text,
.input-group > .input-group-prepend:not(:first-child) > .btn,
.input-group > .input-group-prepend:not(:first-child) > .input-group-text,
.input-group > .input-group-prepend:first-child > .btn:not(:first-child),
.input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child),
.input-group > .custom-select:not(:first-child),
.input-group > .form-control:not(:first-child) {
  border-top-left-radius: 0.2rem;
  border-bottom-left-radius: 0.2rem;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0; }

.m-0 {
  margin: 0 !important; }

.mt-0,
.my-0 {
  margin-top: 0 !important; }

.mr-0,
.mx-0 {
  margin-left: 0 !important;
  margin-right: 0 !important; }

.mb-0,
.my-0 {
  margin-bottom: 0 !important; }

.ml-0,
.mx-0 {
  margin-right: 0 !important;
  margin-left: 0 !important; }

.m-1 {
  margin: 0.375rem !important; }

.mt-1,
.my-1 {
  margin-top: 0.375rem !important; }

.mr-1,
.mx-1 {
  margin-left: 0.375rem !important;
  margin-right: 0 !important; }

.mb-1,
.my-1 {
  margin-bottom: 0.375rem !important; }

.ml-1,
.mx-1 {
  margin-right: 0.375rem !important;
  margin-left: 0 !important; }

.m-2 {
  margin: 0.75rem !important; }

.mt-2,
.my-2 {
  margin-top: 0.75rem !important; }

.mr-2,
.mx-2 {
  margin-left: 0.75rem !important;
  margin-right: 0 !important; }

.mb-2,
.my-2 {
  margin-bottom: 0.75rem !important; }

.ml-2,
.mx-2 {
  margin-right: 0.75rem !important;
  margin-left: 0 !important; }

.m-3 {
  margin: 1.5rem !important; }

.mt-3,
.my-3 {
  margin-top: 1.5rem !important; }

.mr-3,
.mx-3 {
  margin-left: 1.5rem !important;
  margin-right: 0 !important; }

.mb-3,
.my-3 {
  margin-bottom: 1.5rem !important; }

.ml-3,
.mx-3 {
  margin-right: 1.5rem !important;
  margin-left: 0 !important; }

.m-4 {
  margin: 2.25rem !important; }

.mt-4,
.my-4 {
  margin-top: 2.25rem !important; }

.mr-4,
.mx-4 {
  margin-left: 2.25rem !important;
  margin-right: 0 !important; }

.mb-4,
.my-4 {
  margin-bottom: 2.25rem !important; }

.ml-4,
.mx-4 {
  margin-right: 2.25rem !important;
  margin-left: 0 !important; }

.m-5 {
  margin: 4.5rem !important; }

.mt-5,
.my-5 {
  margin-top: 4.5rem !important; }

.mr-5,
.mx-5 {
  margin-left: 4.5rem !important;
  margin-right: 0 !important; }

.mb-5,
.my-5 {
  margin-bottom: 4.5rem !important; }

.ml-5,
.mx-5 {
  margin-right: 4.5rem !important;
  margin-left: 0 !important; }

.p-0 {
  padding: 0 !important; }

.pt-0,
.py-0 {
  padding-top: 0 !important; }

.pr-0,
.px-0 {
  padding-left: 0 !important;
  padding-right: 0 !important; }

.pb-0,
.py-0 {
  padding-bottom: 0 !important; }

.pl-0,
.px-0 {
  padding-right: 0 !important;
  padding-left: 0 !important; }

.p-1 {
  padding: 0.375rem !important; }

.pt-1,
.py-1 {
  padding-top: 0.375rem !important; }

.pr-1,
.px-1 {
  padding-left: 0.375rem !important;
  padding-right: 0 !important; }

.pb-1,
.py-1 {
  padding-bottom: 0.375rem !important; }

.pl-1,
.px-1 {
  padding-right: 0.375rem !important;
  padding-left: 0 !important; }

.p-2 {
  padding: 0.75rem !important; }

.pt-2,
.py-2 {
  padding-top: 0.75rem !important; }

.pr-2,
.px-2 {
  padding-left: 0.75rem !important;
  padding-right: 0 !important; }

.pb-2,
.py-2 {
  padding-bottom: 0.75rem !important; }

.pl-2,
.px-2 {
  padding-right: 0.75rem !important;
  padding-left: 0 !important; }

.p-3 {
  padding: 1.5rem !important; }

.pt-3,
.py-3 {
  padding-top: 1.5rem !important; }

.pr-3,
.px-3 {
  padding-left: 1.5rem !important;
  padding-right: 0 !important; }

.pb-3,
.py-3 {
  padding-bottom: 1.5rem !important; }

.pl-3,
.px-3 {
  padding-right: 1.5rem !important;
  padding-left: 0 !important; }

.p-4 {
  padding: 2.25rem !important; }

.pt-4,
.py-4 {
  padding-top: 2.25rem !important; }

.pr-4,
.px-4 {
  padding-left: 2.25rem !important;
  padding-right: 0 !important; }

.pb-4,
.py-4 {
  padding-bottom: 2.25rem !important; }

.pl-4,
.px-4 {
  padding-right: 2.25rem !important;
  padding-left: 0 !important; }

.p-5 {
  padding: 4.5rem !important; }

.pt-5,
.py-5 {
  padding-top: 4.5rem !important; }

.pr-5,
.px-5 {
  padding-left: 4.5rem !important;
  padding-right: 0 !important; }

.pb-5,
.py-5 {
  padding-bottom: 4.5rem !important; }

.pl-5,
.px-5 {
  padding-right: 4.5rem !important;
  padding-left: 0 !important; }

.m-n1 {
  margin: -0.375rem !important; }

.mt-n1,
.my-n1 {
  margin-top: -0.375rem !important; }

.mr-n1,
.mx-n1 {
  margin-right: -0.375rem !important; }

.mb-n1,
.my-n1 {
  margin-bottom: -0.375rem !important; }

.ml-n1,
.mx-n1 {
  margin-left: -0.375rem !important; }

.m-n2 {
  margin: -0.75rem !important; }

.mt-n2,
.my-n2 {
  margin-top: -0.75rem !important; }

.mr-n2,
.mx-n2 {
  margin-right: -0.75rem !important; }

.mb-n2,
.my-n2 {
  margin-bottom: -0.75rem !important; }

.ml-n2,
.mx-n2 {
  margin-left: -0.75rem !important; }

.m-n3 {
  margin: -1.5rem !important; }

.mt-n3,
.my-n3 {
  margin-top: -1.5rem !important; }

.mr-n3,
.mx-n3 {
  margin-right: -1.5rem !important; }

.mb-n3,
.my-n3 {
  margin-bottom: -1.5rem !important; }

.ml-n3,
.mx-n3 {
  margin-left: -1.5rem !important; }

.m-n4 {
  margin: -2.25rem !important; }

.mt-n4,
.my-n4 {
  margin-top: -2.25rem !important; }

.mr-n4,
.mx-n4 {
  margin-right: -2.25rem !important; }

.mb-n4,
.my-n4 {
  margin-bottom: -2.25rem !important; }

.ml-n4,
.mx-n4 {
  margin-left: -2.25rem !important; }

.m-n5 {
  margin: -4.5rem !important; }

.mt-n5,
.my-n5 {
  margin-top: -4.5rem !important; }

.mr-n5,
.mx-n5 {
  margin-right: -4.5rem !important; }

.mb-n5,
.my-n5 {
  margin-bottom: -4.5rem !important; }

.ml-n5,
.mx-n5 {
  margin-left: -4.5rem !important; }

.m-auto {
  margin: auto !important; }

.mt-auto,
.my-auto {
  margin-top: auto !important; }

.mr-auto,
.mx-auto {
  margin-left: auto !important;
  margin-right: inherit !important; }

.mb-auto,
.my-auto {
  margin-bottom: auto !important; }

.ml-auto,
.mx-auto {
  margin-right: auto !important;
  margin-left: auto !important; }

@media (min-width: 576px) {
  .m-sm-0 {
    margin: 0 !important; }
  .mt-sm-0,
  .my-sm-0 {
    margin-top: 0 !important; }
  .mr-sm-0,
  .mx-sm-0 {
    margin-left: 0 !important;
    margin-right: 0 !important; }
  .mb-sm-0,
  .my-sm-0 {
    margin-bottom: 0 !important; }
  .ml-sm-0,
  .mx-sm-0 {
    margin-right: 0 !important;
    margin-left: 0 !important; }
  .m-sm-1 {
    margin: 0.375rem !important; }
  .mt-sm-1,
  .my-sm-1 {
    margin-top: 0.375rem !important; }
  .mr-sm-1,
  .mx-sm-1 {
    margin-left: 0.375rem !important;
    margin-right: 0 !important; }
  .mb-sm-1,
  .my-sm-1 {
    margin-bottom: 0.375rem !important; }
  .ml-sm-1,
  .mx-sm-1 {
    margin-right: 0.375rem !important;
    margin-left: 0 !important; }
  .m-sm-2 {
    margin: 0.75rem !important; }
  .mt-sm-2,
  .my-sm-2 {
    margin-top: 0.75rem !important; }
  .mr-sm-2,
  .mx-sm-2 {
    margin-left: 0.75rem !important;
    margin-right: 0 !important; }
  .mb-sm-2,
  .my-sm-2 {
    margin-bottom: 0.75rem !important; }
  .ml-sm-2,
  .mx-sm-2 {
    margin-right: 0.75rem !important;
    margin-left: 0 !important; }
  .m-sm-3 {
    margin: 1.5rem !important; }
  .mt-sm-3,
  .my-sm-3 {
    margin-top: 1.5rem !important; }
  .mr-sm-3,
  .mx-sm-3 {
    margin-left: 1.5rem !important;
    margin-right: 0 !important; }
  .mb-sm-3,
  .my-sm-3 {
    margin-bottom: 1.5rem !important; }
  .ml-sm-3,
  .mx-sm-3 {
    margin-right: 1.5rem !important;
    margin-left: 0 !important; }
  .m-sm-4 {
    margin: 2.25rem !important; }
  .mt-sm-4,
  .my-sm-4 {
    margin-top: 2.25rem !important; }
  .mr-sm-4,
  .mx-sm-4 {
    margin-left: 2.25rem !important;
    margin-right: 0 !important; }
  .mb-sm-4,
  .my-sm-4 {
    margin-bottom: 2.25rem !important; }
  .ml-sm-4,
  .mx-sm-4 {
    margin-right: 2.25rem !important;
    margin-left: 0 !important; }
  .m-sm-5 {
    margin: 4.5rem !important; }
  .mt-sm-5,
  .my-sm-5 {
    margin-top: 4.5rem !important; }
  .mr-sm-5,
  .mx-sm-5 {
    margin-left: 4.5rem !important;
    margin-right: 0 !important; }
  .mb-sm-5,
  .my-sm-5 {
    margin-bottom: 4.5rem !important; }
  .ml-sm-5,
  .mx-sm-5 {
    margin-right: 4.5rem !important;
    margin-left: 0 !important; }
  .p-sm-0 {
    padding: 0 !important; }
  .pt-sm-0,
  .py-sm-0 {
    padding-top: 0 !important; }
  .pr-sm-0,
  .px-sm-0 {
    padding-left: 0 !important;
    padding-right: 0 !important; }
  .pb-sm-0,
  .py-sm-0 {
    padding-bottom: 0 !important; }
  .pl-sm-0,
  .px-sm-0 {
    padding-right: 0 !important;
    padding-left: 0 !important; }
  .p-sm-1 {
    padding: 0.375rem !important; }
  .pt-sm-1,
  .py-sm-1 {
    padding-top: 0.375rem !important; }
  .pr-sm-1,
  .px-sm-1 {
    padding-left: 0.375rem !important;
    padding-right: 0 !important; }
  .pb-sm-1,
  .py-sm-1 {
    padding-bottom: 0.375rem !important; }
  .pl-sm-1,
  .px-sm-1 {
    padding-right: 0.375rem !important;
    padding-left: 0 !important; }
  .p-sm-2 {
    padding: 0.75rem !important; }
  .pt-sm-2,
  .py-sm-2 {
    padding-top: 0.75rem !important; }
  .pr-sm-2,
  .px-sm-2 {
    padding-left: 0.75rem !important;
    padding-right: 0 !important; }
  .pb-sm-2,
  .py-sm-2 {
    padding-bottom: 0.75rem !important; }
  .pl-sm-2,
  .px-sm-2 {
    padding-right: 0.75rem !important;
    padding-left: 0 !important; }
  .p-sm-3 {
    padding: 1.5rem !important; }
  .pt-sm-3,
  .py-sm-3 {
    padding-top: 1.5rem !important; }
  .pr-sm-3,
  .px-sm-3 {
    padding-left: 1.5rem !important;
    padding-right: 0 !important; }
  .pb-sm-3,
  .py-sm-3 {
    padding-bottom: 1.5rem !important; }
  .pl-sm-3,
  .px-sm-3 {
    padding-right: 1.5rem !important;
    padding-left: 0 !important; }
  .p-sm-4 {
    padding: 2.25rem !important; }
  .pt-sm-4,
  .py-sm-4 {
    padding-top: 2.25rem !important; }
  .pr-sm-4,
  .px-sm-4 {
    padding-left: 2.25rem !important;
    padding-right: 0 !important; }
  .pb-sm-4,
  .py-sm-4 {
    padding-bottom: 2.25rem !important; }
  .pl-sm-4,
  .px-sm-4 {
    padding-right: 2.25rem !important;
    padding-left: 0 !important; }
  .p-sm-5 {
    padding: 4.5rem !important; }
  .pt-sm-5,
  .py-sm-5 {
    padding-top: 4.5rem !important; }
  .pr-sm-5,
  .px-sm-5 {
    padding-left: 4.5rem !important;
    padding-right: 0 !important; }
  .pb-sm-5,
  .py-sm-5 {
    padding-bottom: 4.5rem !important; }
  .pl-sm-5,
  .px-sm-5 {
    padding-right: 4.5rem !important;
    padding-left: 0 !important; }
  .m-sm-n1 {
    margin: -0.375rem !important; }
  .mt-sm-n1,
  .my-sm-n1 {
    margin-top: -0.375rem !important; }
  .mr-sm-n1,
  .mx-sm-n1 {
    margin-right: -0.375rem !important; }
  .mb-sm-n1,
  .my-sm-n1 {
    margin-bottom: -0.375rem !important; }
  .ml-sm-n1,
  .mx-sm-n1 {
    margin-left: -0.375rem !important; }
  .m-sm-n2 {
    margin: -0.75rem !important; }
  .mt-sm-n2,
  .my-sm-n2 {
    margin-top: -0.75rem !important; }
  .mr-sm-n2,
  .mx-sm-n2 {
    margin-right: -0.75rem !important; }
  .mb-sm-n2,
  .my-sm-n2 {
    margin-bottom: -0.75rem !important; }
  .ml-sm-n2,
  .mx-sm-n2 {
    margin-left: -0.75rem !important; }
  .m-sm-n3 {
    margin: -1.5rem !important; }
  .mt-sm-n3,
  .my-sm-n3 {
    margin-top: -1.5rem !important; }
  .mr-sm-n3,
  .mx-sm-n3 {
    margin-right: -1.5rem !important; }
  .mb-sm-n3,
  .my-sm-n3 {
    margin-bottom: -1.5rem !important; }
  .ml-sm-n3,
  .mx-sm-n3 {
    margin-left: -1.5rem !important; }
  .m-sm-n4 {
    margin: -2.25rem !important; }
  .mt-sm-n4,
  .my-sm-n4 {
    margin-top: -2.25rem !important; }
  .mr-sm-n4,
  .mx-sm-n4 {
    margin-right: -2.25rem !important; }
  .mb-sm-n4,
  .my-sm-n4 {
    margin-bottom: -2.25rem !important; }
  .ml-sm-n4,
  .mx-sm-n4 {
    margin-left: -2.25rem !important; }
  .m-sm-n5 {
    margin: -4.5rem !important; }
  .mt-sm-n5,
  .my-sm-n5 {
    margin-top: -4.5rem !important; }
  .mr-sm-n5,
  .mx-sm-n5 {
    margin-right: -4.5rem !important; }
  .mb-sm-n5,
  .my-sm-n5 {
    margin-bottom: -4.5rem !important; }
  .ml-sm-n5,
  .mx-sm-n5 {
    margin-left: -4.5rem !important; }
  .m-sm-auto {
    margin: auto !important; }
  .mt-sm-auto,
  .my-sm-auto {
    margin-top: auto !important; }
  .mr-sm-auto,
  .mx-sm-auto {
    margin-left: auto !important;
    margin-right: inherit !important; }
  .mb-sm-auto,
  .my-sm-auto {
    margin-bottom: auto !important; }
  .ml-sm-auto,
  .mx-sm-auto {
    margin-right: auto !important;
    margin-left: auto !important; } }

@media (min-width: 768px) {
  .m-md-0 {
    margin: 0 !important; }
  .mt-md-0,
  .my-md-0 {
    margin-top: 0 !important; }
  .mr-md-0,
  .mx-md-0 {
    margin-left: 0 !important;
    margin-right: 0 !important; }
  .mb-md-0,
  .my-md-0 {
    margin-bottom: 0 !important; }
  .ml-md-0,
  .mx-md-0 {
    margin-right: 0 !important;
    margin-left: 0 !important; }
  .m-md-1 {
    margin: 0.375rem !important; }
  .mt-md-1,
  .my-md-1 {
    margin-top: 0.375rem !important; }
  .mr-md-1,
  .mx-md-1 {
    margin-left: 0.375rem !important;
    margin-right: 0 !important; }
  .mb-md-1,
  .my-md-1 {
    margin-bottom: 0.375rem !important; }
  .ml-md-1,
  .mx-md-1 {
    margin-right: 0.375rem !important;
    margin-left: 0 !important; }
  .m-md-2 {
    margin: 0.75rem !important; }
  .mt-md-2,
  .my-md-2 {
    margin-top: 0.75rem !important; }
  .mr-md-2,
  .mx-md-2 {
    margin-left: 0.75rem !important;
    margin-right: 0 !important; }
  .mb-md-2,
  .my-md-2 {
    margin-bottom: 0.75rem !important; }
  .ml-md-2,
  .mx-md-2 {
    margin-right: 0.75rem !important;
    margin-left: 0 !important; }
  .m-md-3 {
    margin: 1.5rem !important; }
  .mt-md-3,
  .my-md-3 {
    margin-top: 1.5rem !important; }
  .mr-md-3,
  .mx-md-3 {
    margin-left: 1.5rem !important;
    margin-right: 0 !important; }
  .mb-md-3,
  .my-md-3 {
    margin-bottom: 1.5rem !important; }
  .ml-md-3,
  .mx-md-3 {
    margin-right: 1.5rem !important;
    margin-left: 0 !important; }
  .m-md-4 {
    margin: 2.25rem !important; }
  .mt-md-4,
  .my-md-4 {
    margin-top: 2.25rem !important; }
  .mr-md-4,
  .mx-md-4 {
    margin-left: 2.25rem !important;
    margin-right: 0 !important; }
  .mb-md-4,
  .my-md-4 {
    margin-bottom: 2.25rem !important; }
  .ml-md-4,
  .mx-md-4 {
    margin-right: 2.25rem !important;
    margin-left: 0 !important; }
  .m-md-5 {
    margin: 4.5rem !important; }
  .mt-md-5,
  .my-md-5 {
    margin-top: 4.5rem !important; }
  .mr-md-5,
  .mx-md-5 {
    margin-left: 4.5rem !important;
    margin-right: 0 !important; }
  .mb-md-5,
  .my-md-5 {
    margin-bottom: 4.5rem !important; }
  .ml-md-5,
  .mx-md-5 {
    margin-right: 4.5rem !important;
    margin-left: 0 !important; }
  .p-md-0 {
    padding: 0 !important; }
  .pt-md-0,
  .py-md-0 {
    padding-top: 0 !important; }
  .pr-md-0,
  .px-md-0 {
    padding-left: 0 !important;
    padding-right: 0 !important; }
  .pb-md-0,
  .py-md-0 {
    padding-bottom: 0 !important; }
  .pl-md-0,
  .px-md-0 {
    padding-right: 0 !important;
    padding-left: 0 !important; }
  .p-md-1 {
    padding: 0.375rem !important; }
  .pt-md-1,
  .py-md-1 {
    padding-top: 0.375rem !important; }
  .pr-md-1,
  .px-md-1 {
    padding-left: 0.375rem !important;
    padding-right: 0 !important; }
  .pb-md-1,
  .py-md-1 {
    padding-bottom: 0.375rem !important; }
  .pl-md-1,
  .px-md-1 {
    padding-right: 0.375rem !important;
    padding-left: 0 !important; }
  .p-md-2 {
    padding: 0.75rem !important; }
  .pt-md-2,
  .py-md-2 {
    padding-top: 0.75rem !important; }
  .pr-md-2,
  .px-md-2 {
    padding-left: 0.75rem !important;
    padding-right: 0 !important; }
  .pb-md-2,
  .py-md-2 {
    padding-bottom: 0.75rem !important; }
  .pl-md-2,
  .px-md-2 {
    padding-right: 0.75rem !important;
    padding-left: 0 !important; }
  .p-md-3 {
    padding: 1.5rem !important; }
  .pt-md-3,
  .py-md-3 {
    padding-top: 1.5rem !important; }
  .pr-md-3,
  .px-md-3 {
    padding-left: 1.5rem !important;
    padding-right: 0 !important; }
  .pb-md-3,
  .py-md-3 {
    padding-bottom: 1.5rem !important; }
  .pl-md-3,
  .px-md-3 {
    padding-right: 1.5rem !important;
    padding-left: 0 !important; }
  .p-md-4 {
    padding: 2.25rem !important; }
  .pt-md-4,
  .py-md-4 {
    padding-top: 2.25rem !important; }
  .pr-md-4,
  .px-md-4 {
    padding-left: 2.25rem !important;
    padding-right: 0 !important; }
  .pb-md-4,
  .py-md-4 {
    padding-bottom: 2.25rem !important; }
  .pl-md-4,
  .px-md-4 {
    padding-right: 2.25rem !important;
    padding-left: 0 !important; }
  .p-md-5 {
    padding: 4.5rem !important; }
  .pt-md-5,
  .py-md-5 {
    padding-top: 4.5rem !important; }
  .pr-md-5,
  .px-md-5 {
    padding-left: 4.5rem !important;
    padding-right: 0 !important; }
  .pb-md-5,
  .py-md-5 {
    padding-bottom: 4.5rem !important; }
  .pl-md-5,
  .px-md-5 {
    padding-right: 4.5rem !important;
    padding-left: 0 !important; }
  .m-md-n1 {
    margin: -0.375rem !important; }
  .mt-md-n1,
  .my-md-n1 {
    margin-top: -0.375rem !important; }
  .mr-md-n1,
  .mx-md-n1 {
    margin-right: -0.375rem !important; }
  .mb-md-n1,
  .my-md-n1 {
    margin-bottom: -0.375rem !important; }
  .ml-md-n1,
  .mx-md-n1 {
    margin-left: -0.375rem !important; }
  .m-md-n2 {
    margin: -0.75rem !important; }
  .mt-md-n2,
  .my-md-n2 {
    margin-top: -0.75rem !important; }
  .mr-md-n2,
  .mx-md-n2 {
    margin-right: -0.75rem !important; }
  .mb-md-n2,
  .my-md-n2 {
    margin-bottom: -0.75rem !important; }
  .ml-md-n2,
  .mx-md-n2 {
    margin-left: -0.75rem !important; }
  .m-md-n3 {
    margin: -1.5rem !important; }
  .mt-md-n3,
  .my-md-n3 {
    margin-top: -1.5rem !important; }
  .mr-md-n3,
  .mx-md-n3 {
    margin-right: -1.5rem !important; }
  .mb-md-n3,
  .my-md-n3 {
    margin-bottom: -1.5rem !important; }
  .ml-md-n3,
  .mx-md-n3 {
    margin-left: -1.5rem !important; }
  .m-md-n4 {
    margin: -2.25rem !important; }
  .mt-md-n4,
  .my-md-n4 {
    margin-top: -2.25rem !important; }
  .mr-md-n4,
  .mx-md-n4 {
    margin-right: -2.25rem !important; }
  .mb-md-n4,
  .my-md-n4 {
    margin-bottom: -2.25rem !important; }
  .ml-md-n4,
  .mx-md-n4 {
    margin-left: -2.25rem !important; }
  .m-md-n5 {
    margin: -4.5rem !important; }
  .mt-md-n5,
  .my-md-n5 {
    margin-top: -4.5rem !important; }
  .mr-md-n5,
  .mx-md-n5 {
    margin-right: -4.5rem !important; }
  .mb-md-n5,
  .my-md-n5 {
    margin-bottom: -4.5rem !important; }
  .ml-md-n5,
  .mx-md-n5 {
    margin-left: -4.5rem !important; }
  .m-md-auto {
    margin: auto !important; }
  .mt-md-auto,
  .my-md-auto {
    margin-top: auto !important; }
  .mr-md-auto,
  .mx-md-auto {
    margin-left: auto !important;
    margin-right: inherit !important; }
  .mb-md-auto,
  .my-md-auto {
    margin-bottom: auto !important; }
  .ml-md-auto,
  .mx-md-auto {
    margin-right: auto !important;
    margin-left: auto !important; } }

@media (min-width: 992px) {
  .m-lg-0 {
    margin: 0 !important; }
  .mt-lg-0,
  .my-lg-0 {
    margin-top: 0 !important; }
  .mr-lg-0,
  .mx-lg-0 {
    margin-left: 0 !important;
    margin-right: 0 !important; }
  .mb-lg-0,
  .my-lg-0 {
    margin-bottom: 0 !important; }
  .ml-lg-0,
  .mx-lg-0 {
    margin-right: 0 !important;
    margin-left: 0 !important; }
  .m-lg-1 {
    margin: 0.375rem !important; }
  .mt-lg-1,
  .my-lg-1 {
    margin-top: 0.375rem !important; }
  .mr-lg-1,
  .mx-lg-1 {
    margin-left: 0.375rem !important;
    margin-right: 0 !important; }
  .mb-lg-1,
  .my-lg-1 {
    margin-bottom: 0.375rem !important; }
  .ml-lg-1,
  .mx-lg-1 {
    margin-right: 0.375rem !important;
    margin-left: 0 !important; }
  .m-lg-2 {
    margin: 0.75rem !important; }
  .mt-lg-2,
  .my-lg-2 {
    margin-top: 0.75rem !important; }
  .mr-lg-2,
  .mx-lg-2 {
    margin-left: 0.75rem !important;
    margin-right: 0 !important; }
  .mb-lg-2,
  .my-lg-2 {
    margin-bottom: 0.75rem !important; }
  .ml-lg-2,
  .mx-lg-2 {
    margin-right: 0.75rem !important;
    margin-left: 0 !important; }
  .m-lg-3 {
    margin: 1.5rem !important; }
  .mt-lg-3,
  .my-lg-3 {
    margin-top: 1.5rem !important; }
  .mr-lg-3,
  .mx-lg-3 {
    margin-left: 1.5rem !important;
    margin-right: 0 !important; }
  .mb-lg-3,
  .my-lg-3 {
    margin-bottom: 1.5rem !important; }
  .ml-lg-3,
  .mx-lg-3 {
    margin-right: 1.5rem !important;
    margin-left: 0 !important; }
  .m-lg-4 {
    margin: 2.25rem !important; }
  .mt-lg-4,
  .my-lg-4 {
    margin-top: 2.25rem !important; }
  .mr-lg-4,
  .mx-lg-4 {
    margin-left: 2.25rem !important;
    margin-right: 0 !important; }
  .mb-lg-4,
  .my-lg-4 {
    margin-bottom: 2.25rem !important; }
  .ml-lg-4,
  .mx-lg-4 {
    margin-right: 2.25rem !important;
    margin-left: 0 !important; }
  .m-lg-5 {
    margin: 4.5rem !important; }
  .mt-lg-5,
  .my-lg-5 {
    margin-top: 4.5rem !important; }
  .mr-lg-5,
  .mx-lg-5 {
    margin-left: 4.5rem !important;
    margin-right: 0 !important; }
  .mb-lg-5,
  .my-lg-5 {
    margin-bottom: 4.5rem !important; }
  .ml-lg-5,
  .mx-lg-5 {
    margin-right: 4.5rem !important;
    margin-left: 0 !important; }
  .p-lg-0 {
    padding: 0 !important; }
  .pt-lg-0,
  .py-lg-0 {
    padding-top: 0 !important; }
  .pr-lg-0,
  .px-lg-0 {
    padding-left: 0 !important;
    padding-right: 0 !important; }
  .pb-lg-0,
  .py-lg-0 {
    padding-bottom: 0 !important; }
  .pl-lg-0,
  .px-lg-0 {
    padding-right: 0 !important;
    padding-left: 0 !important; }
  .p-lg-1 {
    padding: 0.375rem !important; }
  .pt-lg-1,
  .py-lg-1 {
    padding-top: 0.375rem !important; }
  .pr-lg-1,
  .px-lg-1 {
    padding-left: 0.375rem !important;
    padding-right: 0 !important; }
  .pb-lg-1,
  .py-lg-1 {
    padding-bottom: 0.375rem !important; }
  .pl-lg-1,
  .px-lg-1 {
    padding-right: 0.375rem !important;
    padding-left: 0 !important; }
  .p-lg-2 {
    padding: 0.75rem !important; }
  .pt-lg-2,
  .py-lg-2 {
    padding-top: 0.75rem !important; }
  .pr-lg-2,
  .px-lg-2 {
    padding-left: 0.75rem !important;
    padding-right: 0 !important; }
  .pb-lg-2,
  .py-lg-2 {
    padding-bottom: 0.75rem !important; }
  .pl-lg-2,
  .px-lg-2 {
    padding-right: 0.75rem !important;
    padding-left: 0 !important; }
  .p-lg-3 {
    padding: 1.5rem !important; }
  .pt-lg-3,
  .py-lg-3 {
    padding-top: 1.5rem !important; }
  .pr-lg-3,
  .px-lg-3 {
    padding-left: 1.5rem !important;
    padding-right: 0 !important; }
  .pb-lg-3,
  .py-lg-3 {
    padding-bottom: 1.5rem !important; }
  .pl-lg-3,
  .px-lg-3 {
    padding-right: 1.5rem !important;
    padding-left: 0 !important; }
  .p-lg-4 {
    padding: 2.25rem !important; }
  .pt-lg-4,
  .py-lg-4 {
    padding-top: 2.25rem !important; }
  .pr-lg-4,
  .px-lg-4 {
    padding-left: 2.25rem !important;
    padding-right: 0 !important; }
  .pb-lg-4,
  .py-lg-4 {
    padding-bottom: 2.25rem !important; }
  .pl-lg-4,
  .px-lg-4 {
    padding-right: 2.25rem !important;
    padding-left: 0 !important; }
  .p-lg-5 {
    padding: 4.5rem !important; }
  .pt-lg-5,
  .py-lg-5 {
    padding-top: 4.5rem !important; }
  .pr-lg-5,
  .px-lg-5 {
    padding-left: 4.5rem !important;
    padding-right: 0 !important; }
  .pb-lg-5,
  .py-lg-5 {
    padding-bottom: 4.5rem !important; }
  .pl-lg-5,
  .px-lg-5 {
    padding-right: 4.5rem !important;
    padding-left: 0 !important; }
  .m-lg-n1 {
    margin: -0.375rem !important; }
  .mt-lg-n1,
  .my-lg-n1 {
    margin-top: -0.375rem !important; }
  .mr-lg-n1,
  .mx-lg-n1 {
    margin-right: -0.375rem !important; }
  .mb-lg-n1,
  .my-lg-n1 {
    margin-bottom: -0.375rem !important; }
  .ml-lg-n1,
  .mx-lg-n1 {
    margin-left: -0.375rem !important; }
  .m-lg-n2 {
    margin: -0.75rem !important; }
  .mt-lg-n2,
  .my-lg-n2 {
    margin-top: -0.75rem !important; }
  .mr-lg-n2,
  .mx-lg-n2 {
    margin-right: -0.75rem !important; }
  .mb-lg-n2,
  .my-lg-n2 {
    margin-bottom: -0.75rem !important; }
  .ml-lg-n2,
  .mx-lg-n2 {
    margin-left: -0.75rem !important; }
  .m-lg-n3 {
    margin: -1.5rem !important; }
  .mt-lg-n3,
  .my-lg-n3 {
    margin-top: -1.5rem !important; }
  .mr-lg-n3,
  .mx-lg-n3 {
    margin-right: -1.5rem !important; }
  .mb-lg-n3,
  .my-lg-n3 {
    margin-bottom: -1.5rem !important; }
  .ml-lg-n3,
  .mx-lg-n3 {
    margin-left: -1.5rem !important; }
  .m-lg-n4 {
    margin: -2.25rem !important; }
  .mt-lg-n4,
  .my-lg-n4 {
    margin-top: -2.25rem !important; }
  .mr-lg-n4,
  .mx-lg-n4 {
    margin-right: -2.25rem !important; }
  .mb-lg-n4,
  .my-lg-n4 {
    margin-bottom: -2.25rem !important; }
  .ml-lg-n4,
  .mx-lg-n4 {
    margin-left: -2.25rem !important; }
  .m-lg-n5 {
    margin: -4.5rem !important; }
  .mt-lg-n5,
  .my-lg-n5 {
    margin-top: -4.5rem !important; }
  .mr-lg-n5,
  .mx-lg-n5 {
    margin-right: -4.5rem !important; }
  .mb-lg-n5,
  .my-lg-n5 {
    margin-bottom: -4.5rem !important; }
  .ml-lg-n5,
  .mx-lg-n5 {
    margin-left: -4.5rem !important; }
  .m-lg-auto {
    margin: auto !important; }
  .mt-lg-auto,
  .my-lg-auto {
    margin-top: auto !important; }
  .mr-lg-auto,
  .mx-lg-auto {
    margin-left: auto !important;
    margin-right: inherit !important; }
  .mb-lg-auto,
  .my-lg-auto {
    margin-bottom: auto !important; }
  .ml-lg-auto,
  .mx-lg-auto {
    margin-right: auto !important;
    margin-left: auto !important; } }

@media (min-width: 1200px) {
  .m-xl-0 {
    margin: 0 !important; }
  .mt-xl-0,
  .my-xl-0 {
    margin-top: 0 !important; }
  .mr-xl-0,
  .mx-xl-0 {
    margin-left: 0 !important;
    margin-right: 0 !important; }
  .mb-xl-0,
  .my-xl-0 {
    margin-bottom: 0 !important; }
  .ml-xl-0,
  .mx-xl-0 {
    margin-right: 0 !important;
    margin-left: 0 !important; }
  .m-xl-1 {
    margin: 0.375rem !important; }
  .mt-xl-1,
  .my-xl-1 {
    margin-top: 0.375rem !important; }
  .mr-xl-1,
  .mx-xl-1 {
    margin-left: 0.375rem !important;
    margin-right: 0 !important; }
  .mb-xl-1,
  .my-xl-1 {
    margin-bottom: 0.375rem !important; }
  .ml-xl-1,
  .mx-xl-1 {
    margin-right: 0.375rem !important;
    margin-left: 0 !important; }
  .m-xl-2 {
    margin: 0.75rem !important; }
  .mt-xl-2,
  .my-xl-2 {
    margin-top: 0.75rem !important; }
  .mr-xl-2,
  .mx-xl-2 {
    margin-left: 0.75rem !important;
    margin-right: 0 !important; }
  .mb-xl-2,
  .my-xl-2 {
    margin-bottom: 0.75rem !important; }
  .ml-xl-2,
  .mx-xl-2 {
    margin-right: 0.75rem !important;
    margin-left: 0 !important; }
  .m-xl-3 {
    margin: 1.5rem !important; }
  .mt-xl-3,
  .my-xl-3 {
    margin-top: 1.5rem !important; }
  .mr-xl-3,
  .mx-xl-3 {
    margin-left: 1.5rem !important;
    margin-right: 0 !important; }
  .mb-xl-3,
  .my-xl-3 {
    margin-bottom: 1.5rem !important; }
  .ml-xl-3,
  .mx-xl-3 {
    margin-right: 1.5rem !important;
    margin-left: 0 !important; }
  .m-xl-4 {
    margin: 2.25rem !important; }
  .mt-xl-4,
  .my-xl-4 {
    margin-top: 2.25rem !important; }
  .mr-xl-4,
  .mx-xl-4 {
    margin-left: 2.25rem !important;
    margin-right: 0 !important; }
  .mb-xl-4,
  .my-xl-4 {
    margin-bottom: 2.25rem !important; }
  .ml-xl-4,
  .mx-xl-4 {
    margin-right: 2.25rem !important;
    margin-left: 0 !important; }
  .m-xl-5 {
    margin: 4.5rem !important; }
  .mt-xl-5,
  .my-xl-5 {
    margin-top: 4.5rem !important; }
  .mr-xl-5,
  .mx-xl-5 {
    margin-left: 4.5rem !important;
    margin-right: 0 !important; }
  .mb-xl-5,
  .my-xl-5 {
    margin-bottom: 4.5rem !important; }
  .ml-xl-5,
  .mx-xl-5 {
    margin-right: 4.5rem !important;
    margin-left: 0 !important; }
  .p-xl-0 {
    padding: 0 !important; }
  .pt-xl-0,
  .py-xl-0 {
    padding-top: 0 !important; }
  .pr-xl-0,
  .px-xl-0 {
    padding-left: 0 !important;
    padding-right: 0 !important; }
  .pb-xl-0,
  .py-xl-0 {
    padding-bottom: 0 !important; }
  .pl-xl-0,
  .px-xl-0 {
    padding-right: 0 !important;
    padding-left: 0 !important; }
  .p-xl-1 {
    padding: 0.375rem !important; }
  .pt-xl-1,
  .py-xl-1 {
    padding-top: 0.375rem !important; }
  .pr-xl-1,
  .px-xl-1 {
    padding-left: 0.375rem !important;
    padding-right: 0 !important; }
  .pb-xl-1,
  .py-xl-1 {
    padding-bottom: 0.375rem !important; }
  .pl-xl-1,
  .px-xl-1 {
    padding-right: 0.375rem !important;
    padding-left: 0 !important; }
  .p-xl-2 {
    padding: 0.75rem !important; }
  .pt-xl-2,
  .py-xl-2 {
    padding-top: 0.75rem !important; }
  .pr-xl-2,
  .px-xl-2 {
    padding-left: 0.75rem !important;
    padding-right: 0 !important; }
  .pb-xl-2,
  .py-xl-2 {
    padding-bottom: 0.75rem !important; }
  .pl-xl-2,
  .px-xl-2 {
    padding-right: 0.75rem !important;
    padding-left: 0 !important; }
  .p-xl-3 {
    padding: 1.5rem !important; }
  .pt-xl-3,
  .py-xl-3 {
    padding-top: 1.5rem !important; }
  .pr-xl-3,
  .px-xl-3 {
    padding-left: 1.5rem !important;
    padding-right: 0 !important; }
  .pb-xl-3,
  .py-xl-3 {
    padding-bottom: 1.5rem !important; }
  .pl-xl-3,
  .px-xl-3 {
    padding-right: 1.5rem !important;
    padding-left: 0 !important; }
  .p-xl-4 {
    padding: 2.25rem !important; }
  .pt-xl-4,
  .py-xl-4 {
    padding-top: 2.25rem !important; }
  .pr-xl-4,
  .px-xl-4 {
    padding-left: 2.25rem !important;
    padding-right: 0 !important; }
  .pb-xl-4,
  .py-xl-4 {
    padding-bottom: 2.25rem !important; }
  .pl-xl-4,
  .px-xl-4 {
    padding-right: 2.25rem !important;
    padding-left: 0 !important; }
  .p-xl-5 {
    padding: 4.5rem !important; }
  .pt-xl-5,
  .py-xl-5 {
    padding-top: 4.5rem !important; }
  .pr-xl-5,
  .px-xl-5 {
    padding-left: 4.5rem !important;
    padding-right: 0 !important; }
  .pb-xl-5,
  .py-xl-5 {
    padding-bottom: 4.5rem !important; }
  .pl-xl-5,
  .px-xl-5 {
    padding-right: 4.5rem !important;
    padding-left: 0 !important; }
  .m-xl-n1 {
    margin: -0.375rem !important; }
  .mt-xl-n1,
  .my-xl-n1 {
    margin-top: -0.375rem !important; }
  .mr-xl-n1,
  .mx-xl-n1 {
    margin-right: -0.375rem !important; }
  .mb-xl-n1,
  .my-xl-n1 {
    margin-bottom: -0.375rem !important; }
  .ml-xl-n1,
  .mx-xl-n1 {
    margin-left: -0.375rem !important; }
  .m-xl-n2 {
    margin: -0.75rem !important; }
  .mt-xl-n2,
  .my-xl-n2 {
    margin-top: -0.75rem !important; }
  .mr-xl-n2,
  .mx-xl-n2 {
    margin-right: -0.75rem !important; }
  .mb-xl-n2,
  .my-xl-n2 {
    margin-bottom: -0.75rem !important; }
  .ml-xl-n2,
  .mx-xl-n2 {
    margin-left: -0.75rem !important; }
  .m-xl-n3 {
    margin: -1.5rem !important; }
  .mt-xl-n3,
  .my-xl-n3 {
    margin-top: -1.5rem !important; }
  .mr-xl-n3,
  .mx-xl-n3 {
    margin-right: -1.5rem !important; }
  .mb-xl-n3,
  .my-xl-n3 {
    margin-bottom: -1.5rem !important; }
  .ml-xl-n3,
  .mx-xl-n3 {
    margin-left: -1.5rem !important; }
  .m-xl-n4 {
    margin: -2.25rem !important; }
  .mt-xl-n4,
  .my-xl-n4 {
    margin-top: -2.25rem !important; }
  .mr-xl-n4,
  .mx-xl-n4 {
    margin-right: -2.25rem !important; }
  .mb-xl-n4,
  .my-xl-n4 {
    margin-bottom: -2.25rem !important; }
  .ml-xl-n4,
  .mx-xl-n4 {
    margin-left: -2.25rem !important; }
  .m-xl-n5 {
    margin: -4.5rem !important; }
  .mt-xl-n5,
  .my-xl-n5 {
    margin-top: -4.5rem !important; }
  .mr-xl-n5,
  .mx-xl-n5 {
    margin-right: -4.5rem !important; }
  .mb-xl-n5,
  .my-xl-n5 {
    margin-bottom: -4.5rem !important; }
  .ml-xl-n5,
  .mx-xl-n5 {
    margin-left: -4.5rem !important; }
  .m-xl-auto {
    margin: auto !important; }
  .mt-xl-auto,
  .my-xl-auto {
    margin-top: auto !important; }
  .mr-xl-auto,
  .mx-xl-auto {
    margin-left: auto !important;
    margin-right: inherit !important; }
  .mb-xl-auto,
  .my-xl-auto {
    margin-bottom: auto !important; }
  .ml-xl-auto,
  .mx-xl-auto {
    margin-right: auto !important;
    margin-left: auto !important; } }

.float-left {
  float: right !important; }

.float-right {
  float: left !important; }

.float-none {
  float: none !important; }

@media (min-width: 576px) {
  .float-sm-left {
    float: right !important; }
  .float-sm-right {
    float: left !important; }
  .float-sm-none {
    float: none !important; } }

@media (min-width: 768px) {
  .float-md-left {
    float: right !important; }
  .float-md-right {
    float: left !important; }
  .float-md-none {
    float: none !important; } }

@media (min-width: 992px) {
  .float-lg-left {
    float: right !important; }
  .float-lg-right {
    float: left !important; }
  .float-lg-none {
    float: none !important; } }

@media (min-width: 1200px) {
  .float-xl-left {
    float: right !important; }
  .float-xl-right {
    float: left !important; }
  .float-xl-none {
    float: none !important; } }

.text-monospace {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace !important; }

.text-justify {
  text-align: justify !important; }

.text-wrap {
  white-space: normal !important; }

.text-nowrap {
  white-space: nowrap !important; }

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; }

.text-left {
  text-align: right !important; }

.text-right {
  text-align: left !important; }

.text-center {
  text-align: center !important; }

@media (min-width: 576px) {
  .text-sm-left {
    text-align: right !important; }
  .text-sm-right {
    text-align: left !important; }
  .text-sm-center {
    text-align: center !important; } }

@media (min-width: 768px) {
  .text-md-left {
    text-align: right !important; }
  .text-md-right {
    text-align: left !important; }
  .text-md-center {
    text-align: center !important; } }

@media (min-width: 992px) {
  .text-lg-left {
    text-align: right !important; }
  .text-lg-right {
    text-align: left !important; }
  .text-lg-center {
    text-align: center !important; } }

@media (min-width: 1200px) {
  .text-xl-left {
    text-align: right !important; }
  .text-xl-right {
    text-align: left !important; }
  .text-xl-center {
    text-align: center !important; } }

.text-lowercase {
  text-transform: lowercase !important; }

.text-uppercase {
  text-transform: uppercase !important; }

.text-capitalize {
  text-transform: capitalize !important; }

.font-weight-light {
  font-weight: 300 !important; }

.font-weight-lighter {
  font-weight: lighter !important; }

.font-weight-normal {
  font-weight: 400 !important; }

.font-weight-bold {
  font-weight: 700 !important; }

.font-weight-bolder {
  font-weight: bolder !important; }

.font-italic {
  font-style: italic !important; }

.text-white {
  color: #fff !important; }

.text-primary {
  color: #348cd4 !important; }

a.text-primary:hover, a.text-primary:focus {
  color: #21649b !important; }

.text-secondary {
  color: #6c757d !important; }

a.text-secondary:hover, a.text-secondary:focus {
  color: #494f54 !important; }

.text-success {
  color: #3ec396 !important; }

a.text-success:hover, a.text-success:focus {
  color: #2b8a6a !important; }

.text-info {
  color: #4fbde9 !important; }

a.text-info:hover, a.text-info:focus {
  color: #1a9dd1 !important; }

.text-warning {
  color: #f9bc0b !important; }

a.text-warning:hover, a.text-warning:focus {
  color: #b38604 !important; }

.text-danger {
  color: #f36270 !important; }

a.text-danger:hover, a.text-danger:focus {
  color: #ee1b2f !important; }

.text-light {
  color: #f7f7f7 !important; }

a.text-light:hover, a.text-light:focus {
  color: #d1d1d1 !important; }

.text-dark {
  color: #323a46 !important; }

a.text-dark:hover, a.text-dark:focus {
  color: #121519 !important; }

.text-pink {
  color: #e061c9 !important; }

a.text-pink:hover, a.text-pink:focus {
  color: #cc28af !important; }

.text-purple {
  color: #9368f3 !important; }

a.text-purple:hover, a.text-purple:focus {
  color: #6021ed !important; }

.text-body {
  color: #6c757d !important; }

.text-muted {
  color: #98a6ad !important; }

.text-black-50 {
  color: rgba(0, 0, 0, 0.5) !important; }

.text-white-50 {
  color: rgba(255, 255, 255, 0.5) !important; }

.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0; }

.text-decoration-none {
  text-decoration: none !important; }

.text-break {
  word-break: break-word !important;
  overflow-wrap: break-word !important; }

.text-reset {
  color: inherit !important; }

.logo-box {
  float: right; }

.navbar-custom {
  padding: 0 0 0 10px; }
  .navbar-custom .app-search {
    margin-left: 20px;
    margin-right: 0px; }
    .navbar-custom .app-search .input-group-append {
      margin-right: 0; }
    .navbar-custom .app-search .btn {
      border-radius: 30px 0 0 30px !important; }
    .navbar-custom .app-search .form-control {
      padding-right: 20px;
      padding-left: 0;
      border-radius: 0 30px 30px 0 !important; }
  .navbar-custom .topnav-menu > li {
    float: right; }
  .navbar-custom .topnav-menu .nav-link {
    direction: ltr; }

/* Notification */
.notification-list .noti-icon-badge {
  left: 12px;
  right: auto; }

.notification-list .notify-item {
  padding: 12px 20px; }
  .notification-list .notify-item .notify-icon {
    float: right;
    margin-left: 10px;
    margin-right: 0; }
  .notification-list .notify-item .notify-details,
  .notification-list .notify-item .user-msg {
    margin-left: 0;
    margin-right: 45px; }

.notification-list .pro-user-name {
  margin-left: 0.375rem !important;
  margin-right: 0 !important; }

.notification-list .profile-dropdown i {
  margin-left: 5px;
  margin-right: 0px;
  float: right; }

.notification-list .profile-dropdown .notify-item {
  padding: 7px 20px; }

.page-title-box .page-title-right {
  float: left; }

.content-page {
  margin-right: 240px;
  margin-left: 0; }

#sidebar-menu > ul > li > a i {
  margin: 0 3px 0 10px; }

#sidebar-menu > ul > li > a .drop-arrow {
  float: left; }
  #sidebar-menu > ul > li > a .drop-arrow i {
    margin-left: 0; }

#sidebar-menu > ul > li > ul {
  padding-right: 40px;
  padding-left: 0; }
  #sidebar-menu > ul > li > ul ul {
    padding-right: 20px;
    padding-left: 0; }

#sidebar-menu .menu-arrow {
  left: 20px;
  right: auto; }
  #sidebar-menu .menu-arrow:before {
    content: "\F141"; }

#sidebar-menu li.mm-active > a > span.menu-arrow {
  -webkit-transform: rotate(-90deg);
          transform: rotate(-90deg); }

.enlarged .left-side-menu #sidebar-menu > ul > li > a i {
  margin-left: 20px;
  margin-right: 5px; }

.enlarged .left-side-menu #sidebar-menu > ul > li > a span {
  padding-right: 25px;
  padding-left: 0; }

.enlarged .left-side-menu #sidebar-menu > ul > li:hover > ul {
  right: 70px;
  left: auto; }

.enlarged .left-side-menu #sidebar-menu > ul ul li:hover > ul {
  right: 190px;
  margin-top: -36px; }

.enlarged .left-side-menu #sidebar-menu > ul ul li > a span.pull-right {
  left: 20px;
  right: 0; }

.enlarged .navbar-custom {
  right: 0px; }

.enlarged .content-page {
  margin-right: 70px !important;
  margin-left: 0 !important; }

.enlarged .footer {
  left: 0 !important;
  right: 70px !important; }

@media (max-width: 767.98px) {
  .content-page,
  .enlarged .content-page {
    margin-right: 0 !important; } }

.footer {
  left: 0;
  right: 240px; }

.footer-alt {
  right: 0 !important; }

@media (max-width: 767.98px) {
  .footer {
    right: 0 !important; } }

.right-bar {
  float: left !important;
  left: -270px;
  right: auto; }
  .right-bar .user-box .user-img .user-edit {
    right: 0;
    left: -5px; }

.right-bar-enabled .right-bar {
  left: 0;
  right: auto; }

.legendLabel {
  padding-right: 5px !important;
  padding-left: 20px; }

.select2-container .select2-selection--single .select2-selection__rendered {
  padding-right: 12px; }

.select2-container .select2-selection--single .select2-selection__arrow {
  left: 3px;
  right: auto; }

.select2-container .select2-selection--multiple .select2-selection__choice {
  float: right;
  margin-left: 5px;
  margin-right: 0; }

.select2-container .select2-search--inline {
  float: right; }

.bootstrap-select .dropdown-toggle:before {
  float: left; }

.bootstrap-select .dropdown-toggle .filter-option {
  text-align: right; }

.bootstrap-select .dropdown-toggle .filter-option-inner {
  padding-right: 0;
  padding-left: inherit; }

.wizard > .steps .number {
  margin-left: 10px; }

.editable-buttons {
  margin-left: 0;
  margin-right: 7px; }
  .editable-buttons .editable-cancel {
    margin-left: 0;
    margin-right: 7px; }

.dataTables_wrapper .dataTables_filter {
  text-align: left !important; }
  .dataTables_wrapper .dataTables_filter input {
    margin-left: 0px !important;
    margin-right: 0.5em; }

.tablesaw-columntoggle-popup .tablesaw-btn-group > label input {
  margin-right: 0;
  margin-left: .8em; }

.tablesaw-bar .tablesaw-bar-section .tablesaw-btn {
  margin-left: 0;
  margin-right: .4em; }

.table-rep-plugin .btn-group.pull-right {
  float: left; }

.table-rep-plugin .checkbox-row label:after {
  margin-left: -22px;
  top: -2px; }

.ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg {
  left: 0;
  right: auto; }

.ql-editor {
  direction: rtl;
  text-align: right; }

.inbox-widget .inbox-item .inbox-item-img {
  float: right;
  margin-left: 15px;
  margin-right: 0; }

.inbox-widget .inbox-item .inbox-item-date {
  right: auto;
  left: 5px; }

.comment-list .comment-box-item .commnet-item-date {
  left: 7px;
  right: auto; }

.transaction-list .tran-text {
  padding-right: 25px; }

.transaction-list .tran-price {
  margin-right: 30px;
  margin-left: 0px; }

.icons-list-demo i {
  margin-left: 12px;
  margin-right: 0; }

.checkbox label {
  padding-right: 8px;
  padding-left: 0; }
  .checkbox label::before {
    left: auto;
    right: 0;
    margin-left: 0;
    margin-right: -18px; }
  .checkbox label::after {
    left: auto;
    right: 0;
    margin-right: -18px;
    margin-left: 0;
    padding-left: 0;
    padding-right: 3px; }

.checkbox input[type="checkbox"]:checked + label::after {
  left: auto;
  right: 7px;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg); }

.checkbox.checkbox-single label:before {
  margin-right: 0; }

.checkbox.checkbox-single label:after {
  margin-right: 0; }

.radio label {
  padding-left: 0;
  padding-right: 8px; }
  .radio label::before {
    left: auto;
    right: 0;
    margin-left: 0;
    margin-right: -18px; }
  .radio label::after {
    left: 0;
    right: 6px;
    margin-left: 0;
    margin-right: -20px; }

@media print {
  .content-page,
  .content,
  body {
    margin-right: 0; } }

.inbox-leftbar {
  float: right;
  padding: 0 10px 20px 20px; }

.inbox-rightbar {
  margin-right: 250px;
  margin-left: 0px; }

.message-list {
  padding-right: 0; }
  .message-list .col-mail {
    float: right !important; }
  .message-list .col-mail-1 .star-toggle,
  .message-list .col-mail-1 .checkbox-wrapper-mail,
  .message-list .col-mail-1 .dot {
    float: right !important; }
  .message-list .col-mail-1 .dot {
    margin: 22px 0px 0 26px; }
  .message-list .col-mail-1 .checkbox-wrapper-mail {
    margin: 15px 20px 0 10px !important; }
  .message-list .col-mail-1 .star-toggle {
    margin-right: 5px; }
  .message-list .col-mail-1 .title {
    right: 110px !important;
    left: 0 !important; }
  .message-list .col-mail-2 {
    right: 320px !important;
    left: 0 !important; }
    .message-list .col-mail-2 .subject {
      right: 0 !important;
      left: 200px !important; }
    .message-list .col-mail-2 .date {
      left: 0;
      padding-right: 80px;
      padding-left: 0px !important;
      right: auto !important; }

@media (max-width: 648px) {
  .inbox-rightbar {
    margin-right: 0; } }

@media (max-width: 520px) {
  .message-list li .col-mail-1 .title {
    right: 80px !important;
    left: 0px !important; }
  .message-list li .col-mail-2 {
    right: 160px !important; }
    .message-list li .col-mail-2 .date {
      text-align: left;
      padding-left: 10px !important;
      padding-right: 20px; } }

/* =============
   Timeline
============= */
.timeline .time-show {
  margin-left: -75px; }

.timeline:before {
  right: 50%; }

.timeline .timeline-icon {
  right: -54px; }
  .timeline .timeline-icon i {
    right: 4px; }

h3.timeline-title {
  margin: 0 0 5px; }

.timeline-item .timeline-desk .arrow {
  border-left: 12px solid rgba(247, 247, 247, 0.3) !important;
  right: -12px; }

.timeline-item.alt .timeline-desk .arrow-alt {
  border-right: 12px solid rgba(247, 247, 247, 0.9) !important;
  right: auto;
  left: -12px;
  border-left: none !important; }

.timeline-item.alt .timeline-desk .album {
  float: left; }
  .timeline-item.alt .timeline-desk .album a {
    float: left;
    margin-right: 5px; }

.timeline-item.alt .timeline-icon {
  right: auto;
  left: -56px; }

.timeline-item.alt .panel {
  margin-right: 0;
  margin-left: 45px; }

.timeline-item.alt h4 {
  text-align: left; }

.timeline-item.alt p {
  text-align: left; }

.timeline-item.alt .timeline-date {
  text-align: left; }

.timeline-desk .panel {
  margin-right: 45px;
  text-align: right;
  margin-left: 0px; }

.timeline-desk .album a {
  float: right;
  margin-left: 5px; }

.home-btn {
  position: absolute;
  left: 25px;
  right: auto; }
