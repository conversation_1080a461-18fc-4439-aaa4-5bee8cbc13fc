<?php

namespace Database\Factories;

use App\Models\LibraryItem;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\LibraryItem>
 */
class LibraryItemFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = LibraryItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $mediaTypes = ['pdf', 'video', 'audio'];
        $mediaType = $this->faker->randomElement($mediaTypes);
        
        $categories = [
            'Quran', 'Tafsir', 'Hadith', 'Fiqh', 'Seerah', 'Islamic History', 
            'Family', 'Character', 'Spirituality', 'Contemporary Issues'
        ];
        
        $languages = ['English', 'Arabic', 'Urdu', 'French', 'Spanish', 'Indonesian'];
        
        // Generate keywords based on category
        $keywordSets = [
            'Quran' => ['quran', 'recitation', 'tajweed', 'memorization', 'tafsir'],
            'Tafsir' => ['quran', 'explanation', 'interpretation', 'exegesis', 'commentary'],
            'Hadith' => ['hadith', 'sunnah', 'prophet', 'narration', 'bukhari', 'muslim'],
            'Fiqh' => ['fiqh', 'jurisprudence', 'sharia', 'law', 'rulings', 'fatwa'],
            'Seerah' => ['seerah', 'prophet', 'biography', 'muhammad', 'companions'],
            'Islamic History' => ['history', 'civilization', 'caliphate', 'empire', 'scholars'],
            'Family' => ['family', 'marriage', 'parenting', 'children', 'relationships'],
            'Character' => ['character', 'ethics', 'morals', 'manners', 'adab', 'akhlaq'],
            'Spirituality' => ['spirituality', 'heart', 'soul', 'purification', 'dhikr', 'prayer'],
            'Contemporary Issues' => ['contemporary', 'modern', 'issues', 'challenges', 'society', 'west']
        ];
        
        $category = $this->faker->randomElement($categories);
        $possibleKeywords = $keywordSets[$category] ?? ['islam', 'muslim', 'education', 'knowledge'];
        $selectedKeywords = $this->faker->randomElements($possibleKeywords, $this->faker->numberBetween(2, 5));
        
        // Add some general keywords
        if ($this->faker->boolean(30)) {
            $selectedKeywords[] = 'beginner';
        }
        if ($this->faker->boolean(30)) {
            $selectedKeywords[] = 'advanced';
        }
        if ($this->faker->boolean(20)) {
            $selectedKeywords[] = 'children';
        }
        
        // Media type specific attributes
        $duration = null;
        if ($mediaType === 'video' || $mediaType === 'audio') {
            // Generate duration like "1:30:45" or "45:20"
            if ($this->faker->boolean(70)) {
                // Longer format (hours:minutes:seconds)
                $hours = $this->faker->numberBetween(0, 2);
                $minutes = $this->faker->numberBetween(0, 59);
                $seconds = $this->faker->numberBetween(0, 59);
                $duration = sprintf("%d:%02d:%02d", $hours, $minutes, $seconds);
            } else {
                // Shorter format (minutes:seconds)
                $minutes = $this->faker->numberBetween(1, 59);
                $seconds = $this->faker->numberBetween(0, 59);
                $duration = sprintf("%d:%02d", $minutes, $seconds);
            }
        }
        
        // Generate external URLs for videos
        $externalUrl = null;
        if ($mediaType === 'video' && $this->faker->boolean(70)) {
            // YouTube video URLs
            $videoIds = [
                'dQw4w9WgXcQ', 'jNQXAC9IVRw', '9bZkp7q19f0', 'OPf0YbXqDm0', 
                'JGwWNGJdvx8', 'kJQP7kiw5Fk', 'RgKAFK5djSk', 'fJ9rUzIMcZQ',
                'YQHsXMglC9A', 'CevxZvSJLk8', '09R8_2nJtjg', 'pRpeEdMmmQ0'
            ];
            $externalUrl = 'https://www.youtube.com/watch?v=' . $this->faker->randomElement($videoIds);
        }
        
        return [
            'title' => $this->generateTitle($category, $mediaType),
            'author' => $this->faker->boolean(80) ? $this->generateAuthorName() : null,
            'description' => $this->generateDescription($category, $mediaType),
            'media_type' => $mediaType,
            'thumbnail_path' => null, // We'll handle this separately
            'file_path' => null, // We'll handle this separately
            'external_url' => $externalUrl,
            'duration' => $duration,
            'file_size' => $this->faker->boolean(70) ? $this->faker->numberBetween(1, 50) . ' MB' : null,
            'language' => $this->faker->randomElement($languages),
            'category' => $category,
            'keywords' => implode(', ', $selectedKeywords),
            'isactive' => $this->faker->boolean(90), // 90% are active
            'created_by' => 'Seeder',
            'created_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'updated_at' => $this->faker->dateTimeBetween('-6 months', 'now'),
        ];
    }
    
    /**
     * Generate a realistic title based on category and media type
     */
    private function generateTitle($category, $mediaType): string
    {
        $titlePrefixes = [
            'Quran' => [
                'Understanding Surah', 'Tafsir of Juz', 'Recitation of Surah', 
                'Memorizing Juz', 'Tajweed Rules for', 'The Beauty of Surah'
            ],
            'Tafsir' => [
                'Tafsir of Surah', 'Understanding', 'Explanation of', 
                'Deep Dive into', 'Reflections on', 'Lessons from'
            ],
            'Hadith' => [
                'Explanation of', 'Understanding', 'Commentary on', 
                'Lessons from', 'The Wisdom in', 'Applying'
            ],
            'Fiqh' => [
                'Fiqh of', 'Rulings on', 'Understanding', 
                'Practical Guide to', 'Simplified', 'Comparative Fiqh:'
            ],
            'Seerah' => [
                'Life of the Prophet:', 'Lessons from the Seerah:', 'The Prophet\'s', 
                'Muhammad ﷺ:', 'Companions of the Prophet:', 'Stories from the Seerah:'
            ],
            'Islamic History' => [
                'History of', 'Rise and Fall of', 'The Golden Age of', 
                'Islamic Civilization:', 'Great Muslims in', 'Journey through'
            ],
            'Family' => [
                'Building a', 'Guide to', 'Raising', 
                'Strengthening', 'Islamic Perspective on', 'Nurturing'
            ],
            'Character' => [
                'Developing', 'The Importance of', 'Cultivating', 
                'Islamic Ethics:', 'Purification of', 'The Prophet\'s Example of'
            ],
            'Spirituality' => [
                'Journey to', 'Purification of', 'Connecting with', 
                'Strengthening Your', 'Revival of', 'The Path to'
            ],
            'Contemporary Issues' => [
                'Islam and', 'Muslim Response to', 'Navigating', 
                'Islamic Perspective on', 'Dealing with', 'Addressing'
            ]
        ];
        
        $titleSuffixes = [
            'Quran' => [
                'Al-Baqarah', 'Al-Imran', 'An-Nisa', 'Al-Fatiha', 'Ar-Rahman', 
                'Ya-Sin', 'Al-Kahf', 'Al-Mulk', 'Al-Ikhlas', '30', '15', '10'
            ],
            'Tafsir' => [
                'Al-Baqarah', 'the Quran', 'Ayat al-Kursi', 'the Last Ten Surahs', 
                'Juz Amma', 'the Stories of the Prophets', 'the Verses of Mercy'
            ],
            'Hadith' => [
                'Sahih Bukhari', 'Sahih Muslim', '40 Hadith Nawawi', 'Riyadh as-Saliheen', 
                'Hadith Qudsi', 'the Hadith of Gabriel', 'Prophetic Supplications'
            ],
            'Fiqh' => [
                'Salah', 'Zakat', 'Fasting', 'Hajj', 'Marriage', 'Business Transactions', 
                'Inheritance', 'Purification', 'Contemporary Issues', 'Worship'
            ],
            'Seerah' => [
                'Makkah Period', 'Madinah Period', 'Battles', 'Family Life', 
                'Leadership', 'Character', 'Miracles', 'Final Sermon'
            ],
            'Islamic History' => [
                'the Caliphates', 'Muslim Spain', 'the Ottoman Empire', 'Islamic Sciences', 
                'Muslim Contributions', 'the Crusades', 'Islamic Art and Architecture'
            ],
            'Family' => [
                'Strong Muslim Family', 'Islamic Marriage', 'Righteous Children', 
                'Family Bonds', 'Spousal Relations', 'Parenting in Islam'
            ],
            'Character' => [
                'Patience', 'Gratitude', 'Honesty', 'Humility', 'Generosity', 
                'Self-Discipline', 'Compassion', 'Forgiveness'
            ],
            'Spirituality' => [
                'the Heart', 'Allah', 'Inner Peace', 'Spiritual Growth', 
                'the Soul', 'Taqwa', 'Ihsan', 'Divine Love'
            ],
            'Contemporary Issues' => [
                'Modern Technology', 'Social Media', 'Western Society', 'Mental Health', 
                'Environmental Ethics', 'Interfaith Relations', 'Islamophobia'
            ]
        ];
        
        $prefix = $titlePrefixes[$category] ?? ['The Complete Guide to', 'Introduction to', 'Understanding'];
        $suffix = $titleSuffixes[$category] ?? ['Islamic Knowledge', 'Faith', 'Practice', 'Belief'];
        
        $title = $this->faker->randomElement($prefix) . ' ' . $this->faker->randomElement($suffix);
        
        // Add media type specific modifiers
        if ($mediaType === 'video' && $this->faker->boolean(30)) {
            $title = 'Video: ' . $title;
        } elseif ($mediaType === 'audio' && $this->faker->boolean(30)) {
            $title = 'Lecture: ' . $title;
        }
        
        return $title;
    }
    
    /**
     * Generate a realistic Islamic scholar or author name
     */
    private function generateAuthorName(): string
    {
        $commonNames = [
            'Dr. Yasir Qadhi', 'Mufti Menk', 'Nouman Ali Khan', 'Sheikh Omar Suleiman',
            'Dr. Bilal Philips', 'Sheikh Hamza Yusuf', 'Dr. Zakir Naik', 'Imam Siraj Wahhaj',
            'Dr. Jonathan Brown', 'Sheikh Muhammad al-Yaqoubi', 'Ustadh Usama Canon',
            'Dr. Haifaa Younis', 'Ustadha Yasmin Mogahed', 'Sheikh Abdul Nasir Jangda',
            'Dr. Ingrid Mattson', 'Sheikh Abdullah Oduro', 'Dr. Shabir Ally', 'Imam Zaid Shakir',
            'Dr. Israr Ahmad', 'Sheikh Muhammad al-Shareef', 'Dr. Jamal Badawi',
            'Sheikh Abu Eesa Niamatullah', 'Dr. Umar Faruq Abd-Allah', 'Sheikh Suhaib Webb'
        ];
        
        if ($this->faker->boolean(70)) {
            return $this->faker->randomElement($commonNames);
        } else {
            $titles = ['Sheikh', 'Imam', 'Dr.', 'Ustadh', 'Mufti', 'Professor'];
            return $this->faker->randomElement($titles) . ' ' . $this->faker->firstName() . ' ' . $this->faker->lastName();
        }
    }
    
    /**
     * Generate a realistic description based on category and media type
     */
    private function generateDescription($category, $mediaType): string
    {
        $descriptions = [
            'Quran' => [
                "This comprehensive {mediaType} provides a detailed explanation of {subject} with focus on proper recitation and understanding.",
                "Learn the meanings and context of {subject} with word-by-word analysis and historical background.",
                "Improve your connection with the Quran through this in-depth study of {subject}, covering both linguistic and spiritual dimensions."
            ],
            'Tafsir' => [
                "A scholarly explanation of {subject}, examining the historical context, linguistic analysis, and spiritual lessons.",
                "This tafsir of {subject} connects classical interpretations with contemporary relevance for today's Muslims.",
                "Discover the depths of meaning in {subject} through this comprehensive tafsir that combines traditional and modern approaches."
            ],
            'Hadith' => [
                "An authentic collection of hadiths from {subject} with detailed explanations of their meanings and applications.",
                "Learn how to implement the teachings from {subject} in your daily life with practical examples and guidance.",
                "This {mediaType} examines the chain of narration and contextual background of important hadiths from {subject}."
            ],
            'Fiqh' => [
                "A practical guide to the fiqh of {subject}, covering the rulings from different schools of thought.",
                "Learn the Islamic legal framework for {subject} with evidence from Quran and Sunnah and scholarly consensus.",
                "This comprehensive {mediaType} simplifies complex fiqh issues related to {subject} for everyday application."
            ],
            'Seerah' => [
                "Journey through the life of the Prophet Muhammad ﷺ, focusing on {subject} and the lessons we can draw for our lives today.",
                "This biographical {mediaType} examines {subject} from the Prophet's life with authentic narrations and historical context.",
                "Learn about the character, wisdom, and leadership of the Prophet Muhammad ﷺ through the events of {subject}."
            ],
            'Islamic History' => [
                "Explore the rich history of {subject} and its impact on Islamic civilization and the wider world.",
                "This historical {mediaType} presents {subject} with authentic sources, beautiful narratives, and important lessons.",
                "Discover the forgotten achievements and contributions of Muslims in {subject} through this enlightening presentation."
            ],
            'Family' => [
                "Build stronger family relationships through Islamic principles with this guide to {subject}.",
                "Learn practical strategies for {subject} based on Quranic teachings and the Prophetic example.",
                "This {mediaType} addresses common challenges in {subject} and provides Islamic solutions for modern families."
            ],
            'Character' => [
                "Develop the essential quality of {subject} through Quranic teachings and Prophetic examples.",
                "This inspiring {mediaType} shows how {subject} can transform your relationship with Allah and others.",
                "Learn practical steps to cultivate {subject} in your character through daily spiritual exercises."
            ],
            'Spirituality' => [
                "Journey towards spiritual growth through this profound exploration of {subject} in Islamic tradition.",
                "This {mediaType} guides you through the process of purifying your heart and strengthening your connection with {subject}.",
                "Discover ancient spiritual practices related to {subject} that can transform your worship and daily life."
            ],
            'Contemporary Issues' => [
                "An Islamic perspective on {subject} that addresses modern challenges with traditional wisdom.",
                "This timely {mediaType} provides guidance on navigating {subject} while maintaining Islamic principles.",
                "Learn how to respond to {subject} with confidence, knowledge, and wisdom from Islamic sources."
            ]
        ];
        
        $categoryDescriptions = $descriptions[$category] ?? [
            "A comprehensive resource on {subject} for Muslims seeking knowledge.",
            "This {mediaType} provides valuable insights into {subject} from an Islamic perspective.",
            "Learn about {subject} through authentic sources and scholarly explanations."
        ];
        
        $description = $this->faker->randomElement($categoryDescriptions);
        
        // Replace placeholders
        $description = str_replace('{mediaType}', $mediaType, $description);
        $description = str_replace('{subject}', strtolower(substr(strstr($this->generateTitle($category, $mediaType), ' '), 1)), $description);
        
        // Add some additional paragraphs for longer descriptions
        if ($this->faker->boolean(70)) {
            $description .= "\n\n" . $this->faker->paragraph(3);
        }
        
        if ($this->faker->boolean(40)) {
            $description .= "\n\n" . $this->faker->paragraph(2);
        }
        
        return $description;
    }
}