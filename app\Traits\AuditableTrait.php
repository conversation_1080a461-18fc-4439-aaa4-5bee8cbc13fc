<?php

namespace App\Traits;

use App\Models\Audit;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;

trait AuditableTrait
{
    /**
     * Boot the auditable trait
     */
    public static function bootAuditableTrait()
    {
        static::created(function ($model) {
            $model->auditEvent('created');
        });

        static::updated(function ($model) {
            $model->auditEvent('updated');
        });

        static::deleted(function ($model) {
            $model->auditEvent('deleted');
        });
    }

    /**
     * Get audits for this model
     */
    public function audits()
    {
        return $this->morphMany(Audit::class, 'auditable')->orderBy('created_at', 'desc');
    }

    /**
     * Get the attributes that should be audited
     */
    public function getAuditableAttributes(): array
    {
        return $this->fillable ?? [];
    }

    /**
     * Get the attributes that should be excluded from auditing
     */
    public function getAuditExclude(): array
    {
        return [
            'created_at',
            'updated_at',
            'deleted_at',
            'password',
            'remember_token',
        ];
    }

    /**
     * Get custom audit events for this model
     */
    public function getAuditEvents(): array
    {
        return ['created', 'updated', 'deleted'];
    }

    /**
     * Transform audit data before saving
     */
    public function transformAuditData(array $data): array
    {
        // Remove excluded attributes
        $excluded = $this->getAuditExclude();
        return array_diff_key($data, array_flip($excluded));
    }

    /**
     * Get the identifier for audit logs
     */
    public function getAuditIdentifier(): string
    {
        if (isset($this->title)) {
            return $this->title;
        }
        
        if (isset($this->name)) {
            return $this->name;
        }
        
        if (isset($this->surahname)) {
            return $this->surahname;
        }
        
        return $this->getKey();
    }

    /**
     * Create an audit record
     */
    protected function auditEvent(string $event)
    {
        if (!in_array($event, $this->getAuditEvents())) {
            return;
        }

        $oldValues = [];
        $newValues = [];

        if ($event === 'updated') {
            $oldValues = $this->transformAuditData($this->getOriginal());
            $newValues = $this->transformAuditData($this->getAttributes());
            
            // Only audit if there are actual changes
            if (empty(array_diff_assoc($newValues, $oldValues))) {
                return;
            }
        } elseif ($event === 'created') {
            $newValues = $this->transformAuditData($this->getAttributes());
        } elseif ($event === 'deleted') {
            $oldValues = $this->transformAuditData($this->getOriginal());
        }

        $user = Auth::user();

        Audit::create([
            'user_type' => $user ? get_class($user) : null,
            'user_id' => $user ? $user->id : null,
            'event' => $event,
            'auditable_type' => get_class($this),
            'auditable_id' => $this->getKey(),
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'url' => Request::fullUrl(),
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
        ]);
    }

    /**
     * Create a custom audit event
     */
    public function audit(string $event, array $oldValues = [], array $newValues = [], array $tags = [])
    {
        $user = Auth::user();

        Audit::create([
            'user_type' => $user ? get_class($user) : null,
            'user_id' => $user ? $user->id : null,
            'event' => $event,
            'auditable_type' => get_class($this),
            'auditable_id' => $this->getKey(),
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'url' => Request::fullUrl(),
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'tags' => $tags,
        ]);
    }
}
