{"version": 3, "sources": ["app.js"], "names": ["$", "Components", "prototype", "initTooltipPlugin", "fn", "tooltip", "initPopoverPlugin", "popover", "initSlimScrollPlugin", "slimScroll", "height", "position", "size", "touchScrollStep", "color", "initFormValidation", "on", "event", "this", "addClass", "checkValidity", "preventDefault", "stopPropagation", "initCustomModalPlugin", "e", "Custombox", "modal", "content", "target", "attr", "effect", "overlay", "open", "initCounterUp", "each", "idx", "obj", "counterUp", "delay", "time", "initPeityCharts", "colors", "split", "width", "peity", "fill", "data", "initKnob", "knob", "init", "<PERSON><PERSON><PERSON><PERSON>", "window", "j<PERSON><PERSON><PERSON>", "App", "$body", "$window", "_resetSidebarScroll", "slimscroll", "wheelStep", "initMenu", "$this", "toggleClass", "removeClass", "metisMenu", "document", "closest", "length", "hasClass", "pageUrl", "location", "href", "parent", "prev", "slideToggle", "initLayout", "Waves"], "mappings": "CAUA,SAAAA,GACA,aAEA,IAAAC,EAAA,aAGAA,EAAAC,UAAAC,kBAAA,WACAH,EAAAI,GAAAC,SAAAL,EAAA,2BAAAK,WAIAJ,EAAAC,UAAAI,kBAAA,WACAN,EAAAI,GAAAG,SAAAP,EAAA,2BAAAO,WAIAN,EAAAC,UAAAM,qBAAA,WAEAR,EAAAI,GAAAK,YAAAT,EAAA,eAAAS,WAAA,CACAC,OAAA,OACAC,SAAA,QACAC,KAAA,MACAC,gBAAA,GACAC,MAAA,aAKAb,EAAAC,UAAAa,mBAAA,WACAf,EAAA,qBAAAgB,GAAA,SAAA,SAAAC,GAEA,OADAjB,EAAAkB,MAAAC,SAAA,kBACA,IAAAnB,EAAAkB,MAAA,GAAAE,kBACAH,EAAAI,iBACAJ,EAAAK,mBACA,MAOArB,EAAAC,UAAAqB,sBAAA,WACAvB,EAAA,+BAAAgB,GAAA,QAAA,SAAAQ,GACAA,EAAAH,iBACA,IAAAI,UAAAC,MAAA,CACAC,QAAA,CACAC,OAAA5B,EAAAkB,MAAAW,KAAA,QACAC,OAAA9B,EAAAkB,MAAAW,KAAA,mBAEAE,QAAA,CACAjB,MAAAd,EAAAkB,MAAAW,KAAA,wBAIAG,UAKA/B,EAAAC,UAAA+B,cAAA,WACAjC,EAAAkB,MAAAW,KAAA,eAAA7B,EAAAkB,MAAAW,KAAA,cACA7B,EAAAkB,MAAAW,KAAA,cAAA7B,EAAAkB,MAAAW,KAAA,aACA7B,EAAA,6BAAAkC,KAAA,SAAAC,EAAAC,GACApC,EAAAkB,MAAAmB,UAAA,CACAC,MAAA,IACAC,KAAA,UAMAtC,EAAAC,UAAAsC,gBAAA,WACAxC,EAAA,6BAAAkC,KAAA,SAAAC,EAAAC,GACA,IAAAK,EAAAzC,EAAAkB,MAAAW,KAAA,eAAA7B,EAAAkB,MAAAW,KAAA,eAAAa,MAAA,KAAA,GACAC,EAAA3C,EAAAkB,MAAAW,KAAA,cAAA7B,EAAAkB,MAAAW,KAAA,cAAA,GACAnB,EAAAV,EAAAkB,MAAAW,KAAA,eAAA7B,EAAAkB,MAAAW,KAAA,eAAA,GACA7B,EAAAkB,MAAA0B,MAAA,MAAA,CACAC,KAAAJ,EACAE,MAAAA,EACAjC,OAAAA,MAIAV,EAAA,+BAAAkC,KAAA,SAAAC,EAAAC,GACA,IAAAK,EAAAzC,EAAAkB,MAAAW,KAAA,eAAA7B,EAAAkB,MAAAW,KAAA,eAAAa,MAAA,KAAA,GACAC,EAAA3C,EAAAkB,MAAAW,KAAA,cAAA7B,EAAAkB,MAAAW,KAAA,cAAA,GACAnB,EAAAV,EAAAkB,MAAAW,KAAA,eAAA7B,EAAAkB,MAAAW,KAAA,eAAA,GACA7B,EAAAkB,MAAA0B,MAAA,QAAA,CACAC,KAAAJ,EACAE,MAAAA,EACAjC,OAAAA,MAIAV,EAAA,mCAAAkC,KAAA,SAAAC,EAAAC,GACApC,EAAAkB,MAAA0B,MAAA,WAIA5C,EAAA,8BAAAkC,KAAA,SAAAC,EAAAC,GACApC,EAAAkB,MAAA0B,MAAA,OAAA5C,EAAAkB,MAAA4B,UAIA9C,EAAA,6BAAAkC,KAAA,SAAAC,EAAAC,GACA,IAAAK,EAAAzC,EAAAkB,MAAAW,KAAA,eAAA7B,EAAAkB,MAAAW,KAAA,eAAAa,MAAA,KAAA,GACAC,EAAA3C,EAAAkB,MAAAW,KAAA,cAAA7B,EAAAkB,MAAAW,KAAA,cAAA,GACAnB,EAAAV,EAAAkB,MAAAW,KAAA,eAAA7B,EAAAkB,MAAAW,KAAA,eAAA,GACA7B,EAAAkB,MAAA0B,MAAA,MAAA,CACAC,KAAAJ,EACAE,MAAAA,EACAjC,OAAAA,OAKAT,EAAAC,UAAA6C,SAAA,WACA/C,EAAA,wBAAAkC,KAAA,SAAAC,EAAAC,GACApC,EAAAkB,MAAA8B,UAKA/C,EAAAC,UAAA+C,KAAA,WAEA/B,KAAAf,oBACAe,KAAAZ,oBACAY,KAAAV,uBACAU,KAAAH,qBACAG,KAAAK,wBACAL,KAAAe,gBACAf,KAAAsB,kBACAtB,KAAA6B,YAGA/C,EAAAC,WAAA,IAAAA,EAAAD,EAAAC,WAAAiD,YAAAjD,EAvIA,CAyIAkD,OAAAC,QAGA,SAAApD,GACA,aAEA,IAAAqD,EAAA,WACAnC,KAAAoC,MAAAtD,EAAA,QACAkB,KAAAqC,QAAAvD,EAAAmD,SAMAE,EAAAnD,UAAAsD,oBAAA,WAEAxD,EAAA,oBAAAyD,WAAA,CACA/C,OAAA,OACAC,SAAA,QACAC,KAAA,MACAE,MAAA,UACA4C,UAAA,EACA7C,gBAAA,MAOAwC,EAAAnD,UAAAyD,SAAA,WACA,IAAAC,EAAA1C,KAGAlB,EAAA,uBAAAgB,GAAA,QAAA,SAAAC,GACAA,EAAAI,iBACAuC,EAAAN,MAAAO,YAAA,kBACA,KAAAD,EAAAL,QAAAZ,QACAiB,EAAAN,MAAAO,YAAA,YAEAD,EAAAN,MAAAQ,YAAA,YAIAF,EAAAJ,wBAIAxD,EAAA,cAAA+D,YAGAH,EAAAJ,sBAGAxD,EAAA,qBAAAgB,GAAA,QAAA,SAAAQ,GACAxB,EAAA,QAAA6D,YAAA,uBAGA7D,EAAAgE,UAAAhD,GAAA,QAAA,OAAA,SAAAQ,GACA,EAAAxB,EAAAwB,EAAAI,QAAAqC,QAAA,iCAAAC,QAIA,EAAAlE,EAAAwB,EAAAI,QAAAqC,QAAA,8BAAAC,QAAAlE,EAAAwB,EAAAI,QAAAuC,SAAA,uBACA,EAAAnE,EAAAwB,EAAAI,QAAAqC,QAAA,uBAAAC,SAIAlE,EAAA,QAAA8D,YAAA,qBACA9D,EAAA,QAAA8D,YAAA,qBAKA9D,EAAA,gBAAAkC,KAAA,WACA,IAAAkC,EAAAjB,OAAAkB,SAAAC,KAAA5B,MAAA,QAAA,GACAxB,KAAAoD,MAAAF,IACApE,EAAAkB,MAAAC,SAAA,UACAnB,EAAAkB,MAAAqD,SAAApD,SAAA,aACAnB,EAAAkB,MAAAqD,SAAAA,SAAApD,SAAA,WACAnB,EAAAkB,MAAAqD,SAAAA,SAAAC,OAAArD,SAAA,UACAnB,EAAAkB,MAAAqD,SAAAA,SAAAA,SAAApD,SAAA,aACAnB,EAAAkB,MAAAqD,SAAAA,SAAAA,SAAAA,SAAApD,SAAA,WACAnB,EAAAkB,MAAAqD,SAAAA,SAAAA,SAAAA,SAAAA,SAAApD,SAAA,gBAKAnB,EAAA,kBAAAgB,GAAA,QAAA,SAAAC,GACAjB,EAAAkB,MAAA2C,YAAA,QACA7D,EAAA,eAAAyE,YAAA,QAQApB,EAAAnD,UAAAwE,WAAA,WAEA,KAAAxD,KAAAqC,QAAAZ,SAAAzB,KAAAqC,QAAAZ,SAAA,KACAzB,KAAAoC,MAAAnC,SAAA,YAEA,GAAAD,KAAAoC,MAAAR,KAAA,kBACA5B,KAAAoC,MAAAQ,YAAA,aAMAT,EAAAnD,UAAA+C,KAAA,WACA,IAAAW,EAAA1C,KACAA,KAAAwD,aACAxD,KAAAyC,WACA3D,EAAAC,WAAAgD,OAEAW,EAAAL,QAAAvC,GAAA,SAAA,SAAAQ,GACAA,EAAAH,iBACAuC,EAAAc,aACAd,EAAAJ,yBAIAxD,EAAAqD,IAAA,IAAAA,EAAArD,EAAAqD,IAAAH,YAAAG,EAvHA,CA0HAF,OAAAC,QAEA,SAAApD,GACA,aAEAmD,OAAAC,OADAC,IAAAJ,OAFA,GAMA0B,MAAA1B", "file": "app.min.js", "sourcesContent": ["/*\r\nTemplate Name: Abstack - Responsive Bootstrap 4 Admin Dashboard\r\nAuthor: CoderThemes\r\nVersion: 2.0.0\r\nWebsite: https://coderthemes.com/\r\nContact: <EMAIL>\r\nFile: Main Js File\r\n*/\r\n\r\n\r\n!function ($) {\r\n    \"use strict\";\r\n\r\n    var Components = function () { };\r\n\r\n    //initializing tooltip\r\n    Components.prototype.initTooltipPlugin = function () {\r\n        $.fn.tooltip && $('[data-toggle=\"tooltip\"]').tooltip()\r\n    },\r\n\r\n    //initializing popover\r\n    Components.prototype.initPopoverPlugin = function () {\r\n        $.fn.popover && $('[data-toggle=\"popover\"]').popover()\r\n    },\r\n\r\n    //initializing Slimscroll\r\n    Components.prototype.initSlimScrollPlugin = function () {\r\n        //You can change the color of scroll bar here\r\n        $.fn.slimScroll && $(\".slimscroll\").slimScroll({\r\n            height: 'auto',\r\n            position: 'right',\r\n            size: \"6px\",\r\n            touchScrollStep: 20,\r\n            color: '#9ea5ab'\r\n        });\r\n    },\r\n\r\n    //initializing form validation\r\n    Components.prototype.initFormValidation = function () {\r\n        $(\".needs-validation\").on('submit', function (event) {\r\n            $(this).addClass('was-validated');\r\n            if ($(this)[0].checkValidity() === false) {\r\n                event.preventDefault();\r\n                event.stopPropagation();\r\n                return false;\r\n            }\r\n            return true;\r\n        });\r\n    },\r\n\r\n    //initializing custom modal\r\n    Components.prototype.initCustomModalPlugin = function() {\r\n        $('[data-plugin=\"custommodal\"]').on('click', function(e) {\r\n            e.preventDefault();\r\n            var modal = new Custombox.modal({\r\n                content: {\r\n                    target: $(this).attr(\"href\"),\r\n                    effect: $(this).attr(\"data-animation\")\r\n                },\r\n                overlay: {\r\n                    color: $(this).attr(\"data-overlayColor\")\r\n                }\r\n            });\r\n            // Open\r\n            modal.open();\r\n        });\r\n    },\r\n\r\n    // Counterup\r\n    Components.prototype.initCounterUp = function() {\r\n        var delay = $(this).attr('data-delay')?$(this).attr('data-delay'):100; //default is 100\r\n        var time = $(this).attr('data-time')?$(this).attr('data-time'):1200; //default is 1200\r\n         $('[data-plugin=\"counterup\"]').each(function(idx, obj) {\r\n            $(this).counterUp({\r\n                delay: 100,\r\n                time: 1200\r\n            });\r\n         });\r\n    },\r\n\r\n    //peity charts\r\n    Components.prototype.initPeityCharts = function() {\r\n        $('[data-plugin=\"peity-pie\"]').each(function(idx, obj) {\r\n            var colors = $(this).attr('data-colors')?$(this).attr('data-colors').split(\",\"):[];\r\n            var width = $(this).attr('data-width')?$(this).attr('data-width'):20; //default is 20\r\n            var height = $(this).attr('data-height')?$(this).attr('data-height'):20; //default is 20\r\n            $(this).peity(\"pie\", {\r\n                fill: colors,\r\n                width: width,\r\n                height: height\r\n            });\r\n        });\r\n        //donut\r\n         $('[data-plugin=\"peity-donut\"]').each(function(idx, obj) {\r\n            var colors = $(this).attr('data-colors')?$(this).attr('data-colors').split(\",\"):[];\r\n            var width = $(this).attr('data-width')?$(this).attr('data-width'):20; //default is 20\r\n            var height = $(this).attr('data-height')?$(this).attr('data-height'):20; //default is 20\r\n            $(this).peity(\"donut\", {\r\n                fill: colors,\r\n                width: width,\r\n                height: height\r\n            });\r\n        });\r\n\r\n        $('[data-plugin=\"peity-donut-alt\"]').each(function(idx, obj) {\r\n            $(this).peity(\"donut\");\r\n        });\r\n\r\n        // line\r\n        $('[data-plugin=\"peity-line\"]').each(function(idx, obj) {\r\n            $(this).peity(\"line\", $(this).data());\r\n        });\r\n\r\n        // bar\r\n        $('[data-plugin=\"peity-bar\"]').each(function(idx, obj) {\r\n            var colors = $(this).attr('data-colors')?$(this).attr('data-colors').split(\",\"):[];\r\n            var width = $(this).attr('data-width')?$(this).attr('data-width'):20; //default is 20\r\n            var height = $(this).attr('data-height')?$(this).attr('data-height'):20; //default is 20\r\n            $(this).peity(\"bar\", {\r\n                fill: colors,\r\n                width: width,\r\n                height: height\r\n            });\r\n         });\r\n    },\r\n\r\n    Components.prototype.initKnob = function() {\r\n        $('[data-plugin=\"knob\"]').each(function(idx, obj) {\r\n           $(this).knob();\r\n        });\r\n    },\r\n\r\n    //initilizing\r\n    Components.prototype.init = function () {\r\n        var $this = this;\r\n        this.initTooltipPlugin(),\r\n        this.initPopoverPlugin(),\r\n        this.initSlimScrollPlugin(),\r\n        this.initFormValidation(),\r\n        this.initCustomModalPlugin(),\r\n        this.initCounterUp(),\r\n        this.initPeityCharts(),\r\n        this.initKnob();\r\n    },\r\n\r\n    $.Components = new Components, $.Components.Constructor = Components\r\n\r\n}(window.jQuery),\r\n\r\n\r\nfunction ($) {\r\n    'use strict';\r\n\r\n    var App = function () {\r\n        this.$body = $('body'),\r\n        this.$window = $(window)\r\n    };\r\n\r\n    /**\r\n    Resets the scroll\r\n    */\r\n    App.prototype._resetSidebarScroll = function () {\r\n        // sidebar - scroll container\r\n        $('.slimscroll-menu').slimscroll({\r\n            height: 'auto',\r\n            position: 'right',\r\n            size: \"6px\",\r\n            color: '#9ea5ab',\r\n            wheelStep: 5,\r\n            touchScrollStep: 20\r\n        });\r\n    },\r\n\r\n    /** \r\n     * Initlizes the menu - top and sidebar\r\n    */\r\n    App.prototype.initMenu = function () {\r\n        var $this = this;\r\n\r\n        // Left menu collapse\r\n        $('.button-menu-mobile').on('click', function (event) {\r\n            event.preventDefault();\r\n            $this.$body.toggleClass('sidebar-enable');\r\n            if ($this.$window.width() >= 768) {\r\n                $this.$body.toggleClass('enlarged');\r\n            } else {\r\n                $this.$body.removeClass('enlarged');\r\n            }\r\n\r\n            // sidebar - scroll container\r\n            $this._resetSidebarScroll();\r\n        });\r\n\r\n        // sidebar - main menu\r\n        $(\"#side-menu\").metisMenu();\r\n\r\n        // sidebar - scroll container\r\n        $this._resetSidebarScroll();\r\n\r\n        // right side-bar toggle\r\n        $('.right-bar-toggle').on('click', function (e) {\r\n            $('body').toggleClass('right-bar-enabled');\r\n        });\r\n\r\n        $(document).on('click', 'body', function (e) {\r\n            if ($(e.target).closest('.right-bar-toggle, .right-bar').length > 0) {\r\n                return;\r\n            }\r\n\r\n            if ($(e.target).closest('.left-side-menu, .side-nav').length > 0 || $(e.target).hasClass('button-menu-mobile')\r\n                || $(e.target).closest('.button-menu-mobile').length > 0) {\r\n                return;\r\n            }\r\n\r\n            $('body').removeClass('right-bar-enabled');\r\n            $('body').removeClass('sidebar-enable');\r\n            return;\r\n        });\r\n\r\n        // activate the menu in left side bar based on url\r\n        $(\"#side-menu a\").each(function () {\r\n            var pageUrl = window.location.href.split(/[?#]/)[0];\r\n            if (this.href == pageUrl) {\r\n                $(this).addClass(\"active\");\r\n                $(this).parent().addClass(\"mm-active\"); // add active to li of the current link\r\n                $(this).parent().parent().addClass(\"mm-show\");\r\n                $(this).parent().parent().prev().addClass(\"active\"); // add active class to an anchor\r\n                $(this).parent().parent().parent().addClass(\"mm-active\");\r\n                $(this).parent().parent().parent().parent().addClass(\"mm-show\"); // add active to li of the current link\r\n                $(this).parent().parent().parent().parent().parent().addClass(\"mm-active\");\r\n            }\r\n        });\r\n\r\n        // Topbar - main menu\r\n        $('.navbar-toggle').on('click', function (event) {\r\n            $(this).toggleClass('open');\r\n            $('#navigation').slideToggle(400);\r\n        });\r\n\r\n    },\r\n\r\n    /** \r\n     * Init the layout - with broad sidebar or compact side bar\r\n    */\r\n    App.prototype.initLayout = function () {\r\n        // in case of small size, add class enlarge to have minimal menu\r\n        if (this.$window.width() >= 768 && this.$window.width() <= 1024) {\r\n            this.$body.addClass('enlarged');\r\n        } else {\r\n            if (this.$body.data('keep-enlarged') != true) {\r\n                this.$body.removeClass('enlarged');\r\n            }\r\n        }\r\n    },\r\n\r\n    //initilizing\r\n    App.prototype.init = function () {\r\n        var $this = this;\r\n        this.initLayout();\r\n        this.initMenu();\r\n        $.Components.init();\r\n        // on window resize, make menu flipped automatically\r\n        $this.$window.on('resize', function (e) {\r\n            e.preventDefault();\r\n            $this.initLayout();\r\n            $this._resetSidebarScroll();\r\n        });\r\n    },\r\n\r\n    $.App = new App, $.App.Constructor = App\r\n\r\n\r\n}(window.jQuery),\r\n//initializing main application module\r\nfunction ($) {\r\n    \"use strict\";\r\n    $.App.init();\r\n}(window.jQuery);\r\n\r\n// Waves Effect\r\nWaves.init();"]}