/*
Template Name: Abstack - Responsive Bootstrap 4 Admin Dashboard
Author: CoderThemes
Version: 2.0.0
Website: https://coderthemes.com/
Contact: <EMAIL>
File: Main Css File
*/
@import url("https://fonts.googleapis.com/css?family=Nunito+Sans:400,600,700|Roboto:300,400,500,700&display=swap");
html {
  position: relative;
  min-height: 100%; }

body {
  padding-bottom: 60px;
  overflow-x: hidden; }

.metismenu {
  padding: 0; }
  .metismenu li {
    list-style: none; }
  .metismenu ul {
    padding: 0; }
    .metismenu ul li {
      width: 100%; }
  .metismenu .mm-collapse:not(.mm-show) {
    display: none; }
  .metismenu .mm-collapsing {
    position: relative;
    height: 0;
    overflow: hidden;
    -webkit-transition-timing-function: ease;
            transition-timing-function: ease;
    -webkit-transition-duration: .35s;
            transition-duration: .35s;
    -webkit-transition-property: height, visibility;
    transition-property: height, visibility; }

.nav-second-level li a,
.nav-thrid-level li a {
  padding: 8px 20px;
  color: #6e768e;
  display: block;
  position: relative;
  -webkit-transition: all 0.4s;
  transition: all 0.4s; }
  .nav-second-level li a:focus, .nav-second-level li a:hover,
  .nav-thrid-level li a:focus,
  .nav-thrid-level li a:hover {
    color: #5d6dc3; }

.nav-second-level li.mm-active > a,
.nav-third-level li.mm-active > a {
  color: #5d6dc3; }

#wrapper {
  height: 100%;
  overflow: hidden;
  width: 100%; }

.content-page {
  margin-left: 240px;
  overflow: hidden;
  padding: 0 15px 5px 15px;
  min-height: 80vh;
  margin-top: 70px; }

.left-side-menu {
  width: 240px;
  background: #fff;
  bottom: 0;
  padding: 20px 0;
  position: fixed;
  -webkit-transition: all .2s ease-out;
  transition: all .2s ease-out;
  top: 70px;
  -webkit-box-shadow: 0px 0px 35px 0px rgba(154, 161, 171, 0.15);
          box-shadow: 0px 0px 35px 0px rgba(154, 161, 171, 0.15);
  z-index: 10; }

#sidebar-menu > ul > li > a {
  color: #6e768e;
  display: block;
  padding: 13px 20px;
  position: relative;
  -webkit-transition: all 0.4s;
  transition: all 0.4s;
  font-family: "Roboto", sans-serif;
  font-size: 15px; }
  #sidebar-menu > ul > li > a:hover, #sidebar-menu > ul > li > a:focus, #sidebar-menu > ul > li > a:active {
    color: #5d6dc3;
    text-decoration: none;
    background: #f7f7f7; }
  #sidebar-menu > ul > li > a > span {
    vertical-align: middle; }
  #sidebar-menu > ul > li > a i {
    display: inline-block;
    line-height: 1.0625rem;
    margin: 0 10px 0 3px;
    text-align: center;
    vertical-align: middle;
    width: 20px;
    font-size: 16px; }
  #sidebar-menu > ul > li > a .drop-arrow {
    float: right; }
    #sidebar-menu > ul > li > a .drop-arrow i {
      margin-right: 0; }

#sidebar-menu > ul > li > a.active {
  color: #5d6dc3;
  background: #f7f7f7; }

#sidebar-menu > ul > li > ul {
  padding-left: 37px; }
  #sidebar-menu > ul > li > ul ul {
    padding-left: 20px; }

#sidebar-menu .menu-arrow {
  -webkit-transition: -webkit-transform .15s;
  transition: -webkit-transform .15s;
  transition: transform .15s;
  transition: transform .15s, -webkit-transform .15s;
  position: absolute;
  right: 20px;
  display: inline-block;
  font-family: 'Material Design Icons';
  text-rendering: auto;
  line-height: 1.5rem;
  font-size: 1.1rem;
  -webkit-transform: translate(0, 0);
          transform: translate(0, 0); }
  #sidebar-menu .menu-arrow:before {
    content: "\F142"; }

#sidebar-menu .badge {
  margin-top: 4px; }

#sidebar-menu li.mm-active > a > span.menu-arrow {
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg); }

#sidebar-menu .menu-title {
  padding: 10px 20px;
  letter-spacing: .05em;
  pointer-events: none;
  cursor: default;
  font-size: 0.6875rem;
  text-transform: uppercase;
  color: #6e768e;
  font-weight: 600; }

.enlarged .logo-box {
  width: 70px !important; }

.enlarged .logo span.logo-lg {
  display: none; }

.enlarged .logo span.logo-sm {
  display: block; }

.enlarged .left-side-menu {
  position: absolute;
  padding-top: 0;
  width: 70px !important;
  z-index: 5; }
  .enlarged .left-side-menu .slimScrollDiv,
  .enlarged .left-side-menu .slimscroll-menu {
    overflow: inherit !important;
    height: auto !important; }
  .enlarged .left-side-menu .slimScrollBar {
    visibility: hidden; }
  .enlarged .left-side-menu #sidebar-menu .menu-title,
  .enlarged .left-side-menu #sidebar-menu .menu-arrow,
  .enlarged .left-side-menu #sidebar-menu .label,
  .enlarged .left-side-menu #sidebar-menu .badge {
    display: none !important; }
  .enlarged .left-side-menu #sidebar-menu > ul > li {
    position: relative;
    white-space: nowrap; }
    .enlarged .left-side-menu #sidebar-menu > ul > li > a {
      padding: 15px 20px;
      min-height: 54px;
      -webkit-transition: none;
      transition: none; }
      .enlarged .left-side-menu #sidebar-menu > ul > li > a:hover, .enlarged .left-side-menu #sidebar-menu > ul > li > a:active, .enlarged .left-side-menu #sidebar-menu > ul > li > a:focus {
        color: #5d6dc3; }
      .enlarged .left-side-menu #sidebar-menu > ul > li > a i {
        font-size: 1.125rem;
        margin-right: 20px; }
      .enlarged .left-side-menu #sidebar-menu > ul > li > a span {
        display: none;
        padding-left: 25px; }
    .enlarged .left-side-menu #sidebar-menu > ul > li:hover > a {
      position: relative;
      width: calc(190px + 70px);
      color: #5d6dc3;
      background-color: #f7f7f7;
      -webkit-transition: none;
      transition: none; }
      .enlarged .left-side-menu #sidebar-menu > ul > li:hover > a span {
        display: inline; }
    .enlarged .left-side-menu #sidebar-menu > ul > li:hover a.open :after, .enlarged .left-side-menu #sidebar-menu > ul > li:hover a.mm-active :after {
      display: none; }
    .enlarged .left-side-menu #sidebar-menu > ul > li:hover > ul {
      display: block;
      left: 70px;
      position: absolute;
      width: 190px;
      height: auto !important;
      -webkit-box-shadow: 3px 5px 10px 0 rgba(154, 161, 171, 0.2);
              box-shadow: 3px 5px 10px 0 rgba(154, 161, 171, 0.2); }
      .enlarged .left-side-menu #sidebar-menu > ul > li:hover > ul ul {
        -webkit-box-shadow: 3px 5px 10px 0 rgba(154, 161, 171, 0.2);
                box-shadow: 3px 5px 10px 0 rgba(154, 161, 171, 0.2); }
      .enlarged .left-side-menu #sidebar-menu > ul > li:hover > ul a {
        -webkit-box-shadow: none;
                box-shadow: none;
        padding: 8px 20px;
        position: relative;
        width: 190px;
        z-index: 6; }
        .enlarged .left-side-menu #sidebar-menu > ul > li:hover > ul a:hover {
          color: #5d6dc3; }
  .enlarged .left-side-menu #sidebar-menu > ul ul {
    padding: 5px 0;
    z-index: 9999;
    display: none;
    background-color: #fff; }
    .enlarged .left-side-menu #sidebar-menu > ul ul li:hover > ul {
      display: block;
      left: 190px;
      margin-top: -36px;
      height: auto !important;
      position: absolute;
      width: 190px; }
    .enlarged .left-side-menu #sidebar-menu > ul ul li > a span.pull-right {
      position: absolute;
      right: 20px;
      top: 12px;
      -webkit-transform: rotate(270deg);
              transform: rotate(270deg); }
    .enlarged .left-side-menu #sidebar-menu > ul ul li.active a {
      color: #5d6dc3; }

.enlarged .content-page {
  margin-left: 70px !important; }

.enlarged .footer {
  left: 70px !important; }

.enlarged .user-box {
  display: none; }

body.enlarged {
  min-height: 1200px; }

@media (max-width: 767.98px) {
  body {
    overflow-x: hidden;
    padding-bottom: 80px; }
  .left-side-menu {
    display: none;
    z-index: 999 !important; }
  .sidebar-enable .left-side-menu {
    display: block; }
  .content-page, .enlarged .content-page {
    margin-left: 0 !important;
    padding: 0 10px; }
  .pro-user-name {
    display: none; }
  .logo-box {
    display: none; } }

.logo {
  display: block;
  line-height: 70px; }
  .logo span.logo-lg {
    display: block; }
  .logo span.logo-sm {
    display: none; }
  .logo .logo-lg-text-dark {
    color: #323a46;
    font-weight: 700;
    font-size: 22px;
    text-transform: uppercase; }
  .logo .logo-lg-text-light {
    color: #fff;
    font-weight: 700;
    font-size: 22px;
    text-transform: uppercase; }

.logo-box {
  height: 70px;
  width: 240px;
  float: left; }

.navbar-custom {
  background: -webkit-gradient(linear, left bottom, left top, from(#5d6dc3), to(#3c86d8));
  background: linear-gradient(to top, #5d6dc3, #3c86d8);
  -webkit-box-shadow: 0px 0px 35px 0px rgba(154, 161, 171, 0.15);
          box-shadow: 0px 0px 35px 0px rgba(154, 161, 171, 0.15);
  padding: 0 10px 0 0;
  position: fixed;
  left: 0;
  right: 0;
  height: 70px;
  z-index: 100;
  /* Search */ }
  .navbar-custom .topnav-menu > li {
    float: left; }
  .navbar-custom .topnav-menu .nav-link {
    padding: 0 15px;
    color: rgba(255, 255, 255, 0.6);
    min-width: 32px;
    display: block;
    line-height: 70px;
    text-align: center;
    max-height: 70px; }
  .navbar-custom .dropdown.show .nav-link {
    background-color: rgba(255, 255, 255, 0.05); }
  .navbar-custom .app-search {
    overflow: hidden;
    height: 70px;
    display: table;
    max-width: 200px;
    margin-right: 20px; }
    .navbar-custom .app-search .app-search-box {
      display: table-cell;
      vertical-align: middle; }
      .navbar-custom .app-search .app-search-box input::-webkit-input-placeholder {
        font-size: 0.8125rem;
        color: rgba(255, 255, 255, 0.7); }
    .navbar-custom .app-search .form-control {
      border: none;
      height: 38px;
      padding-left: 20px;
      padding-right: 0;
      color: #fff;
      background-color: rgba(255, 255, 255, 0.07);
      -webkit-box-shadow: none;
              box-shadow: none;
      border-radius: 30px 0 0 30px; }
    .navbar-custom .app-search .input-group-append {
      margin-left: 0;
      z-index: 4; }
    .navbar-custom .app-search .btn {
      background-color: rgba(255, 255, 255, 0.07);
      color: #fff;
      border-color: transparent;
      border-radius: 0 30px 30px 0;
      -webkit-box-shadow: none !important;
              box-shadow: none !important; }
  .navbar-custom .button-menu-mobile {
    border: none;
    color: #fff;
    display: inline-block;
    height: 70px;
    line-height: 70px;
    width: 60px;
    background-color: transparent;
    font-size: 24px;
    cursor: pointer; }
  .navbar-custom .button-menu-mobile.disable-btn {
    display: none; }

/* Notification */
.noti-scroll {
  max-height: 230px; }

.notification-list {
  margin-left: 0; }
  .notification-list .noti-title {
    background-color: #fff;
    padding: 15px 20px; }
  .notification-list .noti-icon {
    font-size: 21px;
    vertical-align: middle; }
  .notification-list .noti-icon-badge {
    display: inline-block;
    position: absolute;
    top: 16px;
    right: 10px; }
  .notification-list .notify-item {
    padding: 12px 20px; }
    .notification-list .notify-item .notify-icon {
      float: left;
      height: 36px;
      width: 36px;
      font-size: 18px;
      line-height: 36px;
      text-align: center;
      margin-top: 4px;
      margin-right: 10px;
      border-radius: 50%;
      color: #fff; }
    .notification-list .notify-item .notify-details {
      margin-bottom: 5px;
      overflow: hidden;
      margin-left: 45px;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #414d5f;
      font-weight: 500; }
      .notification-list .notify-item .notify-details b {
        font-weight: 500; }
      .notification-list .notify-item .notify-details small {
        display: block; }
      .notification-list .notify-item .notify-details span {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 13px; }
    .notification-list .notify-item .user-msg {
      margin-left: 45px;
      white-space: normal;
      line-height: 16px; }
  .notification-list .profile-dropdown .notify-item {
    padding: 7px 20px; }

.profile-dropdown {
  width: 170px; }
  .profile-dropdown i {
    vertical-align: middle;
    margin-right: 5px; }

.nav-user {
  padding: 0 12px !important; }
  .nav-user img {
    height: 32px;
    width: 32px; }

.page-title-box {
  padding: 0px 20px;
  margin: 0 -27px 30px -27px;
  background-color: #fff;
  -webkit-box-shadow: 0px 0px 35px 0px rgba(154, 161, 171, 0.15);
          box-shadow: 0px 0px 35px 0px rgba(154, 161, 171, 0.15); }
  .page-title-box .page-title {
    font-size: 18px;
    margin: 0;
    line-height: 50px;
    font-weight: 700; }
  .page-title-box .page-title-right {
    float: right; }
  .page-title-box .breadcrumb {
    margin-bottom: 0;
    padding: 14px 0; }

@media (max-width: 767.98px) {
  .page-title-box .page-title {
    display: block;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden; }
  .page-title-box .breadcrumb {
    display: none; } }

@media (max-width: 640px) {
  .page-title-box .page-title-right {
    display: none; } }

@media (max-width: 419px) {
  .page-title-box .breadcrumb, .nav-user span {
    display: none; } }

.footer {
  bottom: 0;
  padding: 19px 15px 20px;
  position: absolute;
  right: 0;
  color: #98a6ad;
  left: 240px;
  border-top: 1px solid #dee2e6;
  text-align: center; }

@media (max-width: 767.98px) {
  .footer {
    left: 0 !important;
    text-align: center; } }

.right-bar {
  background-color: #fff;
  -webkit-box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);
          box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);
  display: block;
  position: fixed;
  -webkit-transition: all 200ms ease-out;
  transition: all 200ms ease-out;
  width: 260px;
  z-index: 9999;
  float: right !important;
  right: -270px;
  top: 0;
  bottom: 0; }
  .right-bar .rightbar-title {
    background: -webkit-gradient(linear, left bottom, left top, from(#5d6dc3), to(#3c86d8));
    background: linear-gradient(to top, #5d6dc3, #3c86d8);
    padding: 27px 25px;
    color: #fff; }
  .right-bar .right-bar-toggle {
    background-color: #414b5b;
    height: 24px;
    width: 24px;
    line-height: 24px;
    color: #fff;
    text-align: center;
    border-radius: 50%;
    margin-top: -4px; }
    .right-bar .right-bar-toggle:hover {
      background-color: #475364; }
  .right-bar .user-box {
    padding: 25px;
    text-align: center; }
    .right-bar .user-box .user-img {
      position: relative;
      height: 64px;
      width: 64px;
      margin: 0 auto 15px auto; }
      .right-bar .user-box .user-img .user-edit {
        position: absolute;
        right: -5px;
        bottom: 0px;
        height: 24px;
        width: 24px;
        background-color: #fff;
        line-height: 24px;
        border-radius: 50%;
        -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);
                box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12); }
    .right-bar .user-box h5 {
      margin-bottom: 2px; }
      .right-bar .user-box h5 a {
        color: #323a46; }

.rightbar-overlay {
  background-color: rgba(50, 58, 70, 0.55);
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: none;
  z-index: 9998;
  -webkit-transition: all .2s ease-out;
  transition: all .2s ease-out; }

.right-bar-enabled .right-bar {
  right: 0; }

.right-bar-enabled .rightbar-overlay {
  display: block; }

@media (max-width: 767.98px) {
  .right-bar {
    overflow: auto; }
    .right-bar .slimscroll-menu {
      height: auto !important; } }

.activity-widget .activity-list {
  position: relative;
  border-left: 2px dashed #ced4da;
  padding-left: 24px;
  padding-bottom: 2px; }
  .activity-widget .activity-list::after {
    content: "";
    position: absolute;
    left: -7px;
    top: 6px;
    width: 12px;
    height: 12px;
    background-color: #fff;
    border: 2px solid #5d6dc3;
    border-radius: 50%; }

.inbox-widget .inbox-item {
  overflow: hidden;
  padding: 0.625rem 0;
  position: relative; }
  .inbox-widget .inbox-item .inbox-item-img {
    display: block;
    float: left;
    margin-right: 15px;
    margin-top: 4px; }
    .inbox-widget .inbox-item .inbox-item-img img {
      width: 40px; }
  .inbox-widget .inbox-item .inbox-item-author {
    display: block;
    margin-bottom: 0px;
    font-weight: 600; }
    .inbox-widget .inbox-item .inbox-item-author a {
      color: #6c757d; }
  .inbox-widget .inbox-item .inbox-item-text {
    color: #98a6ad;
    display: block;
    margin: 0;
    overflow: hidden; }
  .inbox-widget .inbox-item .inbox-item-date {
    color: #98a6ad;
    font-size: 0.6875rem;
    position: absolute;
    right: 5px;
    top: 10px; }

.width-xs {
  min-width: 80px; }

.width-sm {
  min-width: 95px; }

.width-md {
  min-width: 110px; }

.width-lg {
  min-width: 140px; }

.width-xl {
  min-width: 160px; }

.font-family-secondary {
  font-family: "Roboto", sans-serif; }

.avatar-xs {
  height: 1.5rem;
  width: 1.5rem; }

.avatar-sm {
  height: 2.25rem;
  width: 2.25rem; }

.avatar-md {
  height: 3.5rem;
  width: 3.5rem; }

.avatar-lg {
  height: 4.5rem;
  width: 4.5rem; }

.avatar-xl {
  height: 6rem;
  width: 6rem; }

.avatar-xxl {
  height: 7.5rem;
  width: 7.5rem; }

.avatar-title {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  color: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%; }

.avatar-group {
  padding-left: 12px; }
  .avatar-group .avatar-group-item {
    margin: 0 0 10px -12px;
    display: inline-block;
    border: 2px solid #fff;
    border-radius: 50%; }

.font-weight-medium {
  font-weight: 500; }

.font-weight-semibold {
  font-weight: 600; }

.sp-line-1,
.sp-line-2,
.sp-line-3,
.sp-line-4 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical; }

.sp-line-1 {
  -webkit-line-clamp: 1; }

.sp-line-2 {
  -webkit-line-clamp: 2; }

.sp-line-3 {
  -webkit-line-clamp: 3; }

.sp-line-4 {
  -webkit-line-clamp: 4; }

.pull-in {
  margin-left: -1.5rem;
  margin-right: -1.5rem; }

.social-list-item {
  height: 2rem;
  width: 2rem;
  line-height: calc(2rem - 4px);
  display: block;
  border: 2px solid #adb5bd;
  border-radius: 50%;
  color: #adb5bd; }

.checkbox label {
  display: inline-block;
  padding-left: 8px;
  position: relative;
  font-weight: normal; }
  .checkbox label::before {
    -o-transition: 0.3s ease-in-out;
    -webkit-transition: 0.3s ease-in-out;
    background-color: #fff;
    border-radius: 3px;
    border: 2px solid #98a6ad;
    content: "";
    display: inline-block;
    height: 18px;
    left: 0;
    margin-left: -18px;
    position: absolute;
    transition: 0.3s ease-in-out;
    width: 18px;
    outline: none !important;
    top: 2px; }
  .checkbox label::after {
    color: #6c757d;
    display: inline-block;
    font-size: 11px;
    height: 18px;
    left: 0;
    margin-left: -18px;
    padding-left: 3px;
    padding-top: 2px;
    position: absolute;
    top: 0;
    width: 18px; }

.checkbox input[type="checkbox"] {
  cursor: pointer;
  opacity: 0;
  z-index: 1;
  outline: none !important; }
  .checkbox input[type="checkbox"]:disabled + label {
    opacity: 0.65; }

.checkbox input[type="checkbox"]:focus + label::before {
  outline-offset: -2px;
  outline: none; }

.checkbox input[type="checkbox"]:checked + label::after {
  content: "";
  position: absolute;
  top: 6px;
  left: 7px;
  display: table;
  width: 4px;
  height: 8px;
  border: 2px solid #6c757d;
  border-top-width: 0;
  border-left-width: 0;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg); }

.checkbox input[type="checkbox"]:disabled + label::before {
  background-color: #f7f7f7;
  cursor: not-allowed; }

.checkbox.checkbox-circle label::before {
  border-radius: 50%; }

.checkbox.checkbox-inline {
  margin-top: 0; }

.checkbox.checkbox-single input {
  height: 18px;
  width: 18px;
  position: absolute; }

.checkbox.checkbox-single label {
  height: 18px;
  width: 18px; }
  .checkbox.checkbox-single label:before {
    margin-left: 0; }
  .checkbox.checkbox-single label:after {
    margin-left: 0; }

.checkbox-primary input[type="checkbox"]:checked + label::before {
  background-color: #5d6dc3;
  border-color: #5d6dc3; }

.checkbox-primary input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-secondary input[type="checkbox"]:checked + label::before {
  background-color: #6c757d;
  border-color: #6c757d; }

.checkbox-secondary input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-success input[type="checkbox"]:checked + label::before {
  background-color: #3ec396;
  border-color: #3ec396; }

.checkbox-success input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-info input[type="checkbox"]:checked + label::before {
  background-color: #4fbde9;
  border-color: #4fbde9; }

.checkbox-info input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-warning input[type="checkbox"]:checked + label::before {
  background-color: #f9bc0b;
  border-color: #f9bc0b; }

.checkbox-warning input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-danger input[type="checkbox"]:checked + label::before {
  background-color: #f36270;
  border-color: #f36270; }

.checkbox-danger input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-light input[type="checkbox"]:checked + label::before {
  background-color: #f7f7f7;
  border-color: #f7f7f7; }

.checkbox-light input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-dark input[type="checkbox"]:checked + label::before {
  background-color: #323a46;
  border-color: #323a46; }

.checkbox-dark input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-pink input[type="checkbox"]:checked + label::before {
  background-color: #e061c9;
  border-color: #e061c9; }

.checkbox-pink input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.checkbox-purple input[type="checkbox"]:checked + label::before {
  background-color: #9368f3;
  border-color: #9368f3; }

.checkbox-purple input[type="checkbox"]:checked + label::after {
  border-color: #fff; }

.radio label {
  display: inline-block;
  padding-left: 8px;
  position: relative;
  font-weight: normal; }
  .radio label::before {
    -o-transition: border 0.5s ease-in-out;
    -webkit-transition: border 0.5s ease-in-out;
    background-color: #fff;
    border-radius: 50%;
    border: 2px solid #98a6ad;
    content: "";
    display: inline-block;
    height: 18px;
    left: 0;
    margin-left: -18px;
    position: absolute;
    transition: border 0.5s ease-in-out;
    width: 18px;
    outline: none !important; }
  .radio label::after {
    -moz-transition: -moz-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
    -ms-transform: scale(0, 0);
    -o-transform: scale(0, 0);
    -o-transition: -o-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
    -webkit-transform: scale(0, 0);
    -webkit-transition: -webkit-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
    background-color: #6c757d;
    border-radius: 50%;
    content: " ";
    display: inline-block;
    height: 10px;
    left: 6px;
    margin-left: -20px;
    position: absolute;
    top: 4px;
    transform: scale(0, 0);
    transition: -webkit-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
    transition: transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
    transition: transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33), -webkit-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
    width: 10px; }

.radio input[type="radio"] {
  cursor: pointer;
  opacity: 0;
  z-index: 1;
  outline: none !important; }
  .radio input[type="radio"]:disabled + label {
    opacity: 0.65; }

.radio input[type="radio"]:focus + label::before {
  outline-offset: -2px;
  outline: 5px auto -webkit-focus-ring-color;
  outline: thin dotted; }

.radio input[type="radio"]:checked + label::after {
  -webkit-transform: scale(1, 1);
  transform: scale(1, 1); }

.radio input[type="radio"]:disabled + label::before {
  cursor: not-allowed; }

.radio.radio-inline {
  margin-top: 0; }

.radio.radio-single label {
  height: 17px; }

.radio-primary input[type="radio"] + label::after {
  background-color: #5d6dc3; }

.radio-primary input[type="radio"]:checked + label::before {
  border-color: #5d6dc3; }

.radio-primary input[type="radio"]:checked + label::after {
  background-color: #5d6dc3; }

.radio-secondary input[type="radio"] + label::after {
  background-color: #6c757d; }

.radio-secondary input[type="radio"]:checked + label::before {
  border-color: #6c757d; }

.radio-secondary input[type="radio"]:checked + label::after {
  background-color: #6c757d; }

.radio-success input[type="radio"] + label::after {
  background-color: #3ec396; }

.radio-success input[type="radio"]:checked + label::before {
  border-color: #3ec396; }

.radio-success input[type="radio"]:checked + label::after {
  background-color: #3ec396; }

.radio-info input[type="radio"] + label::after {
  background-color: #4fbde9; }

.radio-info input[type="radio"]:checked + label::before {
  border-color: #4fbde9; }

.radio-info input[type="radio"]:checked + label::after {
  background-color: #4fbde9; }

.radio-warning input[type="radio"] + label::after {
  background-color: #f9bc0b; }

.radio-warning input[type="radio"]:checked + label::before {
  border-color: #f9bc0b; }

.radio-warning input[type="radio"]:checked + label::after {
  background-color: #f9bc0b; }

.radio-danger input[type="radio"] + label::after {
  background-color: #f36270; }

.radio-danger input[type="radio"]:checked + label::before {
  border-color: #f36270; }

.radio-danger input[type="radio"]:checked + label::after {
  background-color: #f36270; }

.radio-light input[type="radio"] + label::after {
  background-color: #f7f7f7; }

.radio-light input[type="radio"]:checked + label::before {
  border-color: #f7f7f7; }

.radio-light input[type="radio"]:checked + label::after {
  background-color: #f7f7f7; }

.radio-dark input[type="radio"] + label::after {
  background-color: #323a46; }

.radio-dark input[type="radio"]:checked + label::before {
  border-color: #323a46; }

.radio-dark input[type="radio"]:checked + label::after {
  background-color: #323a46; }

.radio-pink input[type="radio"] + label::after {
  background-color: #e061c9; }

.radio-pink input[type="radio"]:checked + label::before {
  border-color: #e061c9; }

.radio-pink input[type="radio"]:checked + label::after {
  background-color: #e061c9; }

.radio-purple input[type="radio"] + label::after {
  background-color: #9368f3; }

.radio-purple input[type="radio"]:checked + label::before {
  border-color: #9368f3; }

.radio-purple input[type="radio"]:checked + label::after {
  background-color: #9368f3; }

@media print {
  .left-side-menu,
  .right-bar,
  .page-title-box,
  .navbar-custom,
  .footer {
    display: none; }
  .card-body,
  .content-page,
  .right-bar,
  .content,
  body {
    padding: 0;
    margin: 0; } }

/* =============
   Widgets
============= */
.tilebox-one {
  background: url("../images/bg-1.png");
  background-size: cover;
  border: 4px solid #fff; }
  .tilebox-one i {
    background: #5d6dc3;
    background: -webkit-gradient(linear, left bottom, left top, from(#5d6dc3), to(#3c86d8));
    background: linear-gradient(to top, #5d6dc3, #3c86d8);
    font-size: 24px;
    height: 50px;
    line-height: 50px;
    width: 50px;
    text-align: center;
    color: #fff !important;
    border-radius: 50%;
    -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);
            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12); }

/* Inbox-widget */
.inbox-widget .inbox-item {
  border-bottom: 1px solid #edeff1;
  overflow: hidden;
  padding: 10px 0;
  position: relative; }
  .inbox-widget .inbox-item .inbox-item-img {
    display: block;
    float: left;
    margin-right: 15px;
    width: 40px; }
  .inbox-widget .inbox-item img {
    width: 40px; }
  .inbox-widget .inbox-item .inbox-item-author {
    color: #323a46;
    display: block;
    margin: 0; }
  .inbox-widget .inbox-item .inbox-item-text {
    color: #98a6ad;
    display: block;
    font-size: 14px;
    margin: 0; }
  .inbox-widget .inbox-item .inbox-item-date {
    color: #98a6ad;
    font-size: 11px;
    position: absolute;
    right: 7px;
    top: 7px; }

/* Comment List */
.comment-list .comment-box-item {
  position: relative; }
  .comment-list .comment-box-item .commnet-item-date {
    color: #98a6ad;
    font-size: 11px;
    position: absolute;
    right: 7px;
    top: 2px; }
  .comment-list .comment-box-item .commnet-item-msg {
    color: #323a46;
    display: block;
    margin: 10px 0;
    font-weight: normal;
    font-size: 15px;
    line-height: 24px; }
  .comment-list .comment-box-item .commnet-item-user {
    color: #98a6ad;
    display: block;
    font-size: 14px;
    margin: 0; }

.comment-list a + a {
  margin-top: 15px;
  display: block; }

/* Transaction */
.transaction-list li {
  padding: 7px 0;
  border-bottom: 1px solid #edeff1;
  clear: both;
  position: relative; }

.transaction-list i {
  width: 20px;
  position: absolute;
  top: 10px;
  font-size: 12px; }

.transaction-list .tran-text {
  padding-left: 25px;
  white-space: nowrap;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 150px; }

.transaction-list .tran-price {
  margin-left: 30px; }

/*!
 * Waves v0.7.6
 * http://fian.my.id/Waves 
 * 
 * Copyright 2014-2018 Alfiana E. Sibuea and other contributors 
 * Released under the MIT license 
 * https://github.com/fians/Waves/blob/master/LICENSE */
.waves-effect {
  position: relative;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent; }

.waves-effect .waves-ripple {
  position: absolute;
  border-radius: 50%;
  width: 100px;
  height: 100px;
  margin-top: -50px;
  margin-left: -50px;
  opacity: 0;
  background: rgba(0, 0, 0, 0.2);
  background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
  -webkit-transition: all 0.5s ease-out;
  transition: all 0.5s ease-out;
  -webkit-transition-property: -webkit-transform, opacity;
  -webkit-transition-property: opacity, -webkit-transform;
  transition-property: opacity, -webkit-transform;
  transition-property: transform, opacity;
  transition-property: transform, opacity, -webkit-transform;
  -webkit-transform: scale(0) translate(0, 0);
  transform: scale(0) translate(0, 0);
  pointer-events: none; }

.waves-effect.waves-light .waves-ripple {
  background: rgba(255, 255, 255, 0.4);
  background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%); }

.waves-effect.waves-classic .waves-ripple {
  background: rgba(0, 0, 0, 0.2); }

.waves-effect.waves-classic.waves-light .waves-ripple {
  background: rgba(255, 255, 255, 0.4); }

.waves-notransition {
  -webkit-transition: none !important;
  transition: none !important; }

.waves-button,
.waves-circle {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%); }

.waves-button,
.waves-button:hover,
.waves-button:visited,
.waves-button-input {
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  border: none;
  outline: none;
  color: inherit;
  background-color: rgba(0, 0, 0, 0);
  font-size: 1em;
  line-height: 1em;
  text-align: center;
  text-decoration: none;
  z-index: 1; }

.waves-button {
  padding: 0.85em 1.1em;
  border-radius: 0.2em; }

.waves-button-input {
  margin: 0;
  padding: 0.85em 1.1em; }

.waves-input-wrapper {
  border-radius: 0.2em;
  vertical-align: bottom; }

.waves-input-wrapper.waves-button {
  padding: 0; }

.waves-input-wrapper .waves-button-input {
  position: relative;
  top: 0;
  left: 0;
  z-index: 1; }

.waves-circle {
  text-align: center;
  width: 2.5em;
  height: 2.5em;
  line-height: 2.5em;
  border-radius: 50%; }

.waves-float {
  -webkit-mask-image: none;
  -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);
  box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);
  -webkit-transition: all 300ms;
  transition: all 300ms; }

.waves-float:active {
  -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);
  box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3); }

.waves-block {
  display: block; }

.slimScrollDiv {
  height: auto !important; }

.jq-toast-single {
  padding: 15px;
  font-family: "Nunito Sans", sans-serif;
  background-color: #5d6dc3;
  font-size: 13px;
  line-height: 22px; }
  .jq-toast-single h2 {
    font-family: "Nunito Sans", sans-serif; }
  .jq-toast-single a {
    font-size: 0.875rem; }
    .jq-toast-single a:hover {
      color: #fff; }

.jq-has-icon {
  padding: 10px 10px 10px 50px; }

.close-jq-toast-single {
  position: absolute;
  top: -12px;
  right: -12px;
  font-size: 20px;
  cursor: pointer;
  height: 32px;
  width: 32px;
  background-color: #323a46;
  border-radius: 50%;
  text-align: center;
  line-height: 32px; }

.jq-toast-loader {
  height: 3px;
  top: 0;
  border-radius: 0; }

.jq-icon-primary {
  background-color: #5d6dc3;
  color: #fff;
  border-color: #5d6dc3; }

.jq-icon-secondary {
  background-color: #6c757d;
  color: #fff;
  border-color: #6c757d; }

.jq-icon-success {
  background-color: #3ec396;
  color: #fff;
  border-color: #3ec396; }

.jq-icon-info {
  background-color: #4fbde9;
  color: #fff;
  border-color: #4fbde9; }

.jq-icon-warning {
  background-color: #f9bc0b;
  color: #fff;
  border-color: #f9bc0b; }

.jq-icon-danger {
  background-color: #f36270;
  color: #fff;
  border-color: #f36270; }

.jq-icon-light {
  background-color: #f7f7f7;
  color: #fff;
  border-color: #f7f7f7; }

.jq-icon-dark {
  background-color: #323a46;
  color: #fff;
  border-color: #323a46; }

.jq-icon-pink {
  background-color: #e061c9;
  color: #fff;
  border-color: #e061c9; }

.jq-icon-purple {
  background-color: #9368f3;
  color: #fff;
  border-color: #9368f3; }

.jq-icon-error {
  background-color: #f36270;
  color: #fff;
  border-color: #f36270; }

.swal2-modal {
  font-family: "Nunito Sans", sans-serif;
  -webkit-box-shadow: 0 10px 33px rgba(0, 0, 0, 0.1);
          box-shadow: 0 10px 33px rgba(0, 0, 0, 0.1);
  border: 2px solid #5d6dc3; }
  .swal2-modal .swal2-title {
    font-size: 24px; }
  .swal2-modal .swal2-content {
    font-size: 16px; }
  .swal2-modal .swal2-spacer {
    margin: 10px 0; }
  .swal2-modal .swal2-file, .swal2-modal .swal2-input, .swal2-modal .swal2-textarea {
    border: 2px solid #dee2e6;
    font-size: 16px;
    -webkit-box-shadow: none;
            box-shadow: none; }
  .swal2-modal .swal2-confirm.btn-confirm {
    background-color: #5d6dc3 !important;
    font-size: 0.875rem; }
  .swal2-modal .swal2-cancel.btn-cancel {
    background-color: #f36270 !important;
    font-size: 0.875rem; }
  .swal2-modal .swal2-styled:focus {
    -webkit-box-shadow: none !important;
            box-shadow: none !important; }

.swal2-icon.swal2-question {
  color: #5d6dc3;
  border-color: #5d6dc3; }

.swal2-icon.swal2-success {
  border-color: #3ec396; }
  .swal2-icon.swal2-success .line, .swal2-icon.swal2-success [class^=swal2-success-line][class$=long],
  .swal2-icon.swal2-success [class^=swal2-success-line] {
    background-color: #3ec396; }
  .swal2-icon.swal2-success .placeholder, .swal2-icon.swal2-success .swal2-success-ring {
    border-color: #3ec396; }

.swal2-icon.swal2-warning {
  color: #f9bc0b;
  border-color: #f9bc0b; }

.swal2-icon.swal2-error {
  border-color: #f36270; }
  .swal2-icon.swal2-error .line {
    background-color: #f36270; }

.swal2-modal .swal2-file:focus, .swal2-modal .swal2-input:focus, .swal2-modal .swal2-textarea:focus {
  outline: 0;
  border: 2px solid #5d6dc3; }

.swal2-container.swal2-shown {
  background-color: rgba(255, 255, 255, 0.9); }

.flotTip {
  padding: 8px 12px;
  background-color: #fff;
  z-index: 99;
  color: #323a46;
  -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);
          box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);
  opacity: 1;
  border-radius: 3px; }

.legend tr {
  height: 30px;
  font-family: "Roboto", sans-serif; }

.legendLabel {
  padding-left: 5px !important;
  line-height: 10px;
  padding-right: 20px;
  font-size: 13px;
  font-weight: 500;
  color: #98a6ad;
  text-transform: uppercase; }

.legendColorBox div div {
  border-radius: 50%; }

@media (max-width: 767.98px) {
  .legendLabel {
    display: none; } }

.morris-chart text {
  font-family: "Roboto", sans-serif !important; }

.morris-hover {
  position: absolute;
  z-index: 10; }
  .morris-hover.morris-default-style {
    font-size: 12px;
    text-align: center;
    border-radius: 5px;
    padding: 10px 12px;
    background: #fff;
    color: #323a46;
    -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);
            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);
    font-family: "Nunito Sans", sans-serif; }
    .morris-hover.morris-default-style .morris-hover-row-label {
      font-weight: bold;
      margin: 0.25em 0;
      font-family: "Roboto", sans-serif; }
    .morris-hover.morris-default-style .morris-hover-point {
      white-space: nowrap;
      margin: 0.1em 0;
      color: #fff; }

.chartjs-chart {
  margin: auto;
  position: relative;
  width: 100%; }

.jqstooltip {
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  width: auto !important;
  height: auto !important;
  background-color: #fff !important;
  -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);
          box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);
  padding: 5px 10px !important;
  border-radius: 3px;
  border-color: #fff !important; }

.jqsfield {
  color: #323a46 !important;
  font-size: 12px !important;
  line-height: 18px !important;
  font-family: "Nunito Sans", sans-serif !important;
  font-weight: 700 !important; }

/* =============
   Maps
============= */
.gmaps,
.gmaps-panaroma {
  height: 300px;
  background: #eeeeee;
  border-radius: 3px; }

.gmaps-overlay {
  display: block;
  text-align: center;
  color: #fff;
  font-size: 16px;
  line-height: 40px;
  background: #5d6dc3;
  border-radius: 4px;
  padding: 10px 20px; }

.gmaps-overlay_arrow {
  left: 50%;
  margin-left: -16px;
  width: 0;
  height: 0;
  position: absolute; }

.gmaps-overlay_arrow.above {
  bottom: -15px;
  border-left: 16px solid transparent;
  border-right: 16px solid transparent;
  border-top: 16px solid #5d6dc3; }

.gmaps-overlay_arrow.below {
  top: -15px;
  border-left: 16px solid transparent;
  border-right: 16px solid transparent;
  border-bottom: 16px solid #5d6dc3; }

.gmaps-full {
  z-index: 99;
  margin: 0 -20px -10px -20px; }
  .gmaps-full .gmaps-full1 {
    height: 80vh;
    width: 100%; }

.jvectormap-label {
  border: none;
  background: #323a46;
  color: #fff;
  font-family: "Roboto", sans-serif;
  font-size: 0.875rem;
  padding: 5px 8px; }

/* Mapael Map */
.mapael .map {
  position: relative; }
  .mapael .map .zoomIn {
    top: 25px; }
  .mapael .map .zoomOut {
    top: 50px; }

.mapael .mapTooltip {
  position: absolute;
  background-color: #5d6dc3;
  opacity: 0.95;
  border-radius: 3px;
  padding: 2px 10px;
  z-index: 1000;
  max-width: 200px;
  display: none;
  color: #fff;
  font-family: "Roboto", sans-serif; }

.mapael .zoomIn,
.mapael .zoomOut,
.mapael .zoomReset {
  display: inline-block;
  text-align: center;
  vertical-align: middle;
  border-radius: 2px;
  font-weight: 500;
  cursor: pointer;
  background-color: #5d6dc3;
  text-decoration: none;
  color: #fff;
  font-size: 14px;
  position: absolute;
  top: 0;
  left: 10px;
  width: 24px;
  height: 24px;
  line-height: 24px; }

.mapael .plotLegend text {
  font-family: "Nunito Sans", sans-serif !important; }

.calendar {
  float: left;
  margin-bottom: 0; }

.fc-view {
  margin-top: 30px; }

.none-border .modal-footer {
  border-top: none; }

.fc-toolbar {
  margin: 15px 0 5px 0; }
  .fc-toolbar h2 {
    font-size: 1.25rem;
    line-height: 1.875rem;
    text-transform: uppercase; }

.fc-day-grid-event .fc-time {
  font-weight: 700; }

.fc-day {
  background: #fff; }

.fc-toolbar .fc-state-active,
.fc-toolbar .ui-state-active,
.fc-toolbar button:focus,
.fc-toolbar button:hover,
.fc-toolbar .ui-state-hover {
  z-index: 0; }

.fc th.fc-widget-header {
  background: #f1f5f7;
  font-size: 13px;
  line-height: 20px;
  padding: 10px 0;
  text-transform: uppercase; }

.fc-unthemed th,
.fc-unthemed td,
.fc-unthemed thead,
.fc-unthemed tbody,
.fc-unthemed .fc-divider,
.fc-unthemed .fc-row,
.fc-unthemed .fc-popover {
  border-color: #dee2e6; }

.fc-ltr .fc-basic-view .fc-day-top .fc-day-number {
  float: right;
  margin: 5px;
  font-family: "Roboto", sans-serif;
  font-size: 12px; }

.fc-button {
  background: #f1f5f7;
  border: none;
  color: #6c757d;
  text-transform: capitalize;
  -webkit-box-shadow: none;
          box-shadow: none;
  border-radius: 3px;
  margin: 0 3px;
  padding: 6px 12px;
  height: auto; }

.fc-text-arrow {
  font-family: inherit;
  font-size: 1rem; }

.fc-state-hover {
  background: #f1f5f7; }

.fc-state-highlight {
  background: #dee2e6; }

.fc-state-down,
.fc-state-active,
.fc-state-disabled {
  background: -webkit-gradient(linear, left bottom, left top, from(#5d6dc3), to(#3c86d8));
  background: linear-gradient(to top, #5d6dc3, #3c86d8);
  color: #fff;
  text-shadow: none; }

.fc-cell-overlay {
  background: #dee2e6; }

.fc-unthemed .fc-today {
  background: #fff; }

.fc-event {
  border-radius: 2px;
  border: none;
  cursor: move;
  font-size: 0.8125rem;
  margin: 5px 7px;
  padding: 5px 5px;
  text-align: center; }

.external-event {
  cursor: move;
  margin: 10px 0;
  padding: 8px 10px;
  color: #fff; }

.fc-basic-view td.fc-week-number span {
  padding-right: 8px; }

.fc-basic-view td.fc-day-number {
  padding-right: 8px; }

.fc-basic-view .fc-content {
  color: #fff; }

.fc-time-grid-event .fc-content {
  color: #fff; }

@media (max-width: 767.98px) {
  .fc-toolbar .fc-left, .fc-toolbar .fc-right, .fc-toolbar .fc-center {
    float: none;
    display: block;
    clear: both;
    margin: 10px 0; }
  .fc .fc-toolbar > * > * {
    float: none; }
  .fc-today-button {
    display: none; } }

/* =============
   Summernote
============= */
@font-face {
  font-family: "summernote";
  font-style: normal;
  font-weight: normal;
  src: url("../fonts/summernote.eot");
  src: url("../fonts/summernote.eot?#iefix") format("embedded-opentype"), url("../fonts/summernote.woff?") format("woff"), url("../fonts/summernote.ttf?") format("truetype"); }

.note-editor.note-frame {
  border: 1px solid #ced4da;
  -webkit-box-shadow: none;
          box-shadow: none;
  margin: 0; }
  .note-editor.note-frame .note-statusbar {
    background-color: #fcfcfc;
    border-top: 1px solid #f7f7f7; }
  .note-editor.note-frame .note-editable {
    border: none; }

.note-status-output {
  display: none; }

.note-editable {
  border: none;
  border-radius: 0.2rem;
  padding: 0.45rem 0.9rem; }
  .note-editable p:last-of-type {
    margin-bottom: 0; }

.note-popover .popover-content .note-color .dropdown-menu,
.card-header.note-toolbar .note-color .dropdown-menu {
  min-width: 344px; }

.note-toolbar {
  z-index: 1; }

.select2-container .select2-selection--single {
  border: 1px solid #ced4da;
  height: 38px;
  outline: none; }
  .select2-container .select2-selection--single .select2-selection__rendered {
    line-height: 36px;
    padding-left: 12px; }
  .select2-container .select2-selection--single .select2-selection__arrow {
    height: 34px;
    width: 34px;
    right: 3px; }
    .select2-container .select2-selection--single .select2-selection__arrow b {
      border-color: #d1d1d1 transparent transparent transparent;
      border-width: 6px 6px 0 6px; }

.select2-container--open .select2-selection--single .select2-selection__arrow b {
  border-color: transparent transparent #d1d1d1 transparent !important;
  border-width: 0 6px 6px 6px !important; }

.select2-results__option {
  padding: 6px 12px; }

.select2-dropdown {
  border: 1px solid #eaeaea;
  -webkit-box-shadow: 0px 0px 35px 0px rgba(154, 161, 171, 0.15);
          box-shadow: 0px 0px 35px 0px rgba(154, 161, 171, 0.15); }

.select2-container--default .select2-search--dropdown {
  padding: 10px;
  background-color: white; }
  .select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #eaeaea;
    outline: none; }

.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background: -webkit-gradient(linear, left bottom, left top, from(#5d6dc3), to(#3c86d8));
  background: linear-gradient(to top, #5d6dc3, #3c86d8); }

.select2-container--default .select2-results__option[aria-selected=true] {
  background-color: #f7f7f7;
  color: #323a46; }
  .select2-container--default .select2-results__option[aria-selected=true]:hover {
    background: -webkit-gradient(linear, left bottom, left top, from(#5d6dc3), to(#3c86d8));
    background: linear-gradient(to top, #5d6dc3, #3c86d8);
    color: #fff; }

.select2-container .select2-selection--multiple {
  min-height: 38px;
  border: 1px solid #ced4da !important; }
  .select2-container .select2-selection--multiple .select2-selection__rendered {
    padding: 1px 10px; }
  .select2-container .select2-selection--multiple .select2-search__field {
    border: 0; }
  .select2-container .select2-selection--multiple .select2-selection__choice {
    background: -webkit-gradient(linear, left bottom, left top, from(#5d6dc3), to(#3c86d8));
    background: linear-gradient(to top, #5d6dc3, #3c86d8);
    border: none;
    color: #fff;
    border-radius: 3px;
    padding: 0 7px;
    margin-top: 7px; }
  .select2-container .select2-selection--multiple .select2-selection__choice__remove {
    color: #fff;
    margin-right: 5px; }
    .select2-container .select2-selection--multiple .select2-selection__choice__remove:hover {
      color: #fff; }

.autocomplete-suggestions {
  border: 1px solid #f9f9f9;
  background: #fff;
  cursor: default;
  overflow: auto;
  max-height: 200px !important;
  -webkit-box-shadow: 0 2px 2px rgba(0, 0, 0, 0.15);
          box-shadow: 0 2px 2px rgba(0, 0, 0, 0.15); }
  .autocomplete-suggestions strong {
    font-weight: bold;
    color: #323a46; }

.autocomplete-suggestion {
  padding: 5px 10px;
  white-space: nowrap;
  overflow: hidden; }

.autocomplete-no-suggestion {
  padding: 5px; }

.autocomplete-selected {
  background: #f7f7f7;
  cursor: pointer; }

.autocomplete-group {
  padding: 5px;
  font-weight: 500;
  font-family: "Roboto", sans-serif; }
  .autocomplete-group strong {
    font-weight: bold;
    font-size: 16px;
    color: #323a46;
    display: block; }

/* Bootstrap tagsinput */
.bootstrap-tagsinput {
  -webkit-box-shadow: none;
          box-shadow: none;
  padding: 4px 7px 4px;
  border: 1px solid #efefef;
  width: 100%; }
  .bootstrap-tagsinput .label-info {
    background: -webkit-gradient(linear, left bottom, left top, from(#5d6dc3), to(#3c86d8));
    background: linear-gradient(to top, #5d6dc3, #3c86d8);
    display: inline-block;
    font-size: 11px;
    margin: 3px 1px;
    padding: 0 5px;
    border-radius: 3px; }

.bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
  width: 100% !important; }

.bootstrap-select .dropdown-menu .dropdown-menu li a {
  display: block;
  width: 100%;
  clear: both;
  font-weight: 400;
  color: #323a46;
  text-align: inherit;
  white-space: nowrap;
  background: 0 0;
  border: 0; }
  .bootstrap-select .dropdown-menu .dropdown-menu li a:hover {
    background: -webkit-gradient(linear, left bottom, left top, from(#5d6dc3), to(#3c86d8));
    background: linear-gradient(to top, #5d6dc3, #3c86d8);
    color: #fff; }

.bootstrap-select .dropdown-toggle:after {
  content: "\F140";
  display: inline-block;
  font-family: "Material Design Icons"; }

.bootstrap-select .dropdown-toggle:focus {
  outline: none !important;
  outline-offset: 0; }

.bootstrap-select a {
  outline: none !important; }

.bootstrap-select .inner {
  overflow-y: inherit !important; }

.bootstrap-select > .btn-pink.bs-placeholder {
  color: #fff !important; }

.parsley-errors-list {
  margin: 0;
  padding: 0; }
  .parsley-errors-list > li {
    list-style: none;
    color: #f36270;
    margin-top: 5px;
    padding-left: 20px;
    position: relative; }
    .parsley-errors-list > li:before {
      content: "\F159";
      font-family: "Material Design Icons";
      position: absolute;
      left: 2px;
      top: -1px; }

.parsley-error {
  border-color: #f36270; }

.parsley-success {
  border-color: #3ec396; }

.bootstrap-timepicker-widget table td input {
  width: 35px;
  border: 0px; }

.bootstrap-timepicker-widget table td a:hover {
  background-color: transparent;
  border: 1px solid transparent; }

/* Daterange Picker */
.daterangepicker td.active, .daterangepicker td.active:hover {
  background: #5d6dc3;
  background: -webkit-gradient(linear, left bottom, left top, from(#5d6dc3), to(#3c86d8));
  background: linear-gradient(to top, #5d6dc3, #3c86d8); }

.daterangepicker .input-mini.active {
  border: 1px solid rgba(50, 58, 70, 0.3); }

.daterangepicker .ranges li {
  border-radius: 2px;
  color: #323a46;
  font-weight: 600;
  font-size: 12px; }

.daterangepicker select.hourselect, .daterangepicker select.minuteselect,
.daterangepicker select.secondselect, .daterangepicker select.ampmselect {
  border: 1px solid rgba(50, 58, 70, 0.3);
  padding: 2px;
  width: 60px; }

.daterangepicker .ranges li.active, .daterangepicker .ranges li:hover {
  background: #5d6dc3;
  background: -webkit-gradient(linear, left bottom, left top, from(#5d6dc3), to(#3c86d8));
  background: linear-gradient(to top, #5d6dc3, #3c86d8);
  border: 1px solid #5d6dc3;
  color: #fff; }

.daterangepicker select.monthselect, .daterangepicker select.yearselect {
  border-color: #98a6ad; }

/* Clock picker */
.clockpicker-canvas line {
  stroke: #959fd8; }

.clockpicker-canvas-bearing, .clockpicker-canvas-fg, .clockpicker-canvas-bg {
  fill: #959fd8; }

.clockpicker-popover .btn-default {
  background-color: #5d6dc3;
  color: #fff; }

.wizard > .steps {
  position: relative;
  display: block;
  width: 100%; }
  .wizard > .steps > ul > li {
    width: 25%; }
  .wizard > .steps a {
    font-size: 16px;
    margin: 0 0.5em 0.5em; }
  .wizard > .steps .number {
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.3);
    display: inline-block;
    line-height: 30px;
    margin-right: 10px;
    width: 30px;
    text-align: center;
    font-size: 1.429em; }
  .wizard > .steps a, .wizard > .steps a:hover, .wizard > .steps a:active {
    display: block;
    width: auto;
    padding: 1em 1em;
    text-decoration: none;
    border-radius: 2px; }
  .wizard > .steps .disabled a {
    border: 1px solid #ededed;
    color: #323a46;
    cursor: default; }
  .wizard > .steps .current a, .wizard > .steps .current a:hover, .wizard > .steps .current a:active {
    background: -webkit-gradient(linear, left bottom, left top, from(#5d6dc3), to(#3c86d8));
    background: linear-gradient(to top, #5d6dc3, #3c86d8);
    color: #fff;
    cursor: default; }
    .wizard > .steps .current a .number, .wizard > .steps .current a:hover .number, .wizard > .steps .current a:active .number {
      color: #fff; }
  .wizard > .steps .done a, .wizard > .steps .done a:hover, .wizard > .steps .done a:active {
    background: #a7b0df;
    color: #fff; }
  .wizard > .steps .error a, .wizard > .steps .error a:hover, .wizard > .steps .error a:active {
    background: snow;
    color: #fff;
    border-color: #fde7e9; }

.wizard > .steps > ul > li, .wizard > .actions > ul > li {
  float: left;
  position: relative; }

.wizard > .content {
  display: block;
  min-height: 240px;
  overflow: hidden;
  position: relative;
  width: auto;
  padding: 20px; }
  .wizard > .content > .body {
    padding: 0;
    position: relative;
    width: 95%; }
    .wizard > .content > .body ul {
      list-style: disc !important; }
      .wizard > .content > .body ul > li {
        display: block;
        line-height: 30px; }
    .wizard > .content > .body > iframe {
      border: 0 none;
      width: 100%;
      height: 100%; }
    .wizard > .content > .body input {
      display: block;
      border-color: #dee2e6; }
      .wizard > .content > .body input:focus {
        border-color: #dee2e6; }
      .wizard > .content > .body input[type="checkbox"] {
        display: inline-block; }
      .wizard > .content > .body input.error {
        background: white;
        border: 1px solid snow;
        color: #f36270; }
    .wizard > .content > .body label {
      display: inline-block;
      margin-bottom: 0.5em;
      margin-top: 10px; }
      .wizard > .content > .body label.error {
        color: #f36270;
        font-size: 12px; }

.wizard > .actions {
  position: relative;
  display: block;
  text-align: right;
  width: 100%;
  margin-top: 15px; }
  .wizard > .actions > ul {
    display: inline-block;
    text-align: right; }
    .wizard > .actions > ul > li {
      margin: 0 0.5em; }
  .wizard > .actions a, .wizard > .actions a:hover, .wizard > .actions a:active {
    background: -webkit-gradient(linear, left bottom, left top, from(#5d6dc3), to(#3c86d8));
    background: linear-gradient(to top, #5d6dc3, #3c86d8);
    color: #fff;
    display: block;
    padding: 0.5em 1em;
    text-decoration: none;
    border-radius: 2px; }
  .wizard > .actions .disabled a, .wizard > .actions .disabled a:hover, .wizard > .actions .disabled a:active {
    background: #fff;
    color: #323a46;
    cursor: default;
    border: 1px solid #f7f7f7; }

.wizard.vertical > .steps {
  display: inline;
  float: left;
  width: 30%; }
  .wizard.vertical > .steps > ul > li {
    float: none;
    width: 100%; }

.wizard.vertical > .content {
  width: 65%;
  margin: 0 2.5% 0.5em;
  display: inline;
  float: left; }

.wizard.vertical > .actions {
  display: inline;
  float: right;
  width: 95%;
  margin: 0 2.5%;
  margin-top: 15px !important; }
  .wizard.vertical > .actions > ul > li {
    margin: 0 0 0 1em; }

/*
  Common 
*/
.wizard, .tabcontrol {
  display: block;
  width: 100%;
  overflow: hidden;
  /* Accessibility */ }
  .wizard a, .tabcontrol a {
    outline: 0; }
  .wizard ul, .tabcontrol ul {
    list-style: none !important;
    padding: 0;
    margin: 0; }
    .wizard ul > li, .tabcontrol ul > li {
      display: block;
      padding: 0; }
  .wizard > .steps .current-info, .tabcontrol > .steps .current-info {
    position: absolute;
    left: -999em; }
  .wizard > .content > .title, .tabcontrol > .content > .title {
    position: absolute;
    left: -999em; }

@media (max-width: 767.98px) {
  .wizard > .steps > ul > li, .wizard.vertical > .steps, .wizard.vertical > .content {
    width: 100%; } }

.editable-clear-x {
  background: url("../images/clear.png") center center no-repeat; }

.editableform-loading {
  background: url("../images/loading.gif") center center no-repeat; }

.editable-checklist label {
  display: block; }

.dropzone {
  border: 2px dashed rgba(50, 58, 70, 0.3);
  background: #fff;
  border-radius: 6px; }

.dataTables_wrapper.container-fluid {
  padding: 0; }

table.dataTable {
  border-collapse: collapse !important;
  margin-bottom: 15px !important; }
  table.dataTable tbody > tr.selected, table.dataTable tbody > tr > .selected {
    background-color: #5d6dc3; }
    table.dataTable tbody > tr.selected td, table.dataTable tbody > tr > .selected td {
      border-color: #5d6dc3; }
  table.dataTable tbody td:focus {
    outline: none !important; }
  table.dataTable tbody th.focus, table.dataTable tbody td.focus {
    outline: 2px solid #5d6dc3 !important;
    outline-offset: -1px;
    color: #5d6dc3;
    background-color: rgba(93, 109, 195, 0.15); }

.dataTables_info {
  font-weight: 600; }

table.dataTable.dtr-inline.collapsed > tbody > tr[role=row] > td:first-child:before, table.dataTable.dtr-inline.collapsed > tbody > tr[role=row] > th:first-child:before {
  -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);
          box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.12);
  background-color: #5d6dc3;
  top: 0.85rem; }

table.dataTable.dtr-inline.collapsed > tbody > tr.parent > td:first-child:before, table.dataTable.dtr-inline.collapsed > tbody > tr.parent > th:first-child:before {
  background-color: #f36270;
  top: 0.85rem; }

div.dt-button-info {
  background-color: #5d6dc3;
  border: none;
  color: #fff;
  -webkit-box-shadow: none;
          box-shadow: none;
  border-radius: 3px;
  text-align: center;
  z-index: 21; }
  div.dt-button-info h2 {
    border-bottom: none;
    background-color: rgba(255, 255, 255, 0.2);
    color: #fff; }

@media (max-width: 767.98px) {
  li.paginate_button.previous, li.paginate_button.next {
    display: inline-block; }
  li.paginate_button {
    display: none; }
  .dataTables_paginate ul {
    text-align: center;
    display: block;
    margin: 1.5rem 0 0 !important; }
  div.dt-buttons {
    display: inline-table;
    margin-bottom: 1.5rem; } }

.activate-select .sorting_1 {
  background-color: #f1f5f7; }

.table-rep-plugin .dropdown-menu li.checkbox-row {
  padding: 7px 15px; }

.table-rep-plugin .table-responsive {
  border: none; }

.table-rep-plugin tbody th {
  font-size: 14px;
  font-weight: normal; }

.table-rep-plugin .checkbox-row {
  padding-left: 40px; }
  .table-rep-plugin .checkbox-row label {
    display: inline-block;
    padding-left: 5px;
    position: relative;
    margin-bottom: 0; }
    .table-rep-plugin .checkbox-row label::before {
      -o-transition: 0.3s ease-in-out;
      -webkit-transition: 0.3s ease-in-out;
      background-color: #fff;
      border-radius: 3px;
      border: 1px solid #98a6ad;
      content: "";
      display: inline-block;
      height: 17px;
      left: 0;
      margin-left: -20px;
      position: absolute;
      transition: 0.3s ease-in-out;
      width: 17px;
      outline: none; }
    .table-rep-plugin .checkbox-row label::after {
      color: #dee2e6;
      display: inline-block;
      font-size: 11px;
      height: 16px;
      left: 0;
      margin-left: -20px;
      padding-left: 3px;
      padding-top: 1px;
      position: absolute;
      top: -1px;
      width: 16px; }
  .table-rep-plugin .checkbox-row input[type="checkbox"] {
    cursor: pointer;
    opacity: 0;
    z-index: 1;
    outline: none; }
    .table-rep-plugin .checkbox-row input[type="checkbox"]:disabled + label {
      opacity: 0.65; }
  .table-rep-plugin .checkbox-row input[type="checkbox"]:focus + label::before {
    outline-offset: -2px;
    outline: none; }
  .table-rep-plugin .checkbox-row input[type="checkbox"]:checked + label::after {
    content: "\f00c";
    font-family: 'Font Awesome 5 Free';
    font-weight: 900; }
  .table-rep-plugin .checkbox-row input[type="checkbox"]:disabled + label::before {
    background-color: #f7f7f7;
    cursor: not-allowed; }
  .table-rep-plugin .checkbox-row input[type="checkbox"]:checked + label::before {
    background-color: #fff;
    border-color: #5d6dc3; }
  .table-rep-plugin .checkbox-row input[type="checkbox"]:checked + label::after {
    color: #5d6dc3; }

.table-rep-plugin table.focus-on tbody tr.focused th, .table-rep-plugin table.focus-on tbody tr.focused td,
.table-rep-plugin .sticky-table-header {
  background: #5d6dc3;
  background: -webkit-gradient(linear, left bottom, left top, from(#5d6dc3), to(#3c86d8));
  background: linear-gradient(to top, #5d6dc3, #3c86d8);
  color: #fff;
  border-color: #5d6dc3; }
  .table-rep-plugin table.focus-on tbody tr.focused th table, .table-rep-plugin table.focus-on tbody tr.focused td table,
  .table-rep-plugin .sticky-table-header table {
    color: #fff; }

.table-rep-plugin .fixed-solution .sticky-table-header {
  top: 70px !important; }

.table-rep-plugin .btn-default {
  background-color: #fff;
  border: 1px solid rgba(50, 58, 70, 0.3);
  color: #323a46; }

.table-rep-plugin .btn-primary {
  background: -webkit-gradient(linear, left bottom, left top, from(#5d6dc3), to(#3c86d8));
  background: linear-gradient(to top, #5d6dc3, #3c86d8);
  border-color: #5d6dc3;
  color: #fff; }

.table-rep-plugin .btn-group.pull-right {
  float: right; }
  .table-rep-plugin .btn-group.pull-right .dropdown-menu {
    left: auto;
    right: 0; }

.table-rep-plugin .btn-toolbar {
  display: block; }

.tablesaw thead {
  background: #f1f5f7;
  background-image: none;
  border: none; }
  .tablesaw thead th {
    text-shadow: none; }
  .tablesaw thead tr:first-child th {
    border: none;
    font-weight: 500;
    font-family: "Roboto", sans-serif; }

.tablesaw td {
  border-top: 1px solid #f1f5f7 !important; }

.tablesaw td,
.tablesaw tbody th {
  font-size: inherit;
  line-height: inherit;
  padding: 10px !important; }

.tablesaw-stack tbody tr,
.tablesaw tbody tr {
  border-bottom: none; }

.tablesaw-bar .btn-select.btn-small:after,
.tablesaw-bar .btn-select.btn-micro:after {
  font-size: 8px;
  padding-right: 10px; }

.tablesaw-swipe .tablesaw-cell-persist {
  -webkit-box-shadow: none;
          box-shadow: none;
  border-color: #f1f5f7; }

.tablesaw-enhanced .tablesaw-bar .btn {
  text-shadow: none;
  background-image: none;
  text-transform: none;
  border: 1px solid #dee2e6;
  padding: 3px 10px;
  color: #323a46; }
  .tablesaw-enhanced .tablesaw-bar .btn:after {
    display: none; }

.tablesaw-enhanced .tablesaw-bar .btn.btn-select:hover {
  background: #fff; }

.tablesaw-enhanced .tablesaw-bar .btn:hover,
.tablesaw-enhanced .tablesaw-bar .btn:focus,
.tablesaw-enhanced .tablesaw-bar .btn:active {
  color: #5d6dc3 !important;
  background-color: #f1f5f7;
  outline: none !important;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
  background-image: none; }

.tablesaw-columntoggle-popup .btn-group {
  display: block; }

.tablesaw-swipe .tablesaw-swipe-cellpersist {
  border-right: 2px solid #f1f5f7; }

.tablesaw-sortable-btn {
  cursor: pointer; }

.tablesaw-swipe-cellpersist {
  width: auto !important; }

.ql-container {
  font-family: "Nunito Sans", sans-serif; }

.ql-bubble {
  border: 1px solid #ced4da;
  border-radius: 0.2rem; }

.ql-toolbar {
  font-family: "Nunito Sans", sans-serif !important; }
  .ql-toolbar span {
    outline: none !important; }

.button-list {
  margin-left: -8px;
  margin-bottom: -12px; }
  .button-list .btn {
    margin-bottom: 12px;
    margin-left: 8px; }

.icons-list-demo div {
  cursor: pointer;
  line-height: 45px;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: block;
  overflow: hidden; }
  .icons-list-demo div p {
    margin-bottom: 0;
    line-height: inherit; }

.icons-list-demo i {
  text-align: center;
  vertical-align: middle;
  font-size: 22px;
  width: 50px;
  height: 50px;
  line-height: 50px;
  margin-right: 12px;
  color: rgba(50, 58, 70, 0.7);
  border-radius: 3px;
  display: inline-block;
  -webkit-transition: all 0.2s;
  transition: all 0.2s; }

.icons-list-demo .col-lg-4 {
  background-clip: padding-box;
  margin-top: 10px; }
  .icons-list-demo .col-lg-4:hover i {
    color: #fff;
    background: -webkit-gradient(linear, left bottom, left top, from(#5d6dc3), to(#3c86d8));
    background: linear-gradient(to top, #5d6dc3, #3c86d8); }

.grid-structure .grid-container {
  background-color: #f1f5f7;
  margin-top: 10px;
  font-size: .8rem;
  font-weight: 500;
  padding: 10px 20px; }

.home-btn {
  position: absolute;
  top: 15px;
  right: 25px; }

.checkmark {
  width: 100px;
  margin: 0 auto;
  padding: 20px 0; }

.path {
  stroke-dasharray: 1000;
  stroke-dashoffset: 0;
  animation: dash 2s ease-in-out;
  -webkit-animation: dash 2s ease-in-out; }

.spin {
  animation: spin 2s;
  -webkit-animation: spin 2s;
  transform-origin: 50% 50%;
  -webkit-transform-origin: 50% 50%; }

@keyframes dash {
  0% {
    stroke-dashoffset: 1000; }
  100% {
    stroke-dashoffset: 0; } }

@keyframes spin {
  0% {
    -webkit-transform: rotate(0deg); }
  100% {
    -webkit-transform: rotate(360deg); } }

/* =============
   Email
============= */
.inbox-leftbar {
  width: 240px;
  float: left;
  padding: 0 20px 20px 10px; }

.inbox-rightbar {
  margin-left: 250px; }

.message-list {
  display: block;
  padding-left: 0; }
  .message-list li {
    position: relative;
    display: block;
    height: 50px;
    line-height: 50px;
    cursor: default;
    -webkit-transition-duration: .3s;
            transition-duration: .3s; }
    .message-list li a {
      color: #6c757d; }
    .message-list li:hover {
      background: rgba(152, 166, 173, 0.15);
      -webkit-transition-duration: .05s;
              transition-duration: .05s; }
    .message-list li .col-mail {
      float: left;
      position: relative; }
    .message-list li .col-mail-1 {
      width: 320px; }
      .message-list li .col-mail-1 .star-toggle,
      .message-list li .col-mail-1 .checkbox-wrapper-mail,
      .message-list li .col-mail-1 .dot {
        display: block;
        float: left; }
      .message-list li .col-mail-1 .dot {
        border: 4px solid transparent;
        border-radius: 100px;
        margin: 22px 26px 0;
        height: 0;
        width: 0;
        line-height: 0;
        font-size: 0; }
      .message-list li .col-mail-1 .checkbox-wrapper-mail {
        margin: 15px 10px 0 20px; }
      .message-list li .col-mail-1 .star-toggle {
        margin-top: 18px;
        margin-left: 5px; }
      .message-list li .col-mail-1 .title {
        position: absolute;
        top: 15px;
        left: 110px;
        right: 0;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap; }
    .message-list li .col-mail-2 {
      position: absolute;
      top: 0;
      left: 320px;
      right: 0;
      bottom: 0; }
      .message-list li .col-mail-2 .subject,
      .message-list li .col-mail-2 .date {
        position: absolute;
        top: 0; }
      .message-list li .col-mail-2 .subject {
        left: 0;
        right: 200px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap; }
      .message-list li .col-mail-2 .date {
        right: 0;
        width: 170px;
        padding-left: 80px; }
  .message-list li.active, .message-list li.selected {
    background: rgba(152, 166, 173, 0.15);
    -webkit-transition-duration: .05s;
            transition-duration: .05s; }
  .message-list li.active,
  .message-list li.active:hover {
    -webkit-box-shadow: inset 3px 0 0 #5d6dc3;
            box-shadow: inset 3px 0 0 #5d6dc3; }
  .message-list li.unread a {
    font-weight: 600;
    color: #272e37; }
  .message-list li.blue-dot .col-mail-1 .dot {
    border-color: #5d6dc3; }
  .message-list li.orange-dot .col-mail-1 .dot {
    border-color: #f9bc0b; }
  .message-list li.green-dot .col-mail-1 .dot {
    border-color: #3ec396; }
  .message-list .checkbox-wrapper-mail {
    cursor: pointer;
    height: 20px;
    width: 20px;
    position: relative;
    display: inline-block;
    -webkit-box-shadow: inset 0 0 0 1px #98a6ad;
            box-shadow: inset 0 0 0 1px #98a6ad;
    border-radius: 1px; }
    .message-list .checkbox-wrapper-mail input {
      opacity: 0;
      cursor: pointer; }
    .message-list .checkbox-wrapper-mail input:checked ~ label {
      opacity: 1; }
    .message-list .checkbox-wrapper-mail label {
      position: absolute;
      top: 3px;
      left: 3px;
      right: 3px;
      bottom: 3px;
      cursor: pointer;
      background: #98a6ad;
      opacity: 0;
      margin-bottom: 0 !important;
      -webkit-transition-duration: .05s;
              transition-duration: .05s; }
    .message-list .checkbox-wrapper-mail label:active {
      background: #87949b; }

.mail-list a {
  font-family: "Roboto", sans-serif;
  vertical-align: middle;
  color: #6c757d;
  padding: 10px 12px;
  display: block; }

@media (max-width: 648px) {
  .inbox-leftbar {
    width: 100%; }
  .inbox-rightbar {
    margin-left: 0; } }

@media (max-width: 520px) {
  .message-list li .col-mail-1 {
    width: 150px; }
    .message-list li .col-mail-1 .title {
      left: 80px; }
  .message-list li .col-mail-2 {
    left: 160px; }
    .message-list li .col-mail-2 .date {
      text-align: right;
      padding-right: 10px;
      padding-left: 20px; } }

/* =============
   Timeline
============= */
.timeline {
  border-collapse: collapse;
  border-spacing: 0;
  display: table;
  margin-bottom: 50px;
  position: relative;
  table-layout: fixed;
  width: 100%; }
  .timeline .time-show {
    margin-bottom: 30px;
    margin-right: -75px;
    margin-top: 30px;
    position: relative; }
    .timeline .time-show a {
      color: #fff; }
  .timeline:before {
    background-color: rgba(65, 77, 95, 0.3);
    bottom: 0;
    content: "";
    left: 50%;
    position: absolute;
    top: 30px;
    width: 2px;
    z-index: 0; }
  .timeline .timeline-icon {
    -webkit-border-radius: 50%;
    background: #414d5f;
    border-radius: 50%;
    color: #fff;
    display: block;
    height: 20px;
    left: -54px;
    margin-top: -10px;
    position: absolute;
    text-align: center;
    top: 50%;
    width: 20px; }
    .timeline .timeline-icon i {
      color: #fff;
      font-size: 13px;
      margin-top: 1px;
      position: absolute;
      left: 4px; }
  .timeline .time-icon:before {
    font-size: 16px;
    margin-top: 5px; }

h3.timeline-title {
  color: #414d5f;
  font-size: 20px;
  font-weight: 400;
  margin: 0 0 5px;
  text-transform: uppercase; }

.timeline-item {
  display: table-row; }
  .timeline-item:before {
    content: "";
    display: block;
    width: 50%; }
  .timeline-item .timeline-desk .arrow {
    border-bottom: 12px solid transparent;
    border-right: 12px solid rgba(247, 247, 247, 0.3) !important;
    border-top: 12px solid transparent;
    display: block;
    height: 0;
    left: -12px;
    margin-top: -12px;
    position: absolute;
    top: 50%;
    width: 0; }
  .timeline-item .timeline-desk .timeline-box {
    padding: 20px; }
  .timeline-item .timeline-date {
    margin-bottom: 10px; }

.timeline-item.alt:after {
  content: "";
  display: block;
  width: 50%; }

.timeline-item.alt .timeline-desk .arrow-alt {
  border-bottom: 12px solid transparent;
  border-left: 12px solid rgba(247, 247, 247, 0.9) !important;
  border-top: 12px solid transparent;
  display: block;
  height: 0;
  left: auto;
  margin-top: -12px;
  position: absolute;
  right: -12px;
  top: 50%;
  width: 0; }

.timeline-item.alt .timeline-desk .album {
  float: right;
  margin-top: 20px; }
  .timeline-item.alt .timeline-desk .album a {
    float: right;
    margin-left: 5px; }

.timeline-item.alt .timeline-icon {
  left: auto;
  right: -56px; }

.timeline-item.alt:before {
  display: none; }

.timeline-item.alt .panel {
  margin-left: 0;
  margin-right: 45px; }

.timeline-item.alt h4 {
  text-align: right; }

.timeline-item.alt p {
  text-align: right; }

.timeline-item.alt .timeline-date {
  text-align: right; }

.timeline-desk {
  display: table-cell;
  vertical-align: top;
  width: 50%; }
  .timeline-desk h4 {
    font-size: 16px;
    margin: 0; }
  .timeline-desk .panel {
    background: rgba(247, 247, 247, 0.9);
    display: block;
    margin-bottom: 5px;
    margin-left: 45px;
    position: relative;
    text-align: left;
    border: 0; }
  .timeline-desk h5 span {
    color: #414d5f;
    display: block;
    font-size: 12px;
    margin-bottom: 4px; }
  .timeline-desk p {
    color: #999999;
    font-size: 14px;
    margin-bottom: 0; }
  .timeline-desk .album {
    margin-top: 12px; }
    .timeline-desk .album a {
      float: left;
      margin-right: 5px; }
    .timeline-desk .album img {
      height: 36px;
      width: auto;
      border-radius: 3px; }
  .timeline-desk .notification {
    background: none repeat scroll 0 0 #fff;
    margin-top: 20px;
    padding: 8px; }

/* =============
   Account Pages
============= */
.home-wrapper {
  margin: 10% 0; }

.bg-accpunt-pages {
  background-color: #5d6dc3;
  background: -webkit-gradient(linear, left bottom, left top, from(#5d6dc3), to(#3c86d8));
  background: linear-gradient(to top, #5d6dc3, #3c86d8);
  padding-bottom: 0;
  min-height: 100px; }

.wrapper-page {
  display: table;
  height: 100vh;
  width: 100%; }

.account-box {
  position: relative;
  max-width: 460px;
  margin: 20px auto;
  background-color: #fff;
  border-radius: 5px; }
  .account-box .account-content {
    padding: 30px; }
  .account-box .account-btn {
    position: absolute;
    left: 0;
    right: 0; }

.account-logo-box {
  padding: 30px 30px 0 30px; }

.text-error {
  color: #5d6dc3;
  text-shadow: rgba(93, 109, 195, 0.3) 5px 1px, rgba(93, 109, 195, 0.2) 10px 3px;
  font-size: 84px;
  font-weight: 700;
  line-height: 90px; }

.checkmark {
  width: 100px;
  margin: 0 auto;
  padding: 20px 0; }

.path {
  stroke-dasharray: 1000;
  stroke-dashoffset: 0;
  animation: dash 2s ease-in-out;
  -webkit-animation: dash 2s ease-in-out; }

.spin {
  animation: spin 2s;
  -webkit-animation: spin 2s;
  transform-origin: 50% 50%;
  -webkit-transform-origin: 50% 50%; }

@-webkit-keyframes dash {
  0% {
    stroke-dashoffset: 1000; }
  100% {
    stroke-dashoffset: 0; } }

@keyframes dash {
  0% {
    stroke-dashoffset: 1000; }
  100% {
    stroke-dashoffset: 0; } }

@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg); }
  100% {
    -webkit-transform: rotate(360deg); } }

@keyframes spin {
  0% {
    -webkit-transform: rotate(0deg); }
  100% {
    -webkit-transform: rotate(360deg); } }

@-webkit-keyframes text {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }

@keyframes text {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }

/* =============
   Pricing
============= */
.pricing-column .ribbon-pricing {
  width: 160px;
  margin: -15px auto -10px;
  padding-bottom: 2px;
  line-height: 22px;
  text-align: center;
  z-index: 1;
  position: relative; }

.pricing-column .plan-title {
  font-family: "Roboto", sans-serif;
  letter-spacing: 1px; }

.pricing-column .plan-price {
  font-size: 48px;
  font-family: "Roboto", sans-serif; }

.pricing-column .plan-duration {
  font-size: 15px;
  color: rgba(255, 255, 255, 0.7); }

.pricing-column .plan-stats {
  padding: 30px 20px 15px; }
  .pricing-column .plan-stats li {
    margin-bottom: 15px;
    line-height: 24px; }
