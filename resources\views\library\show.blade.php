@extends('layouts.master')

@section('content')
    <div class="row">
        <div class="col-lg-12">
            <div class="card-box">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="header-title">{{ $library->title }}</h4>
                    <div>
                        <a href="{{ route('library.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left mr-1"></i> Back to Library
                        </a>
                        <a href="{{ route('library.edit', $library->id) }}" class="btn btn-warning">
                            <i class="fas fa-edit mr-1"></i> Edit
                        </a>
                    </div>
                </div>

                <div class="row">
                    <!-- Left column with thumbnail and metadata -->
                    <div class="col-md-4">
                        <div class="text-center mb-3">
                            <img src="{{ $library->getThumbnailUrl() }}" alt="{{ $library->title }}"
                                class="img-fluid rounded" style="max-height: 300px;">
                        </div>

                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Item Details</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm">
                                    <tbody>
                                        <tr>
                                            <th>Media Type:</th>
                                            <td><span class="badge badge-primary">{{ ucfirst($library->media_type) }}</span>
                                            </td>
                                        </tr>
                                        @if ($library->author)
                                            <tr>
                                                <th>Author/Creator:</th>
                                                <td>{{ $library->author }}</td>
                                            </tr>
                                        @endif
                                        @if ($library->category)
                                            <tr>
                                                <th>Category:</th>
                                                <td>{{ $library->category }}</td>
                                            </tr>
                                        @endif
                                        @if ($library->language)
                                            <tr>
                                                <th>Language:</th>
                                                <td>{{ $library->language }}</td>
                                            </tr>
                                        @endif
                                        @if ($library->duration)
                                            <tr>
                                                <th>Duration:</th>
                                                <td>{{ $library->duration }}</td>
                                            </tr>
                                        @endif
                                        @if ($library->file_size)
                                            <tr>
                                                <th>File Size:</th>
                                                <td>{{ $library->getFormattedFileSizeAttribute() }}</td>
                                            </tr>
                                        @endif
                                        <tr>
                                            <th>Status:</th>
                                            <td>
                                                @if ($library->isactive)
                                                    <span class="badge badge-success">Active</span>
                                                @else
                                                    <span class="badge badge-danger">Inactive</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>Added By:</th>
                                            <td>{{ $library->created_by }}</td>
                                        </tr>
                                        <tr>
                                            <th>Added On:</th>
                                            <td>{{ $library->created_at->format('M d, Y') }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Right column with content preview and description -->
                    <div class="col-md-8">
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Description</h5>
                            </div>
                            <div class="card-body">
                                <p>{{ $library->description }}</p>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Content Preview</h5>
                            </div>
                            <div class="card-body">
                                @if ($library->media_type == 'pdf' && $library->file_path)
                                    <div class="embed-responsive embed-responsive-16by9 mb-3">
                                        <iframe class="embed-responsive-item"
                                            src="{{ asset('storage/' . $library->file_path) }}"></iframe>
                                    </div>
                                    <div class="text-center">
                                        <a href="{{ asset('storage/' . $library->file_path) }}"
                                            class="btn btn-primary" target="_blank">
                                            <i class="fas fa-external-link-alt mr-1"></i> Open in Full Screen
                                        </a>
                                        <a href="{{ asset('storage/' . $library->file_path) }}"
                                            class="btn btn-success" download>
                                            <i class="fas fa-download mr-1"></i> Download PDF
                                        </a>
                                    </div>
                                @elseif ($library->media_type == 'video')
                                    @if (
                                        $library->external_url &&
                                            (strpos($library->external_url, 'youtube.com') !== false ||
                                                strpos($library->external_url, 'youtu.be') !== false))
                                        <div class="embed-responsive embed-responsive-16by9">
                                            <iframe class="embed-responsive-item"
                                                src="{{ str_replace('watch?v=', 'embed/', $library->external_url) }}"
                                                allowfullscreen></iframe>
                                        </div>
                                    @elseif ($library->file_path)
                                        <video controls class="w-100">
                                            <source src="{{ asset('storage/' . $library->file_path) }}" type="video/mp4">
                                            Your browser does not support the video tag.
                                        </video>
                                        <div class="mt-2 text-center">
                                            <a href="{{ asset('storage/' . $library->file_path) }}" class="btn btn-success"
                                                download>
                                                <i class="fas fa-download mr-1"></i> Download Video
                                            </a>
                                        </div>
                                    @else
                                        <div class="alert alert-warning">No video file or external URL provided.</div>
                                    @endif
                                @elseif ($library->media_type == 'audio')
                                    @if ($library->file_path)
                                        <audio controls class="w-100 mb-3">
                                            <source src="{{ asset('storage/' . $library->file_path) }}" type="audio/mpeg">
                                            Your browser does not support the audio element.
                                        </audio>
                                        <div class="text-center">
                                            <a href="{{ asset('storage/' . $library->file_path) }}" class="btn btn-success"
                                                download>
                                                <i class="fas fa-download mr-1"></i> Download Audio
                                            </a>
                                        </div>
                                    @elseif ($library->external_url)
                                        <div class="alert alert-info">External audio URL: <a
                                                href="{{ $library->external_url }}"
                                                target="_blank">{{ $library->external_url }}</a></div>
                                    @else
                                        <div class="alert alert-warning">No audio file or external URL provided.</div>
                                    @endif
                                @elseif ($library->media_type == 'other' && $library->file_path)
                                    <div class="text-center py-4">
                                        <i class="fas fa-file fa-4x text-primary mb-3"></i>
                                        <h5>
                                            This file type cannot be previewed directly.
                                        </h5>
                                        <a href="{{ asset('storage/' . $library->file_path) }}" class="btn btn-success"
                                            download>
                                            <i class="fas fa-download mr-1"></i> Download File
                                        </a>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
